<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.news.dao.NewsInformationDao">
    <resultMap type="com.lanshan.app.news.entity.NewsInformation" id="NewsInformationMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="srcUrl" column="src_url" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="typeId" column="type_id" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP"/>
        <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
        <result property="favoriteCount" column="favorite_count" jdbcType="INTEGER"/>
        <result property="likeCount" column="like_count" jdbcType="INTEGER"/>
        <result property="hasImage" column="has_image" jdbcType="BOOLEAN"/>
        <result property="hasFile" column="has_file" jdbcType="BOOLEAN"/>
        <result property="thumbImage" column="thumb_image" jdbcType="VARCHAR"/>
        <result property="isTop" column="is_top" jdbcType="BOOLEAN"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into news.news_information(src_url, title, summary, content, type_id, created_time, publish_time,
        view_count, favorite_count, like_count, has_image, has_file, thumb_image, is_top, sort, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.srcUrl} , #{entity.title} , #{entity.summary} , #{entity.content} , #{entity.typeId} ,
            #{entity.createdTime} , #{entity.publishTime} , #{entity.viewCount} , #{entity.favoriteCount} ,
            #{entity.likeCount} , #{entity.hasImage} , #{entity.hasFile} , #{entity.thumbImage} , #{entity.isTop} ,
            #{entity.sort} , #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into news.news_information(src_url, title, summary, content, type_id, created_time, publish_time,
        view_count, favorite_count, like_count, has_image, has_file, thumb_image, is_top, sort, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.srcUrl}, #{entity.title}, #{entity.summary}, #{entity.content}, #{entity.typeId},
            #{entity.createdTime}, #{entity.publishTime}, #{entity.viewCount}, #{entity.favoriteCount},
            #{entity.likeCount}, #{entity.hasImage}, #{entity.hasFile}, #{entity.thumbImage}, #{entity.isTop},
            #{entity.sort}, #{entity.status})
        </foreach>
        ON CONFLICT(id) DO update set
        src_url = EXCLUDED.src_url , title = EXCLUDED.title , summary = EXCLUDED.summary , content = EXCLUDED.content ,
        type_id = EXCLUDED.type_id , created_time = EXCLUDED.created_time , publish_time = EXCLUDED.publish_time ,
        view_count = EXCLUDED.view_count , favorite_count = EXCLUDED.favorite_count , like_count = EXCLUDED.like_count ,
        has_image = EXCLUDED.has_image , has_file = EXCLUDED.has_file , thumb_image = EXCLUDED.thumb_image , is_top =
        EXCLUDED.is_top , sort = EXCLUDED.sort , status = EXCLUDED.status
    </insert>
</mapper>

