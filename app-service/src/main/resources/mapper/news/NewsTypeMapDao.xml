<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.news.dao.NewsTypeMapDao">
    <resultMap type="com.lanshan.app.news.entity.NewsTypeMap" id="NewsTypeMapMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="typeId" column="type_id" jdbcType="INTEGER"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="srcCategory" column="src_category" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into news.news_type_map(type_id, type_name, src_category, create_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.typeId} , #{entity.typeName} , #{entity.srcCategory} , #{entity.createTime} , #{entity.isDeleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into news.news_type_map(type_id, type_name, src_category, create_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.typeId}, #{entity.typeName}, #{entity.srcCategory}, #{entity.createTime}, #{entity.isDeleted})
        </foreach>
        ON CONFLICT(id) DO update set
        type_id = EXCLUDED.type_id , type_name = EXCLUDED.type_name , src_category = EXCLUDED.src_category , create_time
        = EXCLUDED.create_time , is_deleted = EXCLUDED.is_deleted
    </insert>
</mapper>

