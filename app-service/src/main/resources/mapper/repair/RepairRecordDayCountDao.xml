<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.repair.dao.RepairRecordDayCountDao">
    <resultMap type="com.lanshan.app.repair.entity.RepairRecordDayCount" id="RepairRecordDayCountMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="buildingId" column="building_id" jdbcType="INTEGER"/>
        <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="count" column="count" jdbcType="INTEGER"/>
        <result property="recordDate" column="record_date" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_record_day_count(building_id, category_id, userid, count, record_date, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.buildingId}, #{entity.categoryId}, #{entity.userid}, #{entity.count}, #{entity.recordDate},
             #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_record_day_count(building_id, category_id, userid, count, record_date, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.buildingId}, #{entity.categoryId}, #{entity.userid}, #{entity.count}, #{entity.recordDate},
             #{entity.createTime})
        </foreach>
        ON CONFLICT(id) DO update set building_id = EXCLUDED.building_id,
                                      category_id = EXCLUDED.category_id,
                                      userid      = EXCLUDED.userid,
                                      count       = EXCLUDED.count,
                                      record_date = EXCLUDED.record_date,
                                      create_time = EXCLUDED.create_time
    </insert>
</mapper>

