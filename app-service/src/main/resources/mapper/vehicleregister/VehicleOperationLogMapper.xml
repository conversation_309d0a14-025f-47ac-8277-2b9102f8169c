<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.vehicleregister.dao.VehicleOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.vehicleregister.po.VehicleOperationLog">
                <id column="id" property="id" />
                <result column="title" property="title" />
                <result column="business_type" property="businessType" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="user_type" property="userType" />
                <result column="request_url" property="requestUrl" />
                <result column="request_method" property="requestMethod" />
                <result column="request_params" property="requestParams" />
                <result column="client_ip" property="clientIp" />
                <result column="status" property="status" />
                <result column="create_time" property="createTime" />
                <result column="request_result" property="requestResult" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                title,
                business_type,
                user_id,
                user_name,
                user_type,
                request_url,
                request_method,
                request_params,
                client_ip,
                status,
                create_time,
                request_result
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.vehicle_operation_log (
            id,
            title,
            business_type,
            user_id,
            user_name,
            user_type,
            request_url,
            request_method,
            request_params,
            client_ip,
            status,
            create_time,
            request_result
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.title},
                #{entity.businessType},
                #{entity.userId},
                #{entity.userName},
                #{entity.userType},
                #{entity.requestUrl},
                #{entity.requestMethod},
                #{entity.requestParams},
                #{entity.clientIp},
                #{entity.status},
                #{entity.createTime},
                #{entity.requestResult}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.vehicle_operation_log (
            id,
            title,
            business_type,
            user_id,
            user_name,
            user_type,
            request_url,
            request_method,
            request_params,
            client_ip,
            status,
            create_time,
            request_result
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.title},
                #{entity.businessType},
                #{entity.userId},
                #{entity.userName},
                #{entity.userType},
                #{entity.requestUrl},
                #{entity.requestMethod},
                #{entity.requestParams},
                #{entity.clientIp},
                #{entity.status},
                #{entity.createTime},
                #{entity.requestResult}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                title = EXCLUDED.title,
                business_type = EXCLUDED.business_type,
                user_id = EXCLUDED.user_id,
                user_name = EXCLUDED.user_name,
                user_type = EXCLUDED.user_type,
                request_url = EXCLUDED.request_url,
                request_method = EXCLUDED.request_method,
                request_params = EXCLUDED.request_params,
                client_ip = EXCLUDED.client_ip,
                status = EXCLUDED.status,
                create_time = EXCLUDED.create_time,
                request_result = EXCLUDED.request_result
    </insert>


</mapper>
