<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.vehicleregister.dao.VehicleRegisterRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.vehicleregister.po.VehicleRegisterRecord">
                <id column="id" property="id" />
                <result column="user_type" property="userType" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="phone" property="phone" />
                <result column="user_department" property="userDepartment" />
                <result column="license_plate" property="licensePlate" />
                <result column="driving_license" property="drivingLicense"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
                <result column="vehicle_photos" property="vehiclePhotos"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
                <result column="sign" property="sign" />
                <result column="audit_status" property="auditStatus" />
                <result column="audit_id" property="auditId" />
                <result column="audit_name" property="auditName" />
                <result column="reject_reason" property="rejectReason" />
                <result column="create_time" property="createTime" />
                <result column="update_time" property="updateTime" />
        <result column="violation_count" property="violationCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_type,
                user_id,
                user_name,
                phone,
                user_department,
                license_plate,
                driving_license,
                vehicle_photos,
                sign,
                audit_status,
                audit_id,
                audit_name,
                reject_reason,
                create_time,
                update_time,
                violation_count
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.vehicle_register_record (
            id,
            user_type,
            user_id,
            user_name,
            phone,
            user_department,
            license_plate,
            driving_license,
            vehicle_photos,
            sign,
            audit_status,
            audit_id,
            audit_name,
            reject_reason,
            create_time,
            update_time,
        violation_count
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userType},
                #{entity.userId},
                #{entity.userName},
                #{entity.phone},
                #{entity.userDepartment},
                #{entity.licensePlate},
                #{entity.drivingLicense},
                #{entity.vehiclePhotos},
                #{entity.sign},
                #{entity.auditStatus},
                #{entity.auditId},
                #{entity.auditName},
                #{entity.rejectReason},
                #{entity.createTime},
                #{entity.updateTime},
                #{entity.violationCount}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.vehicle_register_record (
            id,
            user_type,
            user_id,
            user_name,
            phone,
            user_department,
            license_plate,
            driving_license,
            vehicle_photos,
            sign,
            audit_status,
            audit_id,
            audit_name,
            reject_reason,
            create_time,
            update_time,
        violation_count
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userType},
                #{entity.userId},
                #{entity.userName},
                #{entity.phone},
                #{entity.userDepartment},
                #{entity.licensePlate},
                #{entity.drivingLicense},
                #{entity.vehiclePhotos},
                #{entity.sign},
                #{entity.auditStatus},
                #{entity.auditId},
                #{entity.auditName},
                #{entity.rejectReason},
                #{entity.createTime},
                #{entity.updateTime},
                #{entity.violationCount}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_type = EXCLUDED.user_type,
                user_id = EXCLUDED.user_id,
                user_name = EXCLUDED.user_name,
                phone = EXCLUDED.phone,
                user_department = EXCLUDED.user_department,
                license_plate = EXCLUDED.license_plate,
                driving_license = EXCLUDED.driving_license,
                vehicle_photos = EXCLUDED.vehicle_photos,
                sign = EXCLUDED.sign,
                audit_status = EXCLUDED.audit_status,
                audit_id = EXCLUDED.audit_id,
                audit_name = EXCLUDED.audit_name,
                reject_reason = EXCLUDED.reject_reason,
                create_time = EXCLUDED.create_time,
                update_time = EXCLUDED.update_time,
                violation_count = EXCLUDED.violation_count
    </insert>

    <update id="updateVehicleOffStatusBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.vehicle_register_record (
        id,
        audit_status,
        update_time
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.id},
             #{entity.auditStatus},
            #{entity.updateTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
        update_time = EXCLUDED.update_time,
        audit_status = EXCLUDED.audit_status
    </update>


</mapper>
