<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvFieldDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvField" id="RsvFieldMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="venueId" column="venue_id" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="limitCount" column="limit_count" jdbcType="INTEGER"/>
        <result property="isEnable" column="is_enable" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_field(name, type, venue_id, sort, is_deleted, create_by, create_time, update_by,
        update_time, limit_count, is_enable)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name} , #{entity.type} , #{entity.venueId} , #{entity.sort} , #{entity.isDeleted} ,
            #{entity.createBy} , #{entity.createTime} , #{entity.updateBy} , #{entity.updateTime} , #{entity.limitCount}
            , #{entity.isEnable})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_field(name, type, venue_id, sort, is_deleted, create_by, create_time, update_by,
        update_time, limit_count, is_enable)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type}, #{entity.venueId}, #{entity.sort}, #{entity.isDeleted}, #{entity.createBy},
            #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.limitCount}, #{entity.isEnable})
        </foreach>
        ON CONFLICT(id) DO update set
        name = EXCLUDED.name , type = EXCLUDED.type , venue_id = EXCLUDED.venue_id , sort = EXCLUDED.sort , is_deleted =
        EXCLUDED.is_deleted , create_by = EXCLUDED.create_by , create_time = EXCLUDED.create_time , update_by =
        EXCLUDED.update_by , update_time = EXCLUDED.update_time , limit_count = EXCLUDED.limit_count , is_enable =
        EXCLUDED.is_enable
    </insert>
</mapper>

