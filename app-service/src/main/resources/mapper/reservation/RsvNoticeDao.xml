<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvNoticeDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvNotice" id="RsvNoticeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_notice(title, content, is_deleted, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.title} , #{entity.content} , #{entity.isDeleted} , #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_notice(title, content, is_deleted, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.title}, #{entity.content}, #{entity.isDeleted}, #{entity.createTime})
        </foreach>
        ON CONFLICT(id) DO update set
        title = EXCLUDED.title , content = EXCLUDED.content , is_deleted = EXCLUDED.is_deleted , create_time =
        EXCLUDED.create_time
    </insert>
</mapper>

