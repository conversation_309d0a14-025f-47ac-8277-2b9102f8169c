<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvBlacklistDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvBlacklist" id="RsvBlacklistMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_blacklist(user_id, user_name, create_time, start_time, end_time, comment,
        is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId} , #{entity.userName} , #{entity.createTime} , #{entity.startTime} , #{entity.endTime} ,
            #{entity.comment} , #{entity.isDeleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_blacklist(user_id, user_name, create_time, start_time, end_time, comment,
        is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.createTime}, #{entity.startTime}, #{entity.endTime},
            #{entity.comment}, #{entity.isDeleted})
        </foreach>
        ON CONFLICT(id) DO update set
        user_id = EXCLUDED.user_id , user_name = EXCLUDED.user_name , create_time = EXCLUDED.create_time , start_time =
        EXCLUDED.start_time , end_time = EXCLUDED.end_time , comment = EXCLUDED.comment , is_deleted =
        EXCLUDED.is_deleted
    </insert>
</mapper>

