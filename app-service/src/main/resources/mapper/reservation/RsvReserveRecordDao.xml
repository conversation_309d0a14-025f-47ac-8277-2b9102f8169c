<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvReserveRecordDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvReserveRecord" id="RsvReserveRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="fieldId" column="field_id" jdbcType="INTEGER"/>
        <result property="fieldName" column="field_name" jdbcType="VARCHAR"/>
        <result property="venueName" column="venue_name" jdbcType="VARCHAR"/>
        <result property="useDate" column="use_date" jdbcType="TIMESTAMP"/>
        <result property="useTime" column="use_time" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="objectId" column="object_id" jdbcType="INTEGER"/>
        <result property="venueId" column="venue_id" jdbcType="INTEGER"/>
        <result property="reason" column="reason" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_reserve_record(user_id, user_name, field_id, field_name, venue_name, use_date,
        use_time, create_time, status, update_time, object_id, venue_id, reason)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId} , #{entity.userName} , #{entity.fieldId} , #{entity.fieldName} , #{entity.venueName} ,
            #{entity.useDate} , #{entity.useTime} , #{entity.createTime} , #{entity.status} , #{entity.updateTime} ,
            #{entity.objectId} , #{entity.venueId}, #{entity.reason})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_reserve_record(user_id, user_name, field_id, field_name, venue_name, use_date,
        use_time, create_time, status, update_time, object_id, venue_id, reason)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.fieldId}, #{entity.fieldName}, #{entity.venueName},
            #{entity.useDate}, #{entity.useTime}, #{entity.createTime}, #{entity.status}, #{entity.updateTime},
            #{entity.objectId}, #{entity.venueId}, #{entity.reason})
        </foreach>
        ON CONFLICT(id) DO update set
        user_id = EXCLUDED.user_id , user_name = EXCLUDED.user_name , field_id = EXCLUDED.field_id , field_name =
        EXCLUDED.field_name , venue_name = EXCLUDED.venue_name , use_date = EXCLUDED.use_date , use_time =
        EXCLUDED.use_time , create_time = EXCLUDED.create_time , status = EXCLUDED.status , update_time =
        EXCLUDED.update_time , object_id = EXCLUDED.object_id , venue_id = EXCLUDED.venue_id, reason = EXCLUDED.reason
    </insert>
</mapper>

