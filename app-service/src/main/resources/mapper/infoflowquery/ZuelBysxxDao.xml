<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.infoflowquery.dao.ZuelBysxxDao">

    <resultMap type="com.lanshan.app.infoflowquery.entity.ZuelBysxx" id="ZuelBysxxMap">
        <result property="wid" column="wid" jdbcType="VARCHAR"/>
        <result property="xh" column="xh" jdbcType="VARCHAR"/>
        <result property="yqzxf" column="yqzxf" jdbcType="VARCHAR"/>
        <result property="wczxf" column="wczxf" jdbcType="VARCHAR"/>
        <result property="xwkyqxf" column="xwkyqxf" jdbcType="VARCHAR"/>
        <result property="xwkwcxf" column="xwkwcxf" jdbcType="VARCHAR"/>
        <result property="bxhjyqxf" column="bxhjyqxf" jdbcType="VARCHAR"/>
        <result property="bxhjwcxf" column="bxhjwcxf" jdbcType="VARCHAR"/>
        <result property="shzt" column="shzt" jdbcType="VARCHAR"/>
        <result property="czz" column="czz" jdbcType="VARCHAR"/>
        <result property="cxxxm" column="cxxxm" jdbcType="VARCHAR"/>
        <result property="czrq" column="czrq" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.zuel_bysxx(wid, xh, yqzxf, wczxf, xwkyqxf, xwkwcxf, bxhjyqxf, bxhjwcxf, shzt, czz, cxxxm, czrq)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.wid} , #{entity.xh} , #{entity.yqzxf} , #{entity.wczxf} , #{entity.xwkyqxf} , #{entity.xwkwcxf} , #{entity.bxhjyqxf} , #{entity.bxhjwcxf} , #{entity.shzt} , #{entity.czz} , #{entity.cxxxm} , #{entity.czrq})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.zuel_bysxx(wid, xh, yqzxf, wczxf, xwkyqxf, xwkwcxf, bxhjyqxf, bxhjwcxf, shzt, czz, cxxxm, czrq)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.wid}, #{entity.xh}, #{entity.yqzxf}, #{entity.wczxf}, #{entity.xwkyqxf}, #{entity.xwkwcxf}, #{entity.bxhjyqxf}, #{entity.bxhjwcxf}, #{entity.shzt}, #{entity.czz}, #{entity.cxxxm}, #{entity.czrq})
        </foreach>
        ON CONFLICT(id) DO update set
wid = EXCLUDED.wid , xh = EXCLUDED.xh , yqzxf = EXCLUDED.yqzxf , wczxf = EXCLUDED.wczxf , xwkyqxf = EXCLUDED.xwkyqxf , xwkwcxf = EXCLUDED.xwkwcxf , bxhjyqxf = EXCLUDED.bxhjyqxf , bxhjwcxf = EXCLUDED.bxhjwcxf , shzt = EXCLUDED.shzt , czz = EXCLUDED.czz , cxxxm = EXCLUDED.cxxxm , czrq = EXCLUDED.czrq     </insert>

    <select id="getStuInfoByUserId" resultType="com.lanshan.app.infoflowquery.vo.StuInfoVO">
        select user_id      AS userId,
               name,
               college_name AS collegeName
        from standard_app.std_student_info
        where user_id = #{userId}
    </select>
</mapper>

