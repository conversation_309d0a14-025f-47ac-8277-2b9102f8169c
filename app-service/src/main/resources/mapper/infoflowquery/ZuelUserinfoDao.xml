<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.infoflowquery.dao.ZuelUserinfoDao">

    <resultMap type="com.lanshan.app.infoflowquery.entity.ZuelUserinfo" id="ZuelUserinfoMap">
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="college" column="college" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.zuel_userinfo(userid, username, college)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.userid} , #{entity.username} , #{entity.college})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.zuel_userinfo(userid, username, college)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userid}, #{entity.username}, #{entity.college})
        </foreach>
        ON CONFLICT(id) DO update set
userid = EXCLUDED.userid , username = EXCLUDED.username , college = EXCLUDED.college     </insert>

</mapper>

