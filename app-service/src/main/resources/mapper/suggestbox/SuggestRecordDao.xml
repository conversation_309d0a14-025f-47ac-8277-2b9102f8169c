<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.suggestbox.dao.SuggestRecordDao">
    <resultMap type="com.lanshan.app.suggestbox.entity.SuggestRecord" id="SuggestRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="describe" column="describe" jdbcType="VARCHAR"/>
        <result property="picList" column="pic_list"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="categoryType" column="category_type" jdbcType="INTEGER"/>
        <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="reply" column="reply" jdbcType="VARCHAR"/>
        <result property="replyUserid" column="reply_userid" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="replyDate" column="reply_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_record(describe, pic_list, category_type, category_id, mobile, reply,
                                               reply_userid,
                                               creator,
                                               create_date, updater, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.describe}, #{entity.picList}, #{entity.categoryType}, #{entity.categoryId}, #{entity.mobile},
             #{entity.reply}, #{entity.replyUserid}, #{entity.creator}, #{entity.createDate}, #{entity.updater},
             #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_record(describe, pic_list, category_type, category_id, mobile, reply,
                                               reply_userid,
                                               creator,
                                               create_date, updater, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.describe}, #{entity.picList}, #{entity.categoryType}, #{entity.categoryId}, #{entity.mobile},
             #{entity.reply}, #{entity.creator}, #{entity.createDate}, #{entity.updater}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set describe      = EXCLUDED.describe,
                                      pic_list      = EXCLUDED.pic_list,
                                      category_type = EXCLUDED.category_type,
                                      category_id = EXCLUDED.category_id,
                                      mobile        = EXCLUDED.mobile,
                                      reply         = EXCLUDED.reply,
                                      creator       = EXCLUDED.creator,
                                      create_date   = EXCLUDED.create_date,
                                      updater       = EXCLUDED.updater,
                                      update_date   = EXCLUDED.update_date
    </insert>

    <select id="pageByParam" resultMap="SuggestRecordMap">
        SELECT *
        FROM suggest_box.suggest_record
        <where>
            <if test="qo.name != null and qo.name != ''">
                AND creator IN (SELECT userid FROM addressbook.cp_user WHERE name LIKE '%' || #{qo.name} || '%')
            </if>
            <if test="qo.userid != null and qo.userid != ''">
                AND creator LIKE '%' || #{qo.userid} || '%'
            </if>
            <if test="qo.mobile != null and qo.mobile != ''">
                AND mobile LIKE '%' || #{qo.mobile} || '%'
            </if>
            <if test="qo.deptName != null and qo.deptName != ''">
                AND creator IN
                    (SELECT userid
                     FROM addressbook.cp_user
                     WHERE main_department IN
                           (SELECT id FROM addressbook.cp_department WHERE path LIKE '%' || #{qo.deptName} || '%'))
            </if>
            <if test="qo.replyStatus != null and qo.replyStatus == '0'.toString()">
                AND (reply IS NULL OR trim(reply) = '')
            </if>
            <if test="qo.replyStatus != null and qo.replyStatus == '1'.toString()">
                AND (reply IS NOT NULL AND trim(reply) != '')
            </if>
            <if test="qo.typeName != null and qo.typeName != ''">
                <choose>
                    <when test="qo.type != null and qo.type == '1'.toString()">
                        AND category_id IN
                            (SELECT id FROM suggest_box.suggest_category WHERE name LIKE '%' || #{qo.typeName} || '%')
                    </when>
                    <when test="qo.type != null and qo.type == '2'.toString()">
                        AND category_id IN
                            (SELECT DISTINCT agent_id
                             FROM workbench.wb_app
                             WHERE app_name LIKE '%' || #{qo.typeName} || '%')
                    </when>
                    <otherwise>
                        AND category_id IN
                            ((SELECT id
                              FROM suggest_box.suggest_category
                              WHERE type = '1'
                                AND name LIKE '%' || #{qo.typeName} || '%')
                             UNION ALL
                             (SELECT DISTINCT agent_id
                              FROM workbench.wb_app
                              WHERE app_name LIKE '%' || #{qo.typeName} || '%'))
                    </otherwise>
                </choose>
            </if>
            <if test="qo.replyUserid != null and qo.replyUserid != ''">
                AND category_id IN (SELECT id
                                    FROM suggest_box.suggest_category
                                    WHERE manager_userids @> jsonb_build_array(#{qo.replyUserid}::text))
            </if>
            <if test="qo.desc != null and qo.desc != ''">
                AND describe like '%' || #{qo.desc} || '%'
            </if>
            <if test="qo.categoryId != null">
                AND category_id = #{qo.categoryId}
            </if>
            <if test="qo.agentId != null and qo.agentId != ''">
                AND category_id IN (SELECT id FROM suggest_box.suggest_category WHERE type = 2 AND name = #{qo.agentId})
            </if>
            <if test="qo.type != null and qo.type != ''">
                AND category_type = #{qo.type}
            </if>
        </where>
        ORDER BY create_date DESC
    </select>
</mapper>

