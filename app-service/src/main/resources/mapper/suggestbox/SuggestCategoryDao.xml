<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.suggestbox.dao.SuggestCategoryDao">
    <resultMap type="com.lanshan.app.suggestbox.entity.SuggestCategory" id="SuggestCategoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="managerUserids" column="manager_userids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="enable" column="enable" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_category(type, name, sort, manager_userids, creator, create_date, updater,
                                                 update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.type}, #{entity.name}, #{entity.sort}, #{entity.managerUserids}, #{entity.creator},
             #{entity.createDate}, #{entity.updater}, #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_category(type, name, sort, manager_userids, creator, create_date, updater,
                                                 update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.type}, #{entity.name}, #{entity.sort}, #{entity.managerUserids}, #{entity.creator},
             #{entity.createDate}, #{entity.updater}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set type            = EXCLUDED.type,
                                      name            = EXCLUDED.name,
                                      sort = EXCLUDED.sort,
                                      manager_userids = EXCLUDED.manager_userids,
                                      creator         = EXCLUDED.creator,
                                      create_date     = EXCLUDED.create_date,
                                      updater         = EXCLUDED.updater,
                                      update_date     = EXCLUDED.update_date
    </insert>

    <select id="pageTransactionByParam" resultMap="SuggestCategoryMap">
        SELECT *
        FROM suggest_box.suggest_category
        WHERE type = 1
        <if test="qo.name != null and qo.name != ''">
            AND name LIKE '%' || #{qo.name} || '%'
        </if>
        <if test="qo.managerName != null and qo.managerName != ''">
            AND EXISTS (SELECT 1
                        FROM jsonb_array_elements_text(manager_userids) AS j(userid)
                        WHERE j.userid IN (SELECT userid
                                           FROM addressbook.cp_user
                                           WHERE cp_user.name LIKE '%' || #{qo.managerName} || '%'))
        </if>
        <if test="qo.managerUserid != null and qo.managerUserid != ''">
            AND EXISTS (SELECT 1
                        FROM jsonb_array_elements_text(manager_userids) AS j(userid)
                        WHERE j.userid IN (SELECT userid
                                           FROM addressbook.cp_user cu
                                           WHERE cu.userid LIKE '%' || #{qo.managerUserid} || '%'))
        </if>
        ORDER BY sort, create_date DESC
    </select>

    <select id="pageApplicationByParam" resultMap="SuggestCategoryMap">
        SELECT *
        FROM suggest_box.suggest_category
        WHERE type = 2
        <if test="qo.name != null and qo.name != ''">
            AND name IN (SELECT agent_id FROM workbench.wb_app WHERE app_name LIKE CONCAT('%', #{qo.name}))
        </if>
        <if test="qo.managerName != null and qo.managerName != ''">
            AND EXISTS (SELECT 1
                        FROM jsonb_array_elements_text(manager_userids) AS j(userid)
                        WHERE j.userid IN (SELECT userid
                                           FROM addressbook.cp_user
                                           WHERE cp_user.name LIKE '%' || #{qo.managerName} || '%'))
        </if>
        <if test="qo.managerUserid != null and qo.managerUserid != ''">
            AND EXISTS (SELECT 1
                        FROM jsonb_array_elements_text(manager_userids) AS j(userid)
                        WHERE j.userid IN (SELECT userid
                                           FROM addressbook.cp_user cu
                                           WHERE cu.userid LIKE '%' || #{qo.managerUserid} || '%'))
        </if>
    </select>
</mapper>

