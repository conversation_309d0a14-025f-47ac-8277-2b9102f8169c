<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.suggestbox.dao.SuggestReplyLogDao">

    <resultMap type="com.lanshan.app.suggestbox.entity.SuggestReplyLog" id="SuggestReplyLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="recordId" column="record_id" jdbcType="INTEGER"/>
        <result property="replyId" column="reply_id" jdbcType="INTEGER"/>
        <result property="replyFromUserid" column="reply_from_userid" jdbcType="VARCHAR"/>
        <result property="replyToUserid" column="reply_to_userid" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_reply_log(record_id, reply_id, reply_from_userid, reply_to_userid, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.recordId} , #{entity.replyId} , #{entity.replyFromUserid} , #{entity.replyToUserid} , #{entity.createDate} , #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into suggest_box.suggest_reply_log(record_id, reply_id, reply_from_userid, reply_to_userid, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordId}, #{entity.replyId}, #{entity.replyFromUserid}, #{entity.replyToUserid}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set
record_id = EXCLUDED.record_id , reply_id = EXCLUDED.reply_id , reply_from_userid = EXCLUDED.reply_from_userid , reply_to_userid = EXCLUDED.reply_to_userid , create_date = EXCLUDED.create_date , update_date = EXCLUDED.update_date     </insert>

</mapper>

