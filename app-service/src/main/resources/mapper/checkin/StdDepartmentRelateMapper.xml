<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.checkin.dao.StdDepartmentRelateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.checkin.po.StdDepartmentRelate">
                <result column="id" property="id" />
                <result column="level" property="level" />
                <result column="name" property="name" />
                <result column="name_path" property="namePath" />
                <result column="id_path" property="idPath" />
                <result column="dept_id" property="deptId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                level,
                name,
                name_path,
                id_path,
                dept_id
    </sql>

    <select id="getAllCollege" resultType="com.lanshan.app.checkin.vo.StdDepartmentVO">
        select
            DISTINCT(s.dept_id) as id,
                    s.name,
                    d."path" as namePath,
                    d.id_path,
                    d."order",
                    d.user_count,
                    d.user_active_count
        from standard_app.std_department_relate s
                 inner join addressbook.cp_department d on d.id = s.dept_id
        where s.level = 1
    </select>

    <select id="getGradeByCollegeId" resultType="com.lanshan.app.checkin.vo.StdDepartmentVO">
        select
            s.dept_id as id,s.name,d."path" as namePath,
            d.id_path,d."order",d.user_count,d.user_active_count,
            split_part(s.id_path, '/', 1) as collegeId,
            split_part(s.name_path, '/', 1) as collegeName
        from standard_app.std_department_relate s
                 inner join addressbook.cp_department d on d.id = s.dept_id
        where s.level = 2 and s.id_path like '%' || #{collegeId} || '%'
    </select>

    <select id="getAllGrade" resultType="com.lanshan.app.checkin.vo.StdDepartmentVO">
        SELECT
            s.dept_id AS id,
            s.name,
            s.name_path,
            split_part(s.id_path, '/', 1) as collegeId,
            split_part(s.name_path, '/', 1) as collegeName,
            s.id_path,
            d."order",
            d.user_count,
            d.user_active_count
        FROM
            standard_app.std_department_relate s
                INNER JOIN addressbook.cp_department d ON d.id = s.dept_id
        WHERE
            s.LEVEL = 2
    </select>
    <select id="getGradeById" resultType="com.lanshan.app.checkin.vo.StdDepartmentVO">
        SELECT
            s.dept_id AS id,
            s.name,
            s.name_path,
            split_part(s.id_path, '/', 1) as collegeId,
            split_part(s.name_path, '/', 1) as collegeName,
            s.id_path,
            d."order",
            d.user_count,
            d.user_active_count
        FROM
            standard_app.std_department_relate s
                INNER JOIN addressbook.cp_department d ON d.id = s.dept_id
        WHERE
            s.dept_id = #{id} AND s.LEVEL = 2
    </select>
</mapper>
