<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.access.dao.ComTreeGroupDao">

    <resultMap type="com.lanshan.app.access.entity.ComTreeGroup" id="ComTreeGroupMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="idPath" column="id_path" jdbcType="VARCHAR"/>
        <result property="namePath" column="name_path" jdbcType="VARCHAR"/>
        <result property="treeKey" column="tree_key" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.com_tree_group(name, parent_id, id_path, name_path, tree_key, create_by, create_time, update_by, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.parentId} , #{entity.idPath} , #{entity.namePath} , #{entity.treeKey} , #{entity.createBy} , #{entity.createTime} , #{entity.updateBy} , #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.com_tree_group(name, parent_id, id_path, name_path, tree_key, create_by, create_time, update_by, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentId}, #{entity.idPath}, #{entity.namePath}, #{entity.treeKey}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , parent_id = EXCLUDED.parent_id , id_path = EXCLUDED.id_path , name_path = EXCLUDED.name_path , tree_key = EXCLUDED.tree_key , create_by = EXCLUDED.create_by , create_time = EXCLUDED.create_time , update_by = EXCLUDED.update_by , update_time = EXCLUDED.update_time     </insert>

</mapper>

