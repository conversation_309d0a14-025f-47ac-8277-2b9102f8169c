package com.lanshan.app.hikvision.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "海康威视<车辆管理-车位预约定制化接口>接口请求参数")
public class HikvisionParkingSpaceReservationsAdditionQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "停车库唯一标识")
    private String parkSyscode;
    @ApiModelProperty(value = "车牌号码")
    private String plateNo;
    @ApiModelProperty(value = "手机号")
    private String phoneNo;
    @ApiModelProperty(value = "联系人姓名")
    private String owner;
    @ApiModelProperty(value = "预约类型：0：按时间，1：按次")
    private Integer resvType;
    @ApiModelProperty(value = "允许进出次数   0：单次  1：多次")
    private Integer allowTimes;
    @ApiModelProperty(value = "是否收费   0：收费  1：免费")
    private Integer isCharge;
    @ApiModelProperty(value = "预约方式   5:第三方预约（默认）  6:访客")
    private Integer resvWay;
    @ApiModelProperty(value = "关联入口车道")
    private String inRoadWay;
    @ApiModelProperty(value = "关联出口车道")
    private String outRoadWay;
    @ApiModelProperty(value = "预约开始时间\n" +
            "    按时间预约时必填，按次预约不填\n" +
            "    ISO8601格式：\n" +
            "    yyyy-MM-ddTHH:mm:ss+当前时区，例如北京时间：\n" +
            "            2018-07-26T15:00:00+08:00")
    private String startTime;
    @ApiModelProperty(value = "预约结束时间\n" +
            "    按时间预约时必填，按次预约不填\n" +
            "    ISO8601格式：\n" +
            "    yyyy-MM-ddTHH:mm:ss+当前时区，例如北京时间：\n" +
            "            2018-07-26T15:00:00+08:00")
    private String endTime;
}
