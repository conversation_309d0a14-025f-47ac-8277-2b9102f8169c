package com.lanshan.app.hikvision.qo;

import com.lanshan.app.hikvision.to.HikvisionResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "海康威视<删除人脸信息>接口响应 /api/resource/v1/face/single/add")
public class HikvisionFaceSingleDeleteQo extends HikvisionResponseSet {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("人脸图片Id")
	private String faceId;
}
