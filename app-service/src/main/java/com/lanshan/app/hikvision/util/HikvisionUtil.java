package com.lanshan.app.hikvision.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.hikvision.core.HikvisionConfigInfo;
import com.lanshan.app.hikvision.core.HikvisionConstant;
import com.lanshan.app.hikvision.enums.HikvisionErrorCodeAppointmentRegistrationEnum;
import com.lanshan.app.hikvision.enums.HikvisionErrorCodeFaceRegistrationEnum;
import com.lanshan.app.hikvision.enums.HikvisionErrorCodeParkingspaceReservationsEnum;
import com.lanshan.app.image.enums.HikvisionBusinessTypeEnum;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @ProjectName: capablity-platform-base
 * @Package: com.lanshan.rpc.util
 * @ClassName: HikvisionUtil
 * @Author: leiming
 * @Date: 2022/7/29 17:40
 * @Version: 1.0
 */
@UtilityClass
public final class HikvisionUtil {


    private Map<HikvisionBusinessTypeEnum, HikvisionConfigInfo> map = new HashMap<>();

    static {
        map.put(HikvisionBusinessTypeEnum.APPOINTMENT_REGISTRATION, new HikvisionConfigInfo("***********:443", "25010328", "nTlB72dDRbmGfXbKdhU2"));
        map.put(HikvisionBusinessTypeEnum.PARKINGSPACE_RESERVATIONS, new HikvisionConfigInfo("*************:443", "21944678", "O40Il6LpxNCvAihhv6eO"));
        map.put(HikvisionBusinessTypeEnum.FACE_RESERVATIONS, new HikvisionConfigInfo("***********:443", "25010328", "nTlB72dDRbmGfXbKdhU2"));
    }

    private void setConfig(HikvisionBusinessTypeEnum hikvisionBusinessTypeEnum) {
        HikvisionConfigInfo hikvisionConfigInfo = map.get(hikvisionBusinessTypeEnum);
        hikvisionConfigInfo.setConfig();
    }

    public String commonRequest(String url, String body, HikvisionBusinessTypeEnum hikvisionBusinessTypeEnum) {
        setConfig(hikvisionBusinessTypeEnum);
        String previewUrlsApi = HikvisionConstant.ARTEMIS_PATH + url;
        Map<String, String> path = new HashMap<>(2);
        String key = "https://";
        path.put(key, previewUrlsApi);
        String contentType = "application/json";
        // post请求application/json类型参数
        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);

        JSONObject object = JSON.parseObject(result);
        JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat);

        //如果code是异常错误码则直接返回报错
        String code = object.getString("code");
        if (ObjectUtil.isEmpty(code)) {
            commonException(url, result);
        }
        switch (hikvisionBusinessTypeEnum) {
            case APPOINTMENT_REGISTRATION:
                if (!ObjectUtil.equal(HikvisionErrorCodeAppointmentRegistrationEnum.SUCCESS.value(), code)) {
                    for (HikvisionErrorCodeAppointmentRegistrationEnum hikvisionErrorCodeAppointmentRegistrationEnum : HikvisionErrorCodeAppointmentRegistrationEnum.values()) {
                        if (ObjectUtil.equal(hikvisionErrorCodeAppointmentRegistrationEnum.value(), code)) {
                            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param(hikvisionErrorCodeAppointmentRegistrationEnum.description());
                        }
                    }
                    commonException(url, result);
                }
                break;
            case PARKINGSPACE_RESERVATIONS:
                if (!ObjectUtil.equal(HikvisionErrorCodeParkingspaceReservationsEnum.SUCCESS.value(), code)) {
                    for (HikvisionErrorCodeParkingspaceReservationsEnum hikvisionErrorCodeParkingspaceReservationsEnum : HikvisionErrorCodeParkingspaceReservationsEnum.values()) {
                        if (ObjectUtil.equal(hikvisionErrorCodeParkingspaceReservationsEnum.value(), code)) {
                            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param(hikvisionErrorCodeParkingspaceReservationsEnum.description());
                        }
                    }
                    commonException(url, result);
                }
                break;
            case FACE_RESERVATIONS:
                if (!ObjectUtil.equal(HikvisionErrorCodeFaceRegistrationEnum.SUCCESS.value(), code)) {
                    for (HikvisionErrorCodeFaceRegistrationEnum hikvisionErrorCodeFaceRegistrationEnum : HikvisionErrorCodeFaceRegistrationEnum.values()) {
                        if (ObjectUtil.equal(hikvisionErrorCodeFaceRegistrationEnum.value(), code)) {
                            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param(hikvisionErrorCodeFaceRegistrationEnum.description());
                        }
                    }
                    commonException(url, result);
                }
                break;
            default:
                break;
        }

        return result;
    }

    /**
     * 海康内部统一异常处理
     *
     * @param url
     * @param result
     */
    private void commonException(String url, String result) {
        throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("海康威视接口（" + url + ")调用失败，响应内容：" + result);
    }
}
