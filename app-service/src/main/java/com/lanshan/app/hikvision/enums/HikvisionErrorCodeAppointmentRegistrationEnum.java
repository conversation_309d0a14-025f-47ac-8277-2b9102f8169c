package com.lanshan.app.hikvision.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.HashMap;
import java.util.Map;

/** 海康威视-访客管理错误码 */
public enum HikvisionErrorCodeAppointmentRegistrationEnum {
    SUCCESS("0","调用成功"),
    ERRORCODE_0x0531f002("0x0531f002","预计离开时间必须晚于当前时间"),
    ERRORCODE_0x0531f003("0x0531f003","预计离开时间应晚于预计来访时间"),
    ERRORCODE_0x0531401d("0x0531401d","访客在来访时段内已有其他有效预约或登记"),
    ERRORCODE_0x0531f010("0x0531f010","访客列表内访客信息过多，请检查确认"),
    ERRORCODE_0x0531f016("0x0531f016","预计来访时间查询时间段起止条件错误"),
    ERRORCODE_0x0531f017("0x0531f017","预计离开时间查询时间段起止条件错误"),
    ERRORCODE_0x0531f011("0x0531f011","来访时间查询时间段条件起止条件错误"),
    ERRORCODE_0x0531f012("0x0531f012","离开时间查询时间段起止条件错误"),
    ERRORCODE_0x05314023("0x05314023","访客信息列表中存在相同的手机号码或证件号码"),
    ERRORCODE_0x0531f00e("0x0531f00e","根据参数无法查找到正确记录信息");

    /** 特征值 */
    private String value;
    /** 描述 */
    private String description;

    HikvisionErrorCodeAppointmentRegistrationEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static final Map<String, HikvisionErrorCodeAppointmentRegistrationEnum> VALUES = new HashMap<>();

    static {
        for (final HikvisionErrorCodeAppointmentRegistrationEnum hikvisionErrorCodeAppointmentRegistrationEnum : HikvisionErrorCodeAppointmentRegistrationEnum.values()) {
            HikvisionErrorCodeAppointmentRegistrationEnum.VALUES.put(hikvisionErrorCodeAppointmentRegistrationEnum.value(), hikvisionErrorCodeAppointmentRegistrationEnum);
        }
    }

    @JsonCreator
    public static HikvisionErrorCodeAppointmentRegistrationEnum getByValue(String value) {
        return HikvisionErrorCodeAppointmentRegistrationEnum.VALUES.get(value);
    }

    public String value() {
        return this.value;
    }
    public String description() {
        return this.description;
    }
}
