package com.lanshan.app.hikvision.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "海康威视<根据人员唯一字段获取人员详细信息>接口请求参数")
public class HikvisionPersonInfoQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参数名\n" +
            "certificateNo：证件号码，从获取人员列表v2接口返回报文中的certificateNo字段\n" +
            "personId：人员Id，从获取人员列表v2接口返回报文中的personId字段；\n" +
            "phoneNo：手机号码，从获取人员列表v2接口返回报文中的phoneNo字段；\n" +
            "jobNo：工号，从获取人员列表v2接口返回报文中的jobNo字段。")
    private String paramName;
    @ApiModelProperty(value = "根据入参传对应的值，最大1000个")
    private List<String> paramValue;
}
