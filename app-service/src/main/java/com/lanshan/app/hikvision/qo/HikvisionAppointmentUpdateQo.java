package com.lanshan.app.hikvision.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "海康威视<修改访客预约>接口请求参数")
public class HikvisionAppointmentUpdateQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预约记录ID，与查询访客预约记录v2接口中的appointRecordId对应")
    private String appointRecordId;
    @ApiModelProperty(value = "被访人唯一标识，从 获取人员列表v2 接口获取返回参数personId")
    private String receptionistId;
    @ApiModelProperty(value = "预计来访时间，时间参数需满足ISO8601格式：\n" +
            "yyyy-MM-ddTHH:mm:ss+当前时区，\n" +
            "例如北京时间： 2018-07-26T15:00:00+08:00")
    private String visitStartTime;
    @ApiModelProperty(value = "预计离开时间，时间参数需满足ISO8601格式：\n" +
            "yyyy-MM-ddTHH:mm:ss+当前时区，\n" +
            "例如北京时间： 2018-07-26T15:00:00+08:00 预计离开时间必须晚于当前时间和预计来访时间")
    private String visitEndTime;
    @ApiModelProperty(value = "来访事由，长度为0～128个字符")
    private String visitPurpose;
    @ApiModelProperty(value = "访客信息列表")
    private List<VisitorInfo> visitorInfoList;

    @Data
    @ApiModel(value = "访客信息")
    public static class VisitorInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "访客姓名，长度不超过32，支持1-32位中英文字符，不能包含 ‘ / \\ : * ? “ < >")
        private String visitorName;
        @ApiModelProperty(value = "访客性别 1: 男 2: 女")
        private Integer gender;
        @ApiModelProperty(value = "联系电话建议填写手机号码，仅支持1-20位纯数字")
        private String phoneNo;
        @ApiModelProperty(value = "车牌号，1-16位，不能包含 ‘ / \\ : * ? “ < >")
        private String plateNo;
        @ApiModelProperty(value = "证件类型，参考附录A.11 证件类型(https://open.hikvision.com/docs/docId?productId=5c67f1e2f05948198c909700&version=%2Ff95e951cefc54578b523d1738f65f0a1&curNodeId=ee24b5d1b1ca41e2ba57f2d87b2795c8#a0582e39)")
        private Integer certificateType;
        @ApiModelProperty(value = "证件号码，1~20个数字、字母组成；证件号码非空时，证件类型必填")
        private String certificateNo;
        @ApiModelProperty(value = "证件地址，支持1-128位字母、汉字")
        private String certAddr;
        @ApiModelProperty(value = "发证机关，支持1-32位字母、汉字")
        private String certIssuer;
        @ApiModelProperty(value = "民族")
        private Integer nation;
        @ApiModelProperty(value = "籍贯，支持1-32位字母、汉字")
        private String birthplace;
        @ApiModelProperty(value = "来访单位，支持1-32位字符，不包含’ / \\ : * ? ” < >")
        private String visitorWorkUnit;
        @ApiModelProperty(value = "头像base64编码的字节流，图片最大200K，仅支持jpg格式。由于访客头像需要下发到设备，因此该接口会对访客头像进行质量检测，只有人脸评分大于等于75时，接口才会返回成功，可通过人脸评分接口获取人脸图片的评分。请提供五官清晰，人脸居中的正面人脸免冠照片")
        private String visitorPhoto;
        @ApiModelProperty(value = "访客住址，支持1-128位中英文数字")
        private String visitorAddress;
    }
}
