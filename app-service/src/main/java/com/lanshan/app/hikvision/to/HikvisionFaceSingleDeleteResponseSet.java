package com.lanshan.app.hikvision.to;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "海康威视<根据人脸Id删除人脸>接口响应 /api/resource/v1/face/single/delete")
public class HikvisionFaceSingleDeleteResponseSet extends HikvisionResponseSet {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "返回数据")
    private Result data;

    @Data
    @ApiModel(value = "数据")
    public static class Result implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("人脸Id")
        private String faceId;

        @ApiModelProperty("人脸图片Url")
        private String faceUrl;

        @ApiModelProperty("人员ID")
        private String personId;
    }
}
