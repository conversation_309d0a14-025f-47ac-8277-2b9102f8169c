package com.lanshan.app.hikvision.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.HashMap;
import java.util.Map;

/** 海康威视-人脸管理错误码 */
public enum HikvisionErrorCodeFaceRegistrationEnum {
    SUCCESS("0","调用成功");

    /** 特征值 */
    private String value;
    /** 描述 */
    private String description;

    HikvisionErrorCodeFaceRegistrationEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static final Map<String, HikvisionErrorCodeFaceRegistrationEnum> VALUES = new HashMap<>();

    static {
        for (final HikvisionErrorCodeFaceRegistrationEnum hikvisionErrorCodeFaceRegistrationEnum : HikvisionErrorCodeFaceRegistrationEnum.values()) {
            HikvisionErrorCodeFaceRegistrationEnum.VALUES.put(hikvisionErrorCodeFaceRegistrationEnum.value(), hikvisionErrorCodeFaceRegistrationEnum);
        }
    }

    @JsonCreator
    public static HikvisionErrorCodeFaceRegistrationEnum getByValue(String value) {
        return HikvisionErrorCodeFaceRegistrationEnum.VALUES.get(value);
    }

    public String value() {
        return this.value;
    }
    public String description() {
        return this.description;
    }
}
