package com.lanshan.app.wechatpay.util;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.app.wechatpay.dto.WeChatPayNotifyResponse;
import com.lanshan.app.wechatpay.dto.WeChatPayCreateOrderDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * Description: 微信支付工具
 * Author: jiacheng yang.
 * Date: 2025/02/26 10:42
 * Version: 1.0
 */

@Slf4j
public class WeChatPayUtil {

    private final static String WECHAT_PAY_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    /**
     * Description: 统一下单 <a href="https://pay.weixin.qq.com/doc/v2/merchant/4011935214">v2版本</a>
     * Author: jiacheng yang.
     * Date: 2025/02/26 14:47
     * Param: []
     */
    public static Map<String, Object> createWeChatPayOrder(WeChatPayCreateOrderDTO dto) {
        String paySecret = dto.getPaySecret();

        Map<String, Object> params = new TreeMap<>();
        params.put("appid", dto.getAppid());
        params.put("mch_id", dto.getMch_id());
        params.put("nonce_str", dto.getNonce_str());
        params.put("body", dto.getBody());
        params.put("out_trade_no", dto.getOut_trade_no());
        params.put("total_fee", dto.getTotal_fee());
        params.put("spbill_create_ip", dto.getSpbill_create_ip());
        params.put("notify_url", dto.getNotify_url());
        params.put("trade_type", dto.getTrade_type());
        params.put("attach", dto.getAttach());
        params.put("openid", dto.getOpenid());
        params.put("sign_type", "MD5");

        // 生成签名
        String sign = generateSignature(params, paySecret);
        params.put("sign", sign);
        //转xml
        String xmlParams = XmlUtil.toXml(params);
        //发起请求
        String response = HttpUtil.post(WECHAT_PAY_URL, xmlParams);
        //解析返回结果
        Map<String, Object> result  = XmlUtil.getObj(response,new TypeReference<HashMap<String,Object>>(){});
        if (result == null){
            log.error("微信支付下单失败，返回结果：{}", response);
            return null;
        }
        String returnCode = (String)result.get("return_code");
        if ("SUCCESS".equals(returnCode)) {
            Map<String, Object> data = new TreeMap<>();
            data.put("appId", dto.getAppid());
            data.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
            data.put("nonceStr", result.get("nonce_str"));
            data.put("package", "prepay_id=" + result.get("prepay_id"));
            data.put("signType", "MD5");
            String paySign = generateSignature(data, paySecret);
            data.put("paySign", paySign);
            log.error("微信支付下单成功，返回结果：{}", response);
            return data;
        } else {
            log.error("微信支付下单失败，返回结果：{}", response);
            return null;
        }
    }

    /**
     * Description: 校验回调参数
     * Author: jiacheng yang.
     * Date: 2025/02/27 11:19
     * Param: [notifyData, paySecret 支付密钥]
     */
    public static WeChatPayNotifyResponse validateNotifyParams(Map<String, Object> notifyData,String paySecret) {
        if (MapUtil.isEmpty(notifyData)) {
            String return_msg = "微信支付回调通知参数格式校验错误";
            log.error(return_msg);
            return WeChatPayNotifyResponse.fail(return_msg);
        }
        String returnCode = (String)notifyData.get("return_code");
        if (!returnCode.equals(WeChatPayNotifyResponse.SUCCESS)) {
            String return_msg = "微信支付回调通知 return_code 校验错误";
            log.error(return_msg);
            return WeChatPayNotifyResponse.fail(return_msg);
        }

        String resultCode = (String)notifyData.get("result_code");
        if (!resultCode.equals(WeChatPayNotifyResponse.SUCCESS)) {
            String return_msg = "微信支付回调通知 result_code 校验错误";
            log.error(return_msg);
            return WeChatPayNotifyResponse.fail(return_msg);
        }
        //验证签名
        String sign = (String) notifyData.get("sign");
        notifyData.remove("sign");
        String calculatedSign = WeChatPayUtil.generateSignature(notifyData, paySecret);
        if (!sign.equals(calculatedSign)) {
            String return_msg = "微信支付回调通知验证签名失败";
            log.error(return_msg);
            return WeChatPayNotifyResponse.fail(return_msg);
        }

        return WeChatPayNotifyResponse.success();
    }

    /**
     * Description: 生成签名方法（<a href="https://pay.weixin.qq.com/doc/v2/merchant/4011985891">支付签名规则</a>）
     * Author: jiacheng yang.
     * Date: 2025/02/26 15:19
     * Param: [params, apiKey]
     */
    public static String generateSignature(Map<String, Object> params, String apiKey) {
        String stringA = MapUtil.sortJoin(params, "&", "=", true);
        String stringSignTemp = stringA + "&key=" + apiKey;
        return SecureUtil.md5(stringSignTemp).toUpperCase();
    }

    /**
     * Description: 生成JS-SDK使用权限签名算法（<a href="https://developer.work.weixin.qq.com/document/10029#附录1-JS-SDK使用权限签名算法"></a>）
     * Author: jiacheng yang.
     * Date: 2025/02/26 15:19
     * Param: [params, apiKey]
     */
    public static String generateJsApiSignature(Map<String, Object> params) {
        String stringA = MapUtil.sortJoin(params, "&", "=", true);
        return SecureUtil.sha1(stringA);
    }


    /**
     * 生成符合微信支付规则的订单号
     *
     * @param prefix 订单号前缀（可选，用于区分业务类型）
     * @return 订单号（长度：6~32 字符）
     */
    public static String generateOrderNo(String prefix) {
        // 1. 时间戳（精确到毫秒）
        String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");

        // 2. 随机数（6 位）
        String randomStr = RandomUtil.randomString(6);

        // 3. 拼接前缀（如果存在）
        String orderNo = StrUtil.isNotBlank(prefix) ? prefix + timestamp + randomStr : timestamp + randomStr;

        // 4. 确保长度在 6~32 之间
        if (orderNo.length() > 32) {
            orderNo = orderNo.substring(0, 32);
        }

        return orderNo;
    }

    public static void main(String[] args) {
        System.out.println(generateOrderNo(null));
    }

}
