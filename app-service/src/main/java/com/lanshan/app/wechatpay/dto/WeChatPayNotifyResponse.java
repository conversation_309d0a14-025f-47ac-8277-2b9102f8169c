package com.lanshan.app.wechatpay.dto;


import java.io.Serializable;

/**
 * Description: 微信支付通知返回结果
 * Author: jiacheng yang.
 * Date: 2025/02/27 10:09
 * Version: 1.0
 */

public class WeChatPayNotifyResponse implements Serializable {
    private static final long serialVersionUID = 7308652487408456100L;

    /**
     * 成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 失败
     */
    public static final String FAIL = "FAIL";


    //SUCCESS/FAIL
    private String return_code;
    private String return_msg;

    public WeChatPayNotifyResponse() {
    }


    public static WeChatPayNotifyResponse restResult(String return_code, String return_msg) {
        WeChatPayNotifyResponse result = new WeChatPayNotifyResponse();
        result.setReturn_code(return_code);
        result.setReturn_msg(return_msg);
        return result;
    }

    public static WeChatPayNotifyResponse success(){
        return restResult( SUCCESS, "OK");
    }

    public static WeChatPayNotifyResponse fail(String msg){
        return restResult( FAIL, msg);
    }

    public String getReturn_code() {
        return return_code;
    }

    public void setReturn_code(String return_code) {
        this.return_code = return_code;
    }

    public String getReturn_msg() {
        return return_msg;
    }

    public void setReturn_msg(String return_msg) {
        this.return_msg = return_msg;
    }
}
