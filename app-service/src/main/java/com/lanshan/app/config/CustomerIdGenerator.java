package com.lanshan.app.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.lanshan.app.common.utils.IdGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class CustomerIdGenerator implements IdentifierGenerator {

    @Override
    public Long nextId(Object entity) {
        return IdGenerator.generateId();
    }

    /**
     * 自定义id生成器, 只生成16位的id, 防止前端的无法完整接收
     *
     * @return IdentifierGenerator
     */
    @Primary
    @Bean
    public IdentifierGenerator idGenerator() {
        return new CustomerIdGenerator();
    }
}
