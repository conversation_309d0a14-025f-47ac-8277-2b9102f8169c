package com.lanshan.app.fleamarket.converter;


import java.util.List;

import com.lanshan.app.fleamarket.entity.FleaCommodityType;
import com.lanshan.app.fleamarket.qo.FleaCommodityTypeQo;
import com.lanshan.app.fleamarket.vo.FleaCommodityTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 商品类型表(FleaCommodityType)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface FleaCommodityTypeConverter {

    FleaCommodityTypeConverter INSTANCE = Mappers.getMapper(FleaCommodityTypeConverter.class);

    FleaCommodityTypeVO toVO(FleaCommodityType entity);

    FleaCommodityType toEntity(FleaCommodityTypeVO vo);

    FleaCommodityType toEntity(FleaCommodityTypeQo vo);

    List<FleaCommodityTypeVO> toVO(List<FleaCommodityType> entityList);

    List<FleaCommodityType> toEntity(List<FleaCommodityTypeVO> voList);
}


