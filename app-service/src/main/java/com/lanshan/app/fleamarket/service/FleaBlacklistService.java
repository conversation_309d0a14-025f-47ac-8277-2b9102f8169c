package com.lanshan.app.fleamarket.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.fleamarket.entity.FleaBlacklist;
import com.lanshan.app.fleamarket.qo.FleaBlacklistPageQo;
import com.lanshan.app.fleamarket.qo.FleaBlacklistSaveQo;
import com.lanshan.app.fleamarket.vo.FleaBlacklistVO;

import java.util.List;

/**
 * 黑名单表(FleaBlacklist)表服务接口
 */
public interface FleaBlacklistService extends IService<FleaBlacklist> {

    /**
     * 检查黑名单
     *
     * @return true: 在黑名单，false: 不在黑名单
     */
    Boolean checkBlackList(String userId);

    /**
     * 分页查询黑名单
     *
     * @param pageQo 分页查询对象
     * @return 分页查询结果
     */
    IPage<FleaBlacklistVO> pageBlacklist(FleaBlacklistPageQo pageQo);

    /**
     * 添加黑名单
     *
     * @param qoList 黑名单保存对象
     */
    void addBlacklist(List<FleaBlacklistSaveQo> qoList);

    /**
     * 删除黑名单
     *
     * @param userid 用户id
     */
    void delBlacklist(String userid);
}

