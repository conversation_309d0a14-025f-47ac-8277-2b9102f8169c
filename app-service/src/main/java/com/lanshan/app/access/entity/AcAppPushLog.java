package com.lanshan.app.access.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 应用数据推送日志(AcAppPushLog)实体
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AcAppPushLog implements Serializable {
    private static final long serialVersionUID = -49408557219210765L;

    @ApiModelProperty("主键")
    @TableId(type =IdType.AUTO)
    private Long id;

    @ApiModelProperty("接口调用接入方ID")
    private Long companyId;

    @ApiModelProperty("接口调用应用ID")
    private Long appId;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("操作类型描述")
    private String operateTypeDesc;

    @ApiModelProperty("操作数据")
    private String operateParam;

    @ApiModelProperty("调用结果")
    private String responseResult;

    @ApiModelProperty("调用结果状态")
    private Integer responseStatus;

    @ApiModelProperty("接口调用时间")
    private Date createTime;

}

