package com.lanshan.app.access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.access.entity.AcDataField;

import java.util.List;

/**
 * 权限数据集字段(AcDataField)服务接口
 */
public interface AcDataFieldService extends IService<AcDataField> {

    /**
     * 根据数据权限唯一标识查询字段
     * @param dataKey 数据权限唯一标识
     * @return 字段列表
     */
    List<AcDataField> listByDataKey(String dataKey);
}
