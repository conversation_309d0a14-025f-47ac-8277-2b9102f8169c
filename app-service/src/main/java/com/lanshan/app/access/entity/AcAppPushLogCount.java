package com.lanshan.app.access.entity;

import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统开放API调用日志次数(AcAppPushLogCount)实体
 */
@Data
public class AcAppPushLogCount implements Serializable {
    private static final long serialVersionUID = -62973344623988696L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("操作类型描述")
    private String operateTypeDesc;

    @ApiModelProperty("接入方ID")
    private Long companyId;

    @ApiModelProperty("应用ID")
    private Long appId;

    @ApiModelProperty("推送次数")
    private Integer count;

    @ApiModelProperty("推送成功次数")
    private Integer successCount;

    @ApiModelProperty("推送失败次数")
    private Integer failCount;

    @ApiModelProperty("日志日期")
    private Date logDate;

    @ApiModelProperty("创建时间")
    private Date createTime;
}

