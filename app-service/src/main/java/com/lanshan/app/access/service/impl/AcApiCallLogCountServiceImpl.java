package com.lanshan.app.access.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.access.entity.AcApiCallLogCount;
import com.lanshan.app.access.dao.AcApiCallLogCountDao;
import com.lanshan.app.access.service.AcApiCallLogCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统开放API调用日志次数(AcApiCallCountLog)服务实现类
 */
@Slf4j
@Service
public class AcApiCallLogCountServiceImpl extends ServiceImpl<AcApiCallLogCountDao, AcApiCallLogCount> implements AcApiCallLogCountService {

}
