package com.lanshan.app.access.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.access.converter.AcApiCallLogCountConverter;
import com.lanshan.app.access.converter.AcApiCallLogConverter;
import com.lanshan.app.access.dao.AcApiCallLogCountDao;
import com.lanshan.app.access.dao.AcApiCallLogDao;
import com.lanshan.app.access.dto.AcApiCallLogGroupCountDto;
import com.lanshan.app.access.entity.AcApiCallLogCount;
import com.lanshan.app.access.entity.AcApiCallLog;
import com.lanshan.app.access.excel.ApiCallLogExportDto;
import com.lanshan.app.access.qo.AcApiCallLogCountQO;
import com.lanshan.app.access.qo.AcApiCallLogPageQO;
import com.lanshan.app.access.service.AcApiCallLogCountService;
import com.lanshan.app.access.service.AcApiCallLogService;
import com.lanshan.app.access.vo.AcApiCallLogCountVO;
import com.lanshan.app.access.vo.AcApiCallLogTodayCountVO;
import com.lanshan.app.access.vo.AcApiCallLogVO;
import com.lanshan.app.common.enums.ResultTypeEnum;
import com.lanshan.app.common.utils.ExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 系统开放API调用日志(AcApiCallLog)表服务实现类
 *
 * <AUTHOR>
 */
@Service("acApiCallLogService")
public class AcApiCallLogServiceImpl extends ServiceImpl<AcApiCallLogDao, AcApiCallLog> implements AcApiCallLogService {

    @Resource
    private AcApiCallLogDao acApiCallLogDao;

    @Resource
    private AcApiCallLogCountDao acApiCallLogCountDao;

    @Resource
    private AcApiCallLogCountService acApiCallLogCountService;

    @Override
    public IPage<AcApiCallLogVO> pageApiCallLog(AcApiCallLogPageQO pageQO) {
        Page<AcApiCallLog> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcApiCallLog> queryWrapper = Wrappers.lambdaQuery(AcApiCallLog.class);

        //接入方id
        if (pageQO.getCompanyId() != null) {
            queryWrapper.eq(AcApiCallLog::getCompanyId, pageQO.getCompanyId());
        }
        //appId
        if (pageQO.getAppId() != null) {
            queryWrapper.eq(AcApiCallLog::getAppId, pageQO.getAppId());
        }
        //apiId
        if (pageQO.getApiId() != null) {
            queryWrapper.eq(AcApiCallLog::getApiId, pageQO.getApiId());
        }
        //接口调用结果状态
        if (pageQO.getResponseStatus() != null) {
            queryWrapper.eq(AcApiCallLog::getResponseStatus, pageQO.getResponseStatus());
        }
        //接口调用时间范围
        if (pageQO.getStartTime() != null && pageQO.getEndTime() != null) {
            queryWrapper.ge(AcApiCallLog::getCreateTime, pageQO.getStartTime());
            queryWrapper.lt(AcApiCallLog::getCreateTime, DateUtil.offsetDay(pageQO.getEndTime(), 1));
        }

        //按时间倒序排列
        queryWrapper.orderBy(true, false, AcApiCallLog::getCreateTime);
        //分页查询
        IPage<AcApiCallLog> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        IPage<AcApiCallLogVO> pageVo = result.convert(AcApiCallLogConverter.INSTANCE::toVO);
        for (AcApiCallLogVO vo : pageVo.getRecords()) {
            //设置结果状态
            vo.setResponseStatusDesc(EnumUtil.getFieldBy(ResultTypeEnum::getMsg, ResultTypeEnum::getCode, vo.getResponseStatus()));
        }

        return pageVo;
    }

    @Override
    public void exportApiCallLog(AcApiCallLogPageQO pageQO, HttpServletResponse response) throws IOException {
        //不分页查询
        pageQO.setPage(1L);
        pageQO.setSize(Long.MAX_VALUE);
        //查询接口调用日志
        IPage<AcApiCallLogVO> page = pageApiCallLog(pageQO);
        List<AcApiCallLogVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<ApiCallLogExportDto> userExportDtoList = BeanUtil.copyToList(records, ApiCallLogExportDto.class);
        //导出Excel
        ExcelUtils.exportExcel(response, userExportDtoList, "接口调用日志");
    }

    @Override
    public List<AcApiCallLogCountVO> listCallCount(AcApiCallLogCountQO qo) {
        List<AcApiCallLogCountVO> voList = new ArrayList<>();
        //设置开始时间到结束时间以天为单位的默认列表
        List<DateTime> dateTimeList = DateUtil.rangeToList(qo.getStartTime(), qo.getEndTime(), DateField.DAY_OF_YEAR);
        for (DateTime dateTime : dateTimeList) {
            AcApiCallLogCountVO vo = new AcApiCallLogCountVO();
            vo.setLogDate(dateTime);
            vo.setCount(0);
            vo.setFailCount(0);
            vo.setSuccessCount(0);
            voList.add(vo);
        }

        //查询调用日志次数
        List<AcApiCallLogCountVO> resultList = acApiCallLogCountDao.listCallCount(qo);
        if (CollUtil.isNotEmpty(resultList)) {
            Map<Date, AcApiCallLogCountVO> resultMap = CollUtil.toMap(resultList, null, AcApiCallLogCountVO::getLogDate, item -> item);
            //给每天设置调用次数
            for (AcApiCallLogCountVO vo : voList) {
                AcApiCallLogCountVO result = resultMap.get(vo.getLogDate());
                if (result != null) {
                    vo.setCount(result.getCount());
                    vo.setFailCount(result.getFailCount());
                    vo.setSuccessCount(result.getSuccessCount());
                }
            }
        }

        return voList;
    }

    @Override
    public List<AcApiCallLogTodayCountVO> listTodayCallCount(AcApiCallLogCountQO qo) {
        List<AcApiCallLogTodayCountVO> voList = new ArrayList<>();
        //设置24小时的默认列表
        for (int i = 0; i < 24; i++) {
            AcApiCallLogTodayCountVO vo = new AcApiCallLogTodayCountVO();
            vo.setHour(i);
            vo.setCount(0);
            vo.setFailCount(0);
            vo.setSuccessCount(0);
            voList.add(vo);
        }

        //查询今日调用日志次数
        List<AcApiCallLogTodayCountVO> resultlist = acApiCallLogDao.listTodayCallCount(qo);
        if (CollUtil.isNotEmpty(resultlist)) {
            Map<Integer, AcApiCallLogTodayCountVO> resultMap = CollUtil.toMap(resultlist, null, AcApiCallLogTodayCountVO::getHour, item -> item);
            //给每个小时设置调用次数
            for (AcApiCallLogTodayCountVO vo : voList) {
                AcApiCallLogTodayCountVO result = resultMap.get(vo.getHour());
                if (result != null) {
                    vo.setCount(result.getCount());
                    vo.setFailCount(result.getFailCount());
                    vo.setSuccessCount(result.getSuccessCount());
                }
            }
        }

        //将返回列表小时从0~23转为1~24
        voList.forEach(item -> item.setHour(item.getHour() + 1));
        return voList;
    }

    @Override
    public void genApiCallLogCount(String dateStr) {
        Date date;
        //如果为空，则默认取昨天的数据
        if (StringUtils.isBlank(dateStr)) {
            date = DateUtil.yesterday();
        } else {
            date = DateUtil.parse(dateStr);
        }

        //查询调用日志次数，按接入方、APP、API分组
        List<AcApiCallLogGroupCountDto> list = acApiCallLogDao.listCallLogGroupCount(date);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<AcApiCallLogCount> entityList = AcApiCallLogCountConverter.INSTANCE.toEntity(list);
        //批量新增
        acApiCallLogCountService.saveBatch(entityList);
    }
}

