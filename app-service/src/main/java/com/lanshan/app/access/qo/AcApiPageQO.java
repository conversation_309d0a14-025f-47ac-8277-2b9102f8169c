package com.lanshan.app.access.qo;


import com.lanshan.app.common.bo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ApiPageQO")
@Data
@ToString
public class AcApiPageQO extends PageQo implements Serializable {

    private static final long serialVersionUID = -6383729814459099265L;

    @ApiModelProperty(value = "所属分组ID")
    private Long groupId;

    @ApiModelProperty(value = "API名称")
    private String name;
}

