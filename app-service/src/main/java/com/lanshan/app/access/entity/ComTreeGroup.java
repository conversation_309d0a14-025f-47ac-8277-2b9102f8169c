package com.lanshan.app.access.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 树形分组(ComTreeGroup)表实体类
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ComTreeGroup extends Model<ComTreeGroup> {
    /**
     *主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *名称
     */
    private String name;
    /**
     *上级节点ID
     */
    private Long parentId;
    /**
     *包含所有上级节点ID的ID
     */
    private String idPath;
    /**
     *包含所有上级节点名称的名称
     */
    private String namePath;
    /**
     *树唯一标识
     */
    private String treeKey;
    /**
     *创建者
     */
    private String createBy;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新者
     */
    private String updateBy;
    /**
     *更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

