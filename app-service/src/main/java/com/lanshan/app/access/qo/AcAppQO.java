package com.lanshan.app.access.qo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "接入方应用QO")
@Data
@ToString
public class AcAppQO implements Serializable {
    private static final long serialVersionUID = 3203174907553084171L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "操作类型 1：调用类型 2：推送类型")
    private Integer operateType;

    @ApiModelProperty(value = "应用所属接入方ID")
    private Long companyId;

    @ApiModelProperty(value = "应用所属接入方名称")
    private String companyName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否需要用户授权 0:否 1:是")
    private String needUserAuth;

    @ApiModelProperty(value = "申请原因")
    private String applicationReason;

    @ApiModelProperty(value = "状态。0：禁用；1：正常")
    private Integer status;

    @ApiModelProperty(value = "有效开始时间")
    private Date startTime;

    @ApiModelProperty(value = "有效结束时间")
    private Date endTime;

    @ApiModelProperty(value = "接口列表")
    private List<Long> apiIdList;

    @ApiModelProperty(value = "数据权限列表")
    private List<AcDataScopeQO> dataScopeList;

    @ApiModelProperty(value = "开放文档地址")
    private String openDocUrl;
}

