package com.lanshan.app.access.qo;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "推送日志次数QO")
@Data
@ToString
public class AcAppPushLogCountQO implements Serializable {

    private static final long serialVersionUID = -215428800556704430L;

    @ApiModelProperty(value = "接入方id")
    private Long companyId;

    @ApiModelProperty(value = "appId")
    private Long appId;

    @ExcelProperty("操作类型")
    private String operateType;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern ="yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern ="yyyy-MM-dd")
    private Date endTime;
}

