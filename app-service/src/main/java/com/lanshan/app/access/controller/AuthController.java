package com.lanshan.app.access.controller;


import com.lanshan.app.access.service.AuthService;
import com.lanshan.app.common.bo.Result;
import com.lanshan.app.common.utils.StpUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 权限控制
 */
@RestController
@RequestMapping("/access/auth")
@Api(tags = "权限控制")
public class AuthController {

    @Resource
    private AuthService authService;

    @ApiOperation("获取API访问token")
    @GetMapping("/getApiAccessToken")
    public Result<String> getApiAccessToken(String keyId, String appSecret) {
        return Result.build(authService.getApiAccessToken(keyId, appSecret).getTokenValue());
    }

    /**
     * 模拟学生，老师H5登录
     *
     * @param userId 学工号
     * @return token
     */
    @ApiOperation("模拟学生，老师H5登录")
    @PostMapping("/mockUserLongin")
    public Result<String> mockUserLongin(@RequestParam(value = "userId") String userId) {
        StpUserUtil.login(userId);
        return Result.build(StpUserUtil.getTokenValue());
    }
}
