package com.lanshan.app.access.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 接入方(AcCompany)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "接入方VO")
@Data
@ToString
public class AcCompanyVO implements Serializable {

    private static final long serialVersionUID = -6383729814459099265L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "接入方名称")
    private String name;

    @ApiModelProperty(value = "接入方的keyId")
    private String keyId;

    @ApiModelProperty(value = "接入方访问密钥")
    private String secret;

    @ApiModelProperty(value = "责任人")
    private String contactPerson;

    @ApiModelProperty(value = "责任人电话")
    private String contactNumber;

    @ApiModelProperty(value = "状态。0：禁用；1：正常")
    private Integer status;

    @ApiModelProperty(value = "状态。0：禁用；1：正常")
    private String statusDesc;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "登录账号")
    private String loginName;

    @ApiModelProperty(value = "登录密码")
    private String loginPwd;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "开放文档地址")
    private String openDocUrl;
}

