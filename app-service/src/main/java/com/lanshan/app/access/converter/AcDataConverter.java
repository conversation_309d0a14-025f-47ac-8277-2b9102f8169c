package com.lanshan.app.access.converter;


import com.lanshan.app.access.entity.AcData;
import com.lanshan.app.access.vo.AcDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 权限数据集(AcData)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface AcDataConverter {

    AcDataConverter INSTANCE = Mappers.getMapper(AcDataConverter.class);

    AcDataVO toVO(AcData entity);

    AcData toEntity(AcDataVO vo);

    List<AcDataVO> toVO(List<AcData> entityList);

    List<AcData> toEntity(List<AcDataVO> voList);
}


