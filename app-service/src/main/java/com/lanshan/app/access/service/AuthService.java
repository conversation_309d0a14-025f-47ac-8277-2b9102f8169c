package com.lanshan.app.access.service;

import cn.dev33.satoken.stp.SaTokenInfo;

/**
 * 认证服务接口
 *
 */
public interface AuthService {

    /**
     * 获取API访问令牌
     *
     * @param keyId 密钥id
     * @param appSecret 应用密钥
     * @return SaTokenInfo token信息
     */
    SaTokenInfo getApiAccessToken(String keyId, String appSecret);


    /**
     * 从令牌中获取应用ID
     *
     * @param token 令牌
     * @return 应用ID
     */
    String getAppIdFromToken(String token);
}
