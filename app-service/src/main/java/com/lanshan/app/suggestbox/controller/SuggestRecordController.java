package com.lanshan.app.suggestbox.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.suggestbox.converter.SuggestRecordConverter;
import com.lanshan.app.suggestbox.qo.SuggestRecordQO;
import com.lanshan.app.suggestbox.service.SuggestRecordService;
import com.lanshan.app.suggestbox.vo.ReplyRecordVO;
import com.lanshan.app.suggestbox.vo.SuggestRecordVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 意见箱记录表(SuggestRecord)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("suggest/suggestRecord")
@Api(tags = "意见箱记录表(SuggestRecord)控制层")
public class SuggestRecordController {
    /**
     * 服务对象
     */
    @Resource
    private SuggestRecordService suggestRecordService;

    /**
     * 分页查询所有数据
     *
     * @param qo 查询实体
     * @return 分页数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<SuggestRecordVO>> page(SuggestRecordQO qo) {
        return Result.build(suggestRecordService.pageByParam(qo));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<SuggestRecordVO> selectOne(@PathVariable Serializable id) {
        return Result.build(suggestRecordService.selectOne(id));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody SuggestRecordVO vo) {
        return Result.build(suggestRecordService.saveRecord(vo));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update/put")
    public Result<Boolean> update(@RequestBody SuggestRecordVO vo) {
        return Result.build(this.suggestRecordService.updateById(SuggestRecordConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.suggestRecordService.removeByIds(idList));
    }

    /**
     * 回复意见
     *
     * @param vo 回复对象
     * @return true 成功 false 失败
     */
    @ApiOperation("回复意见")
    @PostMapping("/reply/put")
    public Result<Boolean> reply(@RequestBody ReplyRecordVO vo) {
        return Result.build(this.suggestRecordService.reply(vo));
    }
}

