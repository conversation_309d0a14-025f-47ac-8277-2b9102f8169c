package com.lanshan.app.suggestbox.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.suggestbox.entity.SuggestRecord;
import com.lanshan.app.suggestbox.qo.SuggestRecordQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 意见箱记录表(SuggestRecord)表数据库访问层
 *
 * <AUTHOR>
 */
public interface SuggestRecordDao extends BaseMapper<SuggestRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SuggestRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SuggestRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SuggestRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SuggestRecord> entities);

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param qo   查询参数
     * @return 分页对象
     */
    IPage<SuggestRecord> pageByParam(@Param("page") Page<SuggestRecord> page, @Param("qo") SuggestRecordQO qo);
}

