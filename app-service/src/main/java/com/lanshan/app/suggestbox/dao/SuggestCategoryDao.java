package com.lanshan.app.suggestbox.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.suggestbox.entity.SuggestCategory;
import com.lanshan.app.suggestbox.qo.SuggestCategoryQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 意见箱分类表(SuggestCategory)表数据库访问层
 *
 * <AUTHOR>
 */
public interface SuggestCategoryDao extends BaseMapper<SuggestCategory> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SuggestCategory> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SuggestCategory> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SuggestCategory> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SuggestCategory> entities);

    /**
     * 根据参数分页查询事务数据
     *
     * @param page page 对象
     * @param qo   查询对象
     * @return 分页结果
     */
    IPage<SuggestCategory> pageTransactionByParam(@Param("page") Page<SuggestCategory> page, @Param("qo") SuggestCategoryQO qo);

    /**
     * 根据参数分页查询应用数据
     *
     * @param of page 对象
     * @param qo 查询对象
     * @return 分页结果
     */
    IPage<SuggestCategory> pageApplicationByParam(Page<SuggestCategory> of, SuggestCategoryQO qo);
}

