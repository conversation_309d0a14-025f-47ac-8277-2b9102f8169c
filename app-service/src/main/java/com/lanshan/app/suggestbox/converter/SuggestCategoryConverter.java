package com.lanshan.app.suggestbox.converter;


import com.lanshan.app.suggestbox.entity.SuggestCategory;
import com.lanshan.app.suggestbox.vo.SuggestCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 意见箱分类表(SuggestCategory)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface SuggestCategoryConverter {

    SuggestCategoryConverter INSTANCE = Mappers.getMapper(SuggestCategoryConverter.class);

    @Mapping(target = "userVOList", ignore = true)
    SuggestCategoryVO toVO(SuggestCategory entity);

    SuggestCategory toEntity(SuggestCategoryVO vo);

    List<SuggestCategoryVO> toVO(List<SuggestCategory> entityList);

    List<SuggestCategory> toEntity(List<SuggestCategoryVO> voList);
}


