package com.lanshan.app.suggestbox.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.suggestbox.entity.SuggestRecord;
import com.lanshan.app.suggestbox.qo.SuggestRecordQO;
import com.lanshan.app.suggestbox.vo.ReplyRecordVO;
import com.lanshan.app.suggestbox.vo.SuggestRecordVO;

import java.io.Serializable;

/**
 * 意见箱记录表(SuggestRecord)表服务接口
 *
 * <AUTHOR>
 */
public interface SuggestRecordService extends IService<SuggestRecord> {

    /**
     * 分页查询所有数据
     *
     * @param qo 查询对象
     * @return 分页对象
     */
    IPage<SuggestRecordVO> pageByParam(SuggestRecordQO qo);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据VO
     */
    SuggestRecordVO selectOne(Serializable id);

    /**
     * 回复
     *
     * @param vo 回复实体
     * @return 是否成功
     */
    Boolean reply(ReplyRecordVO vo);

    /**
     * 新增意见
     *
     * @param vo 意见VO
     * @return true 成功 false 失败
     */
    Boolean saveRecord(SuggestRecordVO vo);
}

