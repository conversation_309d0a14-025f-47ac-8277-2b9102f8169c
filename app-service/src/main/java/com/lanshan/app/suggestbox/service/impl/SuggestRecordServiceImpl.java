package com.lanshan.app.suggestbox.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.utils.FeignResultUtil;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.suggestbox.converter.SuggestRecordConverter;
import com.lanshan.app.suggestbox.dao.SuggestRecordDao;
import com.lanshan.app.suggestbox.entity.SuggestRecord;
import com.lanshan.app.suggestbox.enums.CategoryTypeEnum;
import com.lanshan.app.suggestbox.qo.SuggestRecordQO;
import com.lanshan.app.suggestbox.service.SuggestRecordService;
import com.lanshan.app.suggestbox.vo.ReplyRecordVO;
import com.lanshan.app.suggestbox.vo.SmallCategoryVO;
import com.lanshan.app.suggestbox.vo.SuggestRecordVO;
import com.lanshan.app.suggestbox.vo.UserVO;
import com.lanshan.base.api.feign.addressbook.CpDeptFeign;
import com.lanshan.base.api.feign.addressbook.CpUserFeign;
import com.lanshan.base.api.vo.user.DepartmentVo;
import com.lanshan.base.api.vo.user.UserInfoVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 意见箱记录表(SuggestRecord)表服务实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service("suggestRecordService")
public class SuggestRecordServiceImpl extends ServiceImpl<SuggestRecordDao, SuggestRecord> implements SuggestRecordService {

    private final CpUserFeign cpUserFeign;

    private final CpDeptFeign cpDeptFeign;

    private final SuggestCategoryServiceImpl suggestCategoryService;
    @Lazy
    @Resource
    private SuggestRecordService suggestRecordService;

    @Override
    public IPage<SuggestRecordVO> pageByParam(SuggestRecordQO qo) {
        IPage<SuggestRecordVO> page = this.baseMapper.pageByParam(Page.<SuggestRecord>of(qo.getPage(), qo.getSize()), qo)
                .convert(SuggestRecordConverter.INSTANCE::toVO);
        return page.setRecords(fillCpUserInfo(page.getRecords()));
    }

    @Override
    public SuggestRecordVO selectOne(Serializable id) {
        return fillCpUserInfo(Collections.singletonList(SuggestRecordConverter.INSTANCE.toVO(this.getById(id)))).get(0);
    }

    @Override
    public Boolean reply(ReplyRecordVO vo) {
        String recordId = vo.getRecordId();
        SuggestRecord record = this.getById(recordId);
        String replyUserid = record.getReplyUserid();
        String reply = record.getReply();
        if (StringUtils.isNotBlank(replyUserid) || StringUtils.isNoneBlank(reply)) {
            throw ExceptionCodeEnum.SUGGEST_REPLY_ALREADY_EXIST.toServiceException();
        }
        return this.update(Wrappers.lambdaUpdate(SuggestRecord.class).eq(SuggestRecord::getId, recordId)
                .set(SuggestRecord::getReplyUserid, SecurityContextHolder.getUserIdStr())
                .set(SuggestRecord::getReplyDate, new Date())
                .set(SuggestRecord::getReply, vo.getReply()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRecord(SuggestRecordVO vo) {
        return suggestRecordService.save(SuggestRecordConverter.INSTANCE.toEntity(vo));
    }

    /**
     * 填充企微用户信息
     *
     * @param suggestRecordVOS 意见VO
     * @return 意见箱分类VO {@link SuggestRecordVO}
     */
    @NotNull
    private List<SuggestRecordVO> fillCpUserInfo(List<SuggestRecordVO> suggestRecordVOS) {
        if (CollUtil.isEmpty(suggestRecordVOS)) {
            return suggestRecordVOS;
        }
        //获取提交人
        List<String> submitUserIdList = Optional.of(suggestRecordVOS).orElse(Collections.emptyList()).stream()
                .map(SuggestRecordVO::getCreator).collect(Collectors.toList());
        //获取回复人
        List<String> replyUseridList = Optional.of(suggestRecordVOS).orElse(CollUtil.newArrayList()).stream()
                .map(SuggestRecordVO::getReplyUserid).collect(Collectors.toList());
        //合并在一起
        submitUserIdList.addAll(replyUseridList);
        //请求企微获取用户的信息
        List<UserInfoVo> userInfoList = FeignResultUtil.success(cpUserFeign.listUsersByUseridList(submitUserIdList));
        //转换成 map 使用 userid 为 key，用户对象为 value
        Map<String, UserInfoVo> userInfoVoMap = Optional.ofNullable(userInfoList).orElse(Collections.emptyList()).stream()
                .collect(Collectors.toMap(UserInfoVo::getUserid, userInfoVo -> userInfoVo, (o, n) -> n));
        //获取部门信息
        List<Long> deptIdList = Optional.ofNullable(userInfoList).orElse(CollUtil.newArrayList()).stream()
                .map(UserInfoVo::getMainDepartment).collect(Collectors.toList());
        //查询部门信息
        List<DepartmentVo> departmentVoList = FeignResultUtil.success(cpDeptFeign.listDepartmentByIds(deptIdList));
        //转换成 map 使用部门 id 为 key ，path 为 value
        Map<Long, String> deptNamePath = Optional.ofNullable(departmentVoList).orElse(CollUtil.newArrayList()).stream()
                .collect(Collectors.toMap(DepartmentVo::getId, DepartmentVo::getPath, (k1, k2) -> k1));
        //循环填充数据
        for (SuggestRecordVO suggestRecordVO : suggestRecordVOS) {
            //获取当前数据的提交人
            String creator = suggestRecordVO.getCreator();
            //获取提交人用户信息
            UserInfoVo userInfoVo = userInfoVoMap.get(creator);
            //回复人信息
            String replyUserid = suggestRecordVO.getReplyUserid();
            UserInfoVo replyUserInfo = userInfoVoMap.get(replyUserid);
            if (Objects.nonNull(replyUserid) && Objects.nonNull(replyUserInfo)) {
                suggestRecordVO.setReplyUserName(replyUserInfo.getName());
            }
            UserVO userVO = new UserVO();
            userVO.setUserid(creator);
            if (Objects.nonNull(userInfoVo)) {
                userVO.setName(userInfoVo.getName());
                userVO.setDeptNamePath(deptNamePath.get(userInfoVo.getMainDepartment()));
            }
            suggestRecordVO.setSubmitUserVO(userVO);
            fillTypeName(suggestRecordVO);
        }
        return suggestRecordVOS;
    }

    private void fillTypeName(SuggestRecordVO suggestRecordVO) {
        Integer categoryType = suggestRecordVO.getCategoryType();
        if (Objects.isNull(categoryType)) {
            throw ExceptionCodeEnum.SUGGEST_CATEGORY_TYPE_NOT_EXIST.toServiceException();
        }
        //如果是应用类型
        if (categoryType == CategoryTypeEnum.APPLICATION.getCode()) {
            List<SmallCategoryVO> applicationList = suggestCategoryService.listCategoryByType(CategoryTypeEnum.APPLICATION.getCode());
            Map<Long, String> applicationMap = applicationList.stream().collect(Collectors.toMap(SmallCategoryVO::getCategoryId, SmallCategoryVO::getCategoryName, (o, n) -> n));
            suggestRecordVO.setTypeName(applicationMap.get(suggestRecordVO.getCategoryId()));
            return;
        }
        //如果是事务类型
        List<SmallCategoryVO> transactionList = suggestCategoryService.listCategoryByType(CategoryTypeEnum.TRANSACTION.getCode());
        Map<Long, String> transactionMap = transactionList.stream().collect(Collectors.toMap(SmallCategoryVO::getCategoryId, SmallCategoryVO::getCategoryName, (o, n) -> n));
        suggestRecordVO.setTypeName(transactionMap.get(suggestRecordVO.getCategoryId()));
    }
}


