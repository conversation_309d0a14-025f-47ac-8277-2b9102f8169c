package com.lanshan.app.repair.qo;

import com.lanshan.app.repair.enums.StaticTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 统计查询类
 */
@Data
@ApiModel(value = "统计查询类")
public class StaticQO {

    @ApiModelProperty(value = "统计类型 ID 集合")
    private List<String> idList;

    @ApiModelProperty(value = "统计类型 BUILDING 楼栋，WORKER 维修人，CATEGORY 类目")
    private StaticTypeEnum typeEnum;

    @ApiModelProperty(value = "统计开始时间 例如：2024-05-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @ApiModelProperty(value = "统计结束时间 例如：2024-05-01 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endDate;

}
