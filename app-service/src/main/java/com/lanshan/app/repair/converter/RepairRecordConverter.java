package com.lanshan.app.repair.converter;


import com.lanshan.app.repair.entity.RepairRecord;
import com.lanshan.app.repair.vo.RepairRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 报修类目表(RepairRecord)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface RepairRecordConverter {

    RepairRecordConverter INSTANCE = Mappers.getMapper(RepairRecordConverter.class);

    @Mapping(target = "operateLogs", ignore = true)
    RepairRecordVO toVO(RepairRecord entity);

    RepairRecord toEntity(RepairRecordVO vo);

    List<RepairRecordVO> toVO(List<RepairRecord> entityList);

    List<RepairRecord> toEntity(List<RepairRecordVO> voList);
}


