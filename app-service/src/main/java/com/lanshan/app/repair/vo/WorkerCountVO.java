package com.lanshan.app.repair.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 维修人数量统计VO
 */
@Data
@ApiModel(value = "维修人数量统计VO")
public class WorkerCountVO implements Comparable<WorkerCountVO> {

    @ApiModelProperty(value = "维修人id")
    private String workerUserid;

    @ApiModelProperty(value = "任务数量")
    private Integer taskCount;

    @Override
    public int compareTo(@NotNull WorkerCountVO o) {
        if (this.taskCount > o.taskCount) {
            return 1;
        } else if (this.taskCount < o.taskCount) {
            return -1;
        }
        return 0;
    }
}
