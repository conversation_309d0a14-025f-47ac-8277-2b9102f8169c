package com.lanshan.app.repair.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.repair.converter.RepairRecordDayCountConverter;
import com.lanshan.app.repair.entity.RepairRecordDayCount;
import com.lanshan.app.repair.service.RepairRecordDayCountService;
import com.lanshan.app.repair.vo.RepairRecordDayCountVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 报修记录日统计(RepairRecordDayCount)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/repair/repairRecordDayCount")
@Api(tags = "报修记录日统计(RepairRecordDayCount)控制层", hidden = true)
public class RepairRecordDayCountController {
    /**
     * 服务对象
     */
    @Resource
    private RepairRecordDayCountService repairRecordDayCountService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<RepairRecordDayCountVO>> selectAll(Page<RepairRecordDayCountVO> page, RepairRecordDayCountVO vo) {
        QueryWrapper<RepairRecordDayCount> queryWrapper = new QueryWrapper<>(RepairRecordDayCountConverter.INSTANCE.toEntity(vo));
        IPage<RepairRecordDayCount> pageData = this.repairRecordDayCountService.page(page.convert(RepairRecordDayCountConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(RepairRecordDayCountConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<RepairRecordDayCountVO> selectOne(@PathVariable Serializable id) {
        return Result.build(RepairRecordDayCountConverter.INSTANCE.toVO(this.repairRecordDayCountService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody RepairRecordDayCountVO vo) {
        return Result.build(this.repairRecordDayCountService.save(RepairRecordDayCountConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update/put")
    public Result<Boolean> update(@RequestBody RepairRecordDayCountVO vo) {
        return Result.build(this.repairRecordDayCountService.updateById(RepairRecordDayCountConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.repairRecordDayCountService.removeByIds(idList));
    }
}

