package com.lanshan.app.repair.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 报修类目表(RepairCategory)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "repair_category", autoResultMap = true)
public class RepairCategory extends Model<RepairCategory> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 上级节点ID
     */
    private Long parentId;
    /**
     * 类目id路径
     */
    private String idPath;
    /**
     * 类目名称路径
     */
    private String namePath;
    /**
     * 负责人用户id
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> managerUserids;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

