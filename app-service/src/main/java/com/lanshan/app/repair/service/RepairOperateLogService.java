package com.lanshan.app.repair.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.repair.entity.RepairOperateLog;
import com.lanshan.app.repair.enums.RecordOperateEnum;

/**
 * 报修操作记录表(RepairOperateLog)表服务接口
 *
 * <AUTHOR>
 */
public interface RepairOperateLogService extends IService<RepairOperateLog> {

    /**
     * 保存操作日志
     *
     * @param recordOperateEnum    操作类型
     * @param recordId             记录id
     * @param currentOperateUserid 当前操作人id
     * @param currentWorkerUserid  转办人id
     * @param transferredUserid    转办给人id
     */
    void saveOperateLog(RecordOperateEnum recordOperateEnum, Long recordId, String currentOperateUserid, String currentWorkerUserid, String transferredUserid, boolean isAdmin);
}

