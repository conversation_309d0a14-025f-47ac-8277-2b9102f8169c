package com.lanshan.app.repair.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 楼栋表(RepairBuilding)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class RepairBuilding extends Model<RepairBuilding> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 楼栋名称
     */
    private String name;
    /**
     * 楼层
     */
    private Integer floor;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

