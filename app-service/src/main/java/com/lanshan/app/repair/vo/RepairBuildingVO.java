package com.lanshan.app.repair.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 楼栋表(RepairBuilding)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "楼栋表VO")
@Data
@ToString
public class RepairBuildingVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "楼栋名称")
    private String name;

    @ApiModelProperty(value = "楼层")
    private Integer floor;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}

