package com.lanshan.app.repair.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 报修记录日统计(RepairRecordDayCount)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class RepairRecordDayCount extends Model<RepairRecordDayCount> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 楼栋id
     */
    private Long buildingId;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 用户id
     */
    private String userid;
    /**
     * 次数
     */
    private Integer count;
    /**
     * 记录日期
     */
    private Date recordDate;
    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

