package com.lanshan.app.reservation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.reservation.qo.TeamRecordsQO;
import com.lanshan.app.reservation.vo.RsvFieldVO;
import com.lanshan.app.reservation.vo.RsvTeamRecordVO;
import com.lanshan.app.reservation.vo.VenuePreviewImageBindVO;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface RsvTeamManageService {
    /**
     * 分页查询团队预约记录
     *
     * @param qo 查询条件
     * @return 分页结果
     */
    IPage<RsvTeamRecordVO> page(TeamRecordsQO qo);

    /**
     * 查询团队预约记录详情
     *
     * @param id 记录ID
     * @return 记录详情
     */
    RsvTeamRecordVO detail(Long id);

    /**
     * 查询场地场地列表
     *
     * @param page 页码
     * @param size 页大小
     * @param id   场地ID
     * @return 场地场地列表分页
     */
    IPage<RsvFieldVO> stadiumFieldList(Integer page, Integer size, Long id);

    /**
     * 场馆场地新增/编辑
     *
     * @param fieldVO 场地信息
     * @return 操作结果
     */
    Boolean venueFieldOperate(RsvFieldVO fieldVO);

    /**
     * 场地场地删除
     *
     * @param id 场地ID
     * @return 操作结果
     */
    Boolean venueFieldDelete(Long id);

    /**
     * 场地预览图绑定
     *
     * @param bindVO 绑定信息
     * @return 操作结果
     */
    Boolean venuePreviewImageBind(VenuePreviewImageBindVO bindVO);

    /**
     * 场地启用/禁用团队预约
     *
     * @param venueId 场地ID
     * @param enable
     * @return 操作结果
     */
    Boolean venueEnableOrDisableTeam(Long venueId, boolean enable);
}
