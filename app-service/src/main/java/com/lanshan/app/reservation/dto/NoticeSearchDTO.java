package com.lanshan.app.reservation.dto;

import com.lanshan.app.common.bo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "公告搜索DTO")
public class NoticeSearchDTO extends PageQo implements Serializable {
    private static final long serialVersionUID = -7282590100407340330L;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "预约创建时间开始。格式：YYYY-MM-DD")
    private String createTimeStart;

    @ApiModelProperty(value = "预约创建时间结束。格式：YYYY-MM-DD")
    private String createTimeEnd;

    public NoticeSearchDTO() {
    }

    public NoticeSearchDTO(PageQo page) {
        this.setPage(page.getPage());
        this.setSize(page.getSize());
    }
}
