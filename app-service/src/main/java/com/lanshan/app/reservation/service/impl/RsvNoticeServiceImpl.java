package com.lanshan.app.reservation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.reservation.converter.RsvNoticeConverter;
import com.lanshan.app.reservation.dao.RsvNoticeDao;
import com.lanshan.app.reservation.dto.NoticeSearchDTO;
import com.lanshan.app.reservation.entity.RsvNotice;
import com.lanshan.app.reservation.service.RsvNoticeService;
import com.lanshan.app.reservation.vo.RsvNoticeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (RsvNotice)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvNoticeService")
public class RsvNoticeServiceImpl extends ServiceImpl<RsvNoticeDao, RsvNotice> implements RsvNoticeService {

    /**
     * 分页查询所有数据
     *
     * @param dto 查询条件
     * @return 对象列表
     */
    @Override
    public IPage<RsvNoticeVO> getPage(NoticeSearchDTO dto) {
        Page<RsvNotice> page = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<RsvNotice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvNotice::getIsDeleted, false);
        wrapper.like(StringUtils.isNotEmpty(dto.getTitle()), RsvNotice::getTitle, dto.getTitle());
        wrapper.ge(StringUtils.isNotEmpty(dto.getCreateTimeStart()), RsvNotice::getCreateTime, dto.getCreateTimeStart());
        wrapper.le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), RsvNotice::getCreateTime, dto.getCreateTimeEnd());
        wrapper.orderByDesc(RsvNotice::getCreateTime);
        IPage<RsvNotice> data = this.page(page, wrapper);
        return data.convert(RsvNoticeConverter.INSTANCE::toVO);
    }

    /**
     * 获取最近公告
     *
     * @return List<RsvNoticeVO>
     */
    @Override
    public List<RsvNoticeVO> getRecentList() {
        LambdaQueryWrapper<RsvNotice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvNotice::getIsDeleted, false);
        wrapper.orderByDesc(RsvNotice::getCreateTime);
        wrapper.last("limit 2");
        List<RsvNotice> list = this.list(wrapper);
        return RsvNoticeConverter.INSTANCE.toVO(list);
    }
}

