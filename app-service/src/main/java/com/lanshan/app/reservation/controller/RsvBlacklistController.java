package com.lanshan.app.reservation.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.common.bo.Result;
import com.lanshan.app.reservation.converter.RsvBlacklistConverter;
import com.lanshan.app.reservation.dto.BlacklistSearchDTO;
import com.lanshan.app.reservation.entity.RsvBlacklist;
import com.lanshan.app.reservation.service.RsvBlacklistService;
import com.lanshan.app.reservation.vo.RsvBlacklistVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 场馆预约-黑名单管理API控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("manage/reservation/blacklist")
@Api(tags = "场馆预约-黑名单管理API", hidden = true)
public class RsvBlacklistController {
    /**
     * 服务对象
     */
    @Resource
    private RsvBlacklistService rsvBlacklistService;

    /**
     * 分页查询黑名单数据
     *
     * @param dto 查询条件
     * @return 黑名单数据
     */
    @ApiOperation("分页查询黑名单数据")
    @GetMapping
    public Result<IPage<RsvBlacklistVO>> selectAll(BlacklistSearchDTO dto) {
        return Result.build(this.rsvBlacklistService.getPage(dto));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<RsvBlacklistVO> selectOne(@PathVariable Serializable id) {
        return Result.build(RsvBlacklistConverter.INSTANCE.toVO(this.rsvBlacklistService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody RsvBlacklistVO vo) {
        RsvBlacklist entity = RsvBlacklistConverter.INSTANCE.toEntity(vo);
        entity.setCreateTime(new Date());
        return Result.build(this.rsvBlacklistService.save(entity));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody RsvBlacklistVO vo) {
        return Result.build(this.rsvBlacklistService.updateById(RsvBlacklistConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.rsvBlacklistService.removeByIds(idList));
    }
}

