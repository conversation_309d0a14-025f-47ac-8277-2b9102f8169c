package com.lanshan.app.reservation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.reservation.converter.RsvVenueConverter;
import com.lanshan.app.reservation.dao.RsvVenueDao;
import com.lanshan.app.reservation.entity.RsvVenue;
import com.lanshan.app.reservation.service.RsvVenueService;
import com.lanshan.app.reservation.vo.RsvVenueVO;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
 * 场馆信息表(RsvVenue)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvVenueService")
public class RsvVenueServiceImpl extends ServiceImpl<RsvVenueDao, RsvVenue> implements RsvVenueService {

    /**
     * 获取所有开放预约的场馆信息
     *
     * @return List<RsvVenueVO>
     */
    @Override
    public List<RsvVenueVO> getOpenVenueList() {
        // 使用LambdaQueryWrapper,查询RsvVenue表所有开放预约的场馆，并排序
        LambdaQueryWrapper<RsvVenue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsvVenue::getIsDeleted, false);
        queryWrapper.orderByAsc(RsvVenue::getSort);
        List<RsvVenue> list = super.list(queryWrapper);
        return RsvVenueConverter.INSTANCE.toVO(list);
    }

    /**
     * 切换场馆的开放状态
     *
     * @param id 场馆id
     * @return Boolean
     */
    @Override
    public Boolean switchOpen(Serializable id) {
        RsvVenue venue = super.getById(id);
        venue.setIsOpen(!venue.getIsOpen());
        return super.updateById(venue);
    }
}

