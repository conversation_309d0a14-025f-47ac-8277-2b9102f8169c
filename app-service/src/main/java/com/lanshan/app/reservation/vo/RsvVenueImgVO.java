package com.lanshan.app.reservation.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 场馆图片表(RsvVenueImg)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "场馆图片表VO")
@Data
@ToString
public class RsvVenueImgVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "图片名称")
    private String name;

    @ApiModelProperty(value = "场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "")
    private Boolean status;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "排序号")
    private Integer orderNo;
}

