package com.lanshan.app.reservation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.reservation.entity.RsvVenueFieldType;
import com.lanshan.app.reservation.vo.RsvVenueFieldTypeVO;

/**
 * 场馆预约场地类型表(RsvVenueFieldType)表服务接口
 *
 * <AUTHOR>
 */
public interface RsvVenueFieldTypeService extends IService<RsvVenueFieldType> {

    /**
     * 保存数据
     *
     * @param vo
     * @return
     */
    Boolean save(RsvVenueFieldTypeVO vo);

    /**
     * 更新数据
     *
     * @param vo
     * @return
     */
    Boolean update(RsvVenueFieldTypeVO vo);
}

