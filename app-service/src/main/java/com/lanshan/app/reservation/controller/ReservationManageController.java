package com.lanshan.app.reservation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.common.bo.Result;
import com.lanshan.app.reservation.dto.*;
import com.lanshan.app.reservation.service.ReservationManageService;
import com.lanshan.app.reservation.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 场馆预约管理端控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("manage/reservation")
@Api(tags = "场馆预约-预约管理端API", hidden = true)
@RequiredArgsConstructor
public class ReservationManageController {

    private final ReservationManageService reservationManageService;

    @ApiOperation("场馆列表")
    @GetMapping("venue/list")
    public Result<List<RsvVenueVO>> getVenueList() {
        return Result.build(reservationManageService.getVenueList());
    }

    @ApiOperation("获取场馆可预约时间段配置")
    @GetMapping("venue/time/config/list")
    public Result<List<RsvVenueTimeConfigVO>> getVenueTimeConfigList(@ApiParam(value = "场馆ID", required = true) @RequestParam Serializable id) {
        return Result.build(reservationManageService.getVenueTimeConfigList(id));
    }

    @ApiOperation("新增场馆可预约时间段配置")
    @PostMapping("venue/time/config")
    public Result<Boolean> createVenueTimeConfig(@RequestBody @Valid RsvVenueTimeConfigVO dto) {
        return Result.build(reservationManageService.createVenueTimeConfig(dto));
    }

    @ApiOperation("删除场馆可预约时间段配置")
    @PostMapping("venue/time/config/delete")
    public Result<Boolean> removeVenueTimeConfig(@RequestParam @ApiParam(value = "时间段ID", required = true) Serializable id) {
        return Result.build(reservationManageService.removeVenueTimeConfig(id));
    }

    @ApiOperation("获取时间段列表")
    @GetMapping("venue/time/period/list")
    public Result<Set<String>> getTimePeriodList(@ApiParam(value = "场馆ID") @RequestParam(required = false) Serializable id) {
        return Result.build(reservationManageService.getTimePeriodList(id));
    }

    @ApiOperation("保存场馆配置")
    @PostMapping("venue/config")
    public Result<Boolean> saveVenueConfig(@RequestBody ReservationConfigDTO dto) {
        return Result.build(reservationManageService.saveVenueConfig(dto));
    }

    /**
     * 开放、关闭预约场馆
     */
    @ApiOperation("开启/关闭场馆预约")
    @GetMapping("venue/open/switch")
    public Result<Boolean> switchVenueOpen(@RequestParam @ApiParam(value = "场馆ID", required = true) Serializable id) {
        return Result.build(reservationManageService.switchVenueOpen(id));
    }

    @ApiOperation("查询场地列表")
    @GetMapping("field/list")
    public Result<List<RsvFieldVO>> getFieldList(@RequestParam @ApiParam(value = "场馆ID", required = true) Serializable id) {
        return Result.build(reservationManageService.getFieldList(id));
    }

    @ApiOperation("获取场地类型列表")
    @GetMapping("field/type/list")
    public Result<List<Pair<Long, String>>> getFieldTypeList() {
        return Result.build(reservationManageService.getFieldTypeList());
    }


    /**
     * 新增场馆场地
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增场地")
    @PostMapping("field")
    public Result<Boolean> insert(@RequestBody RsvFieldVO vo) {
        return Result.build(this.reservationManageService.saveField(vo));
    }

    /**
     * 修改场地信息
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("field/put")
    @ApiOperation("编辑场地")
    public Result<Boolean> update(@RequestBody RsvFieldVO vo) {
        return Result.build(this.reservationManageService.updateField(vo));
    }

    /**
     * 删除场地信息
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("field/delete")
    @ApiOperation("删除场地")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.reservationManageService.removeField(idList));
    }

    @ApiOperation("分页查询预约对象信息")
    @GetMapping("object/list")
    public Result<IPage<RsvReserveObjectVO>> getReserveObjectList(ReserveObjectSearchDTO dto) {
        return Result.build(this.reservationManageService.getReserveObjectList(dto));
    }

    @ApiOperation("开启/禁用预约对象")
    @GetMapping("object/switch")
    public Result<Boolean> switchReserveObjectEnable(@RequestParam Serializable id) {
        return Result.build(this.reservationManageService.switchReserveObjectEnable(id));
    }

    @ApiOperation("批量开启/禁用预约对象")
    @PostMapping("object/switch/batch")
    public Result<Boolean> switchReserveObjectEnable(@RequestBody @Valid RsvObjectEnableDTO dto) {
        return Result.build(this.reservationManageService.switchReserveObjectEnable(dto));
    }

    @ApiOperation("分页查询场馆预约记录")
    @GetMapping("record/page")
    public Result<IPage<RsvReserveRecordVO>> getReserveRecordPage(ReserveRecordSearchDTO dto) {
        return Result.build(this.reservationManageService.getReserveRecordPage(dto));
    }

    @ApiOperation("获取用户预约配置")
    @GetMapping("user/config")
    public Result<UserReserveConfigDTO> getUserReserveConfig() {
        return Result.build(this.reservationManageService.getUserReserveConfig());
    }

    @ApiOperation("保存用户预约配置")
    @PostMapping("user/config")
    public Result<Boolean> saveUserReserveConfig(@RequestBody UserReserveConfigDTO dto) {
        return Result.build(this.reservationManageService.saveUserReserveConfig(dto));
    }

    @ApiOperation("根据用户场地使用记录更新预约状态")
    @PostMapping("record/status/used")
    public Result<Boolean> updateReserveRecordUsed(@RequestBody ReserveRecordUseDTO dto) {
        return Result.build(this.reservationManageService.updateReserveRecordUsed(dto));
    }

    @ApiOperation("编辑场馆")
    @PostMapping("/venue/update")
    public Result<Boolean> update(@RequestBody @Valid RsvVenueVO vo) {
        return Result.build(this.reservationManageService.updateVenue(vo));
    }

    @ApiOperation("获取场地类型列表V2")
    @GetMapping("field/type/listV2")
    public Result<List<RsvVenueFieldTypeVO>> getFieldTypeListV2() {
        return Result.build(reservationManageService.getFieldTypeListV2());
    }
}
