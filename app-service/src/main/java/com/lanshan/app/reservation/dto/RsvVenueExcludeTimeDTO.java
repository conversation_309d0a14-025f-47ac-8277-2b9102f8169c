package com.lanshan.app.reservation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "场馆预约排除时间表DTO")
public class RsvVenueExcludeTimeDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "1 time 2 lesson")
    private Integer type;

    @ApiModelProperty(value = "配置表主键")
    private Long configId;

    @ApiModelProperty(value = "排除日期，多个用英文“,”分割")
    private String excludeDate;

    @ApiModelProperty(value = "场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "开放时间")
    private String openTime;
}
