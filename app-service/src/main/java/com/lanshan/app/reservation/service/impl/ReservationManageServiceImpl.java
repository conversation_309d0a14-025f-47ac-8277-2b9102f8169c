package com.lanshan.app.reservation.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.app.common.constant.CommonConstant;
import com.lanshan.app.common.entity.SysConfig;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.config.manager.AsyncManager;
import com.lanshan.app.reservation.converter.RsvVenueConverter;
import com.lanshan.app.reservation.converter.RsvVenueFieldTypeConverter;
import com.lanshan.app.reservation.converter.RsvVenueTimeConfigConverter;
import com.lanshan.app.reservation.dto.*;
import com.lanshan.app.reservation.entity.*;
import com.lanshan.app.reservation.enums.FieldTypeEnum;
import com.lanshan.app.reservation.enums.ReserveStatusEnum;
import com.lanshan.app.reservation.service.*;
import com.lanshan.app.reservation.vo.*;
import com.lanshan.base.api.enums.MsgChannelEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.feign.message.MsgCenterFeign;
import com.lanshan.base.api.qo.message.MsgSaveQo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 场馆预约管理端Service实现类
 *
 * <AUTHOR>
 */
@Service("reservationManageService")
@RequiredArgsConstructor
@Slf4j
public class ReservationManageServiceImpl implements ReservationManageService {

    private static final String noticeTemplate = "您的场馆预约（%s %s %s %s），因场地开放时间段调整，被系统取消，请知悉。";
    private final RsvReserveRecordService rsvReserveRecordService;
    private final RsvVenueService rsvVenueService;
    private final RsvFieldService rsvFieldService;
    private final RsvVenueTimeConfigService rsvVenueTimeConfigService;
    private final RsvReserveObjectService rsvReserveObjectService;
    private final ISysConfigService sysConfigService;
    private final MsgCenterFeign msgCenterFeign;
    private final RsvVenueFieldTypeService rsvVenueFieldTypeService;

    /**
     * 获取场馆列表
     *
     * @return 场馆列表
     */
    @Override
    public List<RsvVenueVO> getVenueList() {
        return RsvVenueConverter.INSTANCE.toVO(rsvVenueService.list(Wrappers.<RsvVenue>lambdaQuery().orderByDesc(RsvVenue::getCreateTime)));
    }

    /**
     * 切换场馆是否开放预约
     *
     * @param id 场馆ID
     * @return 是否成功
     */
    @Override
    public Boolean switchVenueOpen(Serializable id) {
        return rsvVenueService.switchOpen(id);
    }

    /**
     * 获取场地列表
     *
     * @param id 场馆ID
     * @return 场地列表
     */
    @Override
    public List<RsvFieldVO> getFieldList(Serializable id) {
        return rsvFieldService.getByVenueId(id);
    }

    /**
     * 保存场地信息
     *
     * @param vo 场地信息
     * @return 是否成功
     */
    @Override
    public Boolean saveField(RsvFieldVO vo) {
        return rsvFieldService.create(vo);
    }

    /**
     * 更新场地信息
     *
     * @param vo 场地信息
     * @return 是否成功
     */
    @Override
    public Boolean updateField(RsvFieldVO vo) {
        return rsvFieldService.update(vo);
    }

    /**
     * 删除场地信息
     *
     * @param idList 场地ID列表
     * @return 是否成功
     */
    @Override
    public Boolean removeField(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return Boolean.FALSE;
        }
        // 场地软删除
        return rsvFieldService.update(new LambdaUpdateWrapper<RsvField>().set(RsvField::getIsDeleted, Boolean.TRUE).in(RsvField::getId, idList));
    }

    /**
     * 获取场地可预约时间段配置
     *
     * @param id 场馆ID
     * @return 场地可预约时间段配置
     */
    @Override
    public List<RsvVenueTimeConfigVO> getVenueTimeConfigList(Serializable id) {
        return rsvVenueTimeConfigService.getByVenueId(id);
    }

    /**
     * 保存场馆配置信息
     *
     * @param dto 场馆配置信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveVenueConfig(ReservationConfigDTO dto) {
        RsvVenue venue = rsvVenueService.getById(dto.getVenueId());
        venue.setDayWeekStudent(StringUtils.join(dto.getDayWeekStudent(), ","));
        venue.setDayWeekTeacher(StringUtils.join(dto.getDayWeekTeacher(), ","));
        venue.setTeamDayWeekTeacher(StringUtils.join(dto.getTeamDayWeekTeacher(), ","));
        venue.setPreBookDay(dto.getPreBookDay());
        venue.setNotice(dto.getNotice());
        venue.setUpdateBy(SecurityContextHolder.getUserIdStr());
        venue.setUpdateTime(new Date());
        rsvVenueService.updateById(venue);

        return Boolean.TRUE;
    }

    /**
     * 获取预约对象列表
     *
     * @param dto 预约对象搜索DTO
     * @return 预约对象列表
     */
    @Override
    public IPage<RsvReserveObjectVO> getReserveObjectList(ReserveObjectSearchDTO dto) {
        return rsvReserveObjectService.getPage(dto);
    }

    /**
     * 切换预约对象是否可用
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    @Override
    public Boolean switchReserveObjectEnable(Serializable id) {
        RsvReserveObject reserveObject = rsvReserveObjectService.getById(id);
        if (reserveObject == null) {
            return Boolean.FALSE;
        }
        RsvObjectEnableDTO dto = new RsvObjectEnableDTO();
        dto.setIds(Collections.singletonList(id));
        dto.setEnable(!reserveObject.getIsEnable());
        switchReserveObjectEnable(dto);
        return Boolean.TRUE;
    }

    /**
     * 批量切换预约对象是否可用
     *
     * @param dto 预约对象ID列表
     * @return 是否成功
     */
    @Override
    public Boolean switchReserveObjectEnable(RsvObjectEnableDTO dto) {

        if (CollectionUtils.isEmpty(dto.getIds())) {
            return Boolean.FALSE;
        }
        rsvReserveObjectService.switchEnable(dto);

        if (Boolean.FALSE.equals(dto.getEnable())) {
            List<RsvReserveRecord> reserveRecordList = rsvReserveRecordService.list(new LambdaQueryWrapper<RsvReserveRecord>()
                    .in(RsvReserveRecord::getObjectId, dto.getIds())
                    .eq(RsvReserveRecord::getStatus, ReserveStatusEnum.RESERVED.getCode()));

            if (CollectionUtils.isNotEmpty(reserveRecordList)) {
                reserveRecordList.forEach(rsvReserveRecord -> {
                    rsvReserveRecord.setStatus(ReserveStatusEnum.CANCEL.getCode());
                    rsvReserveRecord.setUpdateTime(new Date());
                });
                rsvReserveRecordService.updateBatchById(reserveRecordList);
                AsyncManager.me().execute(new TimerTask() {
                    @Override
                    public void run() {
                        sendCancelMsg(reserveRecordList);
                    }
                });
            }
        }
        return Boolean.TRUE;
    }

    private void sendCancelMsg(List<RsvReserveRecord> reserveRecordList) {
        Map<Long, List<RsvReserveRecord>> groupByObjectId = reserveRecordList.stream().collect(Collectors.groupingBy(RsvReserveRecord::getObjectId));
        Long reservationAppId = Long.valueOf(sysConfigService.selectConfigByKey("reservation.app.id"));

        for (Map.Entry<Long, List<RsvReserveRecord>> entry : groupByObjectId.entrySet()) {
            List<String> userIds = entry.getValue().stream().map(RsvReserveRecord::getUserId).collect(Collectors.toList());
            RsvReserveRecord record = entry.getValue().get(0);
            MsgSaveQo msgSaveQo = new MsgSaveQo();
            msgSaveQo.setType(MsgTypeEnum.TEXTCARD.getValue());
            msgSaveQo.setTitle("您的场馆预约被取消！");
            msgSaveQo.setDescription(String.format(noticeTemplate, record.getVenueName(), record.getFieldName(), DateUtil.format(record.getUseDate(), DatePattern.NORM_DATE_PATTERN), record.getUseTime()));
            msgSaveQo.setAppId(reservationAppId);
            msgSaveQo.setLinkAppId(reservationAppId);
            msgSaveQo.setPublishType(1);
            msgSaveQo.setPublisher(SecurityContextHolder.getUserName());
            msgSaveQo.setUserList(userIds);
            msgSaveQo.setPublishChannel(List.of(MsgChannelEnum.WEIXIN_CORP_CHANNEL.name()));
            msgSaveQo.setIsMustRead(YnEnum.NO.getValue());
            msgSaveQo.setSource(1);
            msgCenterFeign.saveMsg(msgSaveQo);
        }
    }

    /**
     * 创建场地可预约时间段配置
     *
     * @param dto 包含创建信息的场地可预约时间段配置数据传输对象
     * @return 创建是否成功的布尔值
     */
    @Override
    public Boolean createVenueTimeConfig(RsvVenueTimeConfigVO dto) {
        RsvVenueTimeConfig entity = RsvVenueTimeConfigConverter.INSTANCE.toEntity(dto);
        return rsvVenueTimeConfigService.save(entity);
    }

    /**
     * 删除场地可预约时间段配置, 同时将该时间段内的预约对象标记为不可用
     *
     * @param id 场地可预约时间段配置ID
     * @return 删除是否成功的布尔值
     */
    @Override
    public Boolean removeVenueTimeConfig(Serializable id) {
        boolean removed = rsvVenueTimeConfigService.removeById(id);
        if (removed) {
            List<RsvReserveObject> objectList = rsvReserveObjectService.list(new LambdaQueryWrapper<RsvReserveObject>()
                    .eq(RsvReserveObject::getTimeConfigId, id)
                    .gt(RsvReserveObject::getOpenDay, new Date())
            );
            List<Serializable> objectIdList = objectList.stream().map(RsvReserveObject::getId).collect(Collectors.toList());
            switchReserveObjectEnable(RsvObjectEnableDTO.builder().ids(objectIdList).enable(Boolean.FALSE).build());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateVenue(RsvVenueVO vo) {
        return rsvVenueService.updateById(RsvVenueConverter.INSTANCE.toEntity(vo));
    }

    @Override
    public List<RsvVenueFieldTypeVO> getFieldTypeListV2() {
        List<RsvVenueFieldType> venueFieldTypeList = rsvVenueFieldTypeService.list(Wrappers.lambdaQuery(RsvVenueFieldType.class)
                .eq(RsvVenueFieldType::getStatus, Boolean.TRUE)
                .eq(RsvVenueFieldType::getDeleteFlag, Boolean.FALSE)
        );
        return RsvVenueFieldTypeConverter.INSTANCE.toVO(venueFieldTypeList);
    }

    /**
     * 获取预约记录列表
     *
     * @param dto 预约记录搜索DTO
     * @return 预约记录列表
     */
    @Override
    public IPage<RsvReserveRecordVO> getReserveRecordPage(ReserveRecordSearchDTO dto) {
        return rsvReserveRecordService.getReserveRecordPage(dto);
    }

    /**
     * 获取用户预约配置信息
     *
     * @return 用户预约配置信息
     */
    @Override
    public UserReserveConfigDTO getUserReserveConfig() {
        String dayLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_DAY_LIMIT_KEY);
        String weekLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_WEEK_LIMIT_KEY);
        String breakLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_MONTH_BREAK_LIMIT_KEY);
        UserReserveConfigDTO dto = new UserReserveConfigDTO();
        dto.setMonthBreakLimit(Integer.valueOf(breakLimit));
        dto.setDayLimitCount(Integer.valueOf(dayLimit));
        dto.setWeekLimitCount(Integer.valueOf(weekLimit));
        return dto;
    }

    /**
     * 保存用户预约配置信息
     *
     * @param dto 用户预约配置信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveUserReserveConfig(UserReserveConfigDTO dto) {
        sysConfigService.updateConfig(SysConfig.builder().configKey(CommonConstant.RSV_DAY_LIMIT_KEY).configValue(String.valueOf(dto.getDayLimitCount())).build());
        sysConfigService.updateConfig(SysConfig.builder().configKey(CommonConstant.RSV_WEEK_LIMIT_KEY).configValue(String.valueOf(dto.getWeekLimitCount())).build());
        sysConfigService.updateConfig(SysConfig.builder().configKey(CommonConstant.RSV_MONTH_BREAK_LIMIT_KEY).configValue(String.valueOf(dto.getMonthBreakLimit())).build());
        return Boolean.TRUE;
    }

    /**
     * 获取可预约时间段列表
     *
     * @return 可预约时间段列表
     */
    @Override
    public Set<String> getTimePeriodList(Serializable id) {
        return rsvVenueTimeConfigService.getTimePeriodList(id);
    }

    /**
     * 获取场地类型列表
     *
     * @return 场地类型列表
     */
    @Override
    public List<Pair<Long, String>> getFieldTypeList() {
        List<Pair<Long, String>> result = new ArrayList<>();
        for (FieldTypeEnum fieldTypeEnum : FieldTypeEnum.values()) {
            result.add(Pair.of(fieldTypeEnum.getCode(), fieldTypeEnum.getDesc()));
        }
        return result;
    }

    /**
     * 更新预定记录使用情况的方法
     *
     * @param dto 包含更新信息的预定记录使用数据传输对象
     * @return 更新是否成功的布尔值
     */
    @Override
    public Boolean updateReserveRecordUsed(ReserveRecordUseDTO dto) {
        return rsvReserveRecordService.updateReserveRecordUsed(dto);
    }
}
