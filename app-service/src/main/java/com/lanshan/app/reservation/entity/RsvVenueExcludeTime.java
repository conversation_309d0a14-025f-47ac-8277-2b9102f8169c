package com.lanshan.app.reservation.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 场馆预约排除时间表(RsvVenueExcludeTime)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class RsvVenueExcludeTime extends Model<RsvVenueExcludeTime> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 1 time 2 lesson
     */
    private Integer type;
    /**
     * 配置表主键
     */
    private Long configId;
    /**
     * 排除日期
     */
    private Date excludeDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

