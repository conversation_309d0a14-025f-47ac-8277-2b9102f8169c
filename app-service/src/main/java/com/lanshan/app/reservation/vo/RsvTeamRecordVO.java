package com.lanshan.app.reservation.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 场馆团队预约信息(RsvTeamRecord)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "场馆团队预约信息VO")
@Data
@ToString
public class RsvTeamRecordVO implements Serializable {

    private static final long serialVersionUID = -4391679206137630463L;
    @ApiModelProperty(value = "主键。审批流单号")
    private Long id;

    @ApiModelProperty(value = "场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "预约人ID")
    private String userId;

    @ApiModelProperty(value = "预约人姓名")
    private String userName;

    @ApiModelProperty(value = "预约类型。1：单次预约；2：周期重复")
    private Integer type;

    @ApiModelProperty(value = "预约开始时间。格式：YYYY-MM-DD HH:mm")
    private String startDate;

    @ApiModelProperty(value = "预约结束时间.。格式：YYYY-MM-DD HH:mm")
    private String endDate;

    @ApiModelProperty(value = "一周的第几天。周日为第0天. 多个半角逗号分隔")
    private String dayOfWeek;

    @ApiModelProperty(value = "预约时间段开始。格式：HH:mm")
    private String startTime;

    @ApiModelProperty(value = "预约时间段结束。格式：HH:mm")
    private String endTime;

    @ApiModelProperty(value = "预约状态。0：待提交；1：已提交，审批中；2：已通过审批；3-已驳回；4-已转审；9：取消预约")
    private Integer status;

    @ApiModelProperty(value = "创建时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "场馆名称")
    private String venueName;

    @ApiModelProperty(value = "场地设计图")
    private String designImgUrl;

    @ApiModelProperty(value = "预约场地原因。")
    private String reason;

    @ApiModelProperty(value = "团体预约场地信息")
    private List<RsvFieldVO> teamRecordFieldVOs;

    @ApiModelProperty(value = "审批信息")
    private List<RsvTeamRecordApprovalVO> approvalList;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "团体预约节次信息")
    private List<RsvTeamRecordLessonVO> lessonList;

    @ApiModelProperty(value = "校区名称")
    private String areaCode;
}

