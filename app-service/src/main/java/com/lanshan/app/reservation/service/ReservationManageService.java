package com.lanshan.app.reservation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.reservation.dto.*;
import com.lanshan.app.reservation.vo.*;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 场馆预约管理端Service API
 *
 * <AUTHOR>
 */
public interface ReservationManageService {

    /**
     * 获取场馆列表
     *
     * @return 场馆列表
     */
    List<RsvVenueVO> getVenueList();

    /**
     * 切换场馆是否开放预约
     *
     * @param id 场馆ID
     * @return 是否成功
     */
    Boolean switchVenueOpen(Serializable id);

    /**
     * 获取场地列表
     *
     * @param id 场馆ID
     * @return 场地列表
     */
    List<RsvFieldVO> getFieldList(Serializable id);

    /**
     * 保存场地信息
     *
     * @param vo 场地信息
     * @return 是否成功
     */
    Boolean saveField(RsvFieldVO vo);

    /**
     * 更新场地信息
     *
     * @param vo 场地信息
     * @return 是否成功
     */
    Boolean updateField(RsvFieldVO vo);

    /**
     * 删除场地信息
     *
     * @param idList 场地ID列表
     * @return 是否成功
     */
    Boolean removeField(List<Long> idList);

    /**
     * 获取场地可预约时间段配置
     *
     * @param id 场馆ID
     * @return 场地可预约时间段配置
     */
    List<RsvVenueTimeConfigVO> getVenueTimeConfigList(Serializable id);

    /**
     * 保存场馆配置信息
     *
     * @param dto 场馆配置信息
     * @return 是否成功
     */
    Boolean saveVenueConfig(ReservationConfigDTO dto);

    /**
     * 获取预约对象列表
     *
     * @param dto 预约对象搜索DTO
     * @return 预约对象列表
     */
    IPage<RsvReserveObjectVO> getReserveObjectList(ReserveObjectSearchDTO dto);

    /**
     * 切换预约对象是否可用
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    Boolean switchReserveObjectEnable(Serializable id);

    /**
     * 获取预约记录列表
     *
     * @param dto 预约记录搜索DTO
     * @return 预约记录列表
     */
    IPage<RsvReserveRecordVO> getReserveRecordPage(ReserveRecordSearchDTO dto);

    /**
     * 获取用户预约配置信息
     *
     * @return 用户预约配置信息
     */
    UserReserveConfigDTO getUserReserveConfig();

    /**
     * 保存用户预约配置信息
     *
     * @param dto 用户预约配置信息
     * @return 是否成功
     */
    Boolean saveUserReserveConfig(UserReserveConfigDTO dto);

    /**
     * 获取可预约时间段列表
     *
     * @param id 场馆ID
     * @return 可预约时间段列表
     */
    Set<String> getTimePeriodList(Serializable id);

    /**
     * 获取场地类型列表
     *
     * @return 场地类型列表
     */
    List<Pair<Long, String>> getFieldTypeList();

    /**
     * 更新预定记录使用情况的方法
     *
     * @param dto 包含更新信息的预定记录使用数据传输对象
     * @return 更新是否成功的布尔值
     */
    Boolean updateReserveRecordUsed(ReserveRecordUseDTO dto);

    /**
     * 批量切换预约对象是否可用
     *
     * @param dto 预约对象ID列表
     * @return 是否成功
     */
    Boolean switchReserveObjectEnable(RsvObjectEnableDTO dto);

    /**
     * 创建场地可预约时间段配置
     *
     * @param dto 包含创建信息的场地可预约时间段配置数据传输对象
     * @return 创建是否成功的布尔值
     */
    Boolean createVenueTimeConfig(RsvVenueTimeConfigVO dto);

    /**
     * 删除场地可预约时间段配置, 同时将该时间段内的预约对象标记为不可用
     *
     * @param id 场地可预约时间段配置ID
     * @return 删除是否成功的布尔值
     */
    Boolean removeVenueTimeConfig(Serializable id);

    /**
     * 更新场馆信息
     *
     * @param vo 包含更新信息的场馆数据传输对象
     * @return 更新是否成功的布尔值
     */
    Boolean updateVenue(@Valid RsvVenueVO vo);

    /**
     * 获取场地类型列表
     *
     * @return 场地类型列表
     */
    List<RsvVenueFieldTypeVO> getFieldTypeListV2();
}
