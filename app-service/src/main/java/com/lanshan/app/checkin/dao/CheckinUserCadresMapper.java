package com.lanshan.app.checkin.dao;

import com.lanshan.app.checkin.po.CheckinUserCadres;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 学生干部 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-30
 */
@Mapper
public interface CheckinUserCadresMapper extends BaseMapper<CheckinUserCadres> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinUserCadres> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CheckinUserCadres> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinUserCadres> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CheckinUserCadres> entities);

    @Select("select count(user_id) from standard_app.checkin_user_cadres where user_id = #{userId}")
    int judgeWhetherCadres(@Param("userId") String userId);

    @Select("select dept_id from standard_app.checkin_user_cadres where user_id = #{userId}")
    List<Long> getCadresByDeptIds(@Param("userId")String userId);

    List<CheckinUserCadres> getCadresWithGrade();
}
