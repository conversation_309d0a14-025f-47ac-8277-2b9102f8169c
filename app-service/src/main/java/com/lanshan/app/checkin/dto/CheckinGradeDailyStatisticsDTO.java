package com.lanshan.app.checkin.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * 部门统计DTO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CheckinGradeDailyStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -3067440235030398987L;

    private Collection<Long> deptIds;

    private Date startTime;

    private Date endTime;

}
