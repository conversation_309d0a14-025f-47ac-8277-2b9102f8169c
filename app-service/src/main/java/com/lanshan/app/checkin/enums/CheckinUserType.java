package com.lanshan.app.checkin.enums;

import com.lanshan.app.checkin.vo.CheckinEnumTypeVO;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

public enum CheckinUserType {

    STUDENT_CADRES(1,"学生干部"),
    COUNSELOR(2,"辅导员"),
    LEAN_WORK(3,"学工部"),
    FACULTY(4,"院系");

    public final Integer value;
    public final String label;

    CheckinUserType(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getName(Integer value) {
        for (CheckinUserType cardStatus : CheckinUserType.values()) {
            if (cardStatus.value.equals(value)) {
                return cardStatus.label;
            }
        }
        return "";
    }

    public CheckinEnumTypeVO toVO() {
        return new CheckinEnumTypeVO(this.value, this.label);
    }

    public static List<CheckinEnumTypeVO> getAllCheckinUserType() {
        return Arrays.stream(CheckinUserType.values())
                .map(CheckinUserType::toVO)
                .collect(Collectors.toList());
    }

}
