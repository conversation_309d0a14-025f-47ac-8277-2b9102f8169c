package com.lanshan.app.checkin.service;

import com.lanshan.app.checkin.dto.CheckinDailyDTO;
import com.lanshan.app.checkin.dto.CheckinDailyUserDTO;
import com.lanshan.app.checkin.dto.CheckinUserStatusDTO;
import com.lanshan.app.checkin.dto.MessageUnCheckinUserDTO;
import com.lanshan.app.checkin.po.CheckinUserDaily;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.checkin.vo.CheckinDailyVO;
import com.lanshan.app.checkin.vo.CheckinUserDailyExcelVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 用户打卡每日统计表 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-27
 */
public interface CheckinUserDailyService extends IService<CheckinUserDaily> {

    boolean insertBatch(List<CheckinUserDaily> list);

    boolean insertOrUpdateBatch(List<CheckinUserDaily> list);

    /**
     * 获取班级 打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/29 17:15
     */
    List<CheckinUserDailyExcelVO> getDeptCheckinDailyRecord(CheckinDailyDTO dto);

    /**
     * 获取用户 打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/29 17:15
     */
    CheckinDailyVO getUserCheckinDailyRecord(CheckinDailyUserDTO dto);


    /**
     * 根据userId、belongDay（唯一索引）获取
     * <AUTHOR> yang.
     * @since 2025/4/30 15:15
     */
    CheckinUserDaily getByUserIdAndBelongDay(String userId, String belongDay);

    /**
     * 设置 在校未打卡
     * <AUTHOR> yang.
     * @since 2025/4/30 15:07
     */
    void changeUserUnSchool(@Valid CheckinUserStatusDTO dto);

    /**
     * 设置 无需打卡备注
     * <AUTHOR> yang.
     * @since 2025/4/30 15:07
     */
    void changeUserRemark(@Valid CheckinUserStatusDTO dto);

    /**
     * 通知未打卡用户
     * <AUTHOR> yang.
     * @since 2025/5/6 10:53
     */
    void sendWxCpMessageToUnCheckinUser(@Valid MessageUnCheckinUserDTO dto);

    /**
     * 导出 全校日打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 11:15
     */
    List<CheckinUserDailyExcelVO> exportDayAllCheckinRecord(@Valid CheckinDailyDTO dto);

    /**
     * 导出 学院日打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 11:15
     */
    List<CheckinUserDailyExcelVO> exportDayCheckinRecord(@Valid CheckinDailyDTO dto);
}
