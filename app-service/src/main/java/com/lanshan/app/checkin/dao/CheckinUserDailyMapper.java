package com.lanshan.app.checkin.dao;

import com.lanshan.app.checkin.po.CheckinUserDaily;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户打卡每日统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-27
 */
@Mapper
public interface CheckinUserDailyMapper extends BaseMapper<CheckinUserDaily> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinUserDaily> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CheckinUserDaily> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinUserDaily> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CheckinUserDaily> entities);

}
