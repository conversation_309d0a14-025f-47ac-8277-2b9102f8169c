package com.lanshan.app.checkin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lanshan.app.checkin.dto.CheckinStudentCadresDTO;
import com.lanshan.app.checkin.po.CheckinAuditUser;
import com.lanshan.app.checkin.po.CheckinUserCadres;
import com.lanshan.app.checkin.dao.CheckinUserCadresMapper;
import com.lanshan.app.checkin.service.CheckinUserCadresService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.checkin.vo.CheckinUserCadresVO;
import com.lanshan.app.common.utils.FeignResultUtil;
import com.lanshan.base.api.feign.addressbook.CpUserFeign;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生干部 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-30
 */

@Slf4j
@Service
public class CheckinUserCadresServiceImpl extends ServiceImpl<CheckinUserCadresMapper, CheckinUserCadres> implements CheckinUserCadresService {

    @Resource
    private CheckinUserCadresMapper checkinUserCadresMapper;
    @Resource
    private CpUserFeign cpUserFeign;

    @Resource
    @Lazy
    private CheckinUserCadresServiceImpl self;

    @Override
    public boolean judgeWhetherCadres(String userId) {
        return checkinUserCadresMapper.judgeWhetherCadres(userId) > 0;
    }

    @Override
    public String getCounselorByCadres(String userId) {
        LambdaQueryWrapper<CheckinUserCadres> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckinUserCadres::getUserId, userId);
        CheckinUserCadres one = this.getOne(wrapper);
        if (one != null) {
            return one.getCreateUserId();
        }
        return "-1";
    }


    @Override
    public List<CheckinUserCadresVO> getUserCadres(List<Long> deptIds) {
        //用户信息
        List<UserInfoPartVO> users = FeignResultUtil.success(cpUserFeign.listUsersByDeptIds(deptIds));
        if (CollUtil.isEmpty(users)) {
            return List.of();
        }

        List<String> userIds = users.stream().map(UserInfoPartVO::getUserId).collect(Collectors.toList());
        //辅导员设置的学生干部
        LambdaQueryWrapper<CheckinUserCadres> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CheckinUserCadres::getUserId, userIds);
        List<CheckinUserCadres> list = this.list(wrapper);

        Map<String, CheckinUserCadres> cadresMap = list.stream()
                .collect(Collectors.toMap(CheckinUserCadres::getUserId, c -> c,(newVal, oldVal) -> newVal));
        List<CheckinUserCadresVO> cadresList = new ArrayList<>();
        CheckinUserCadresVO cadresVO = null;
        for (UserInfoPartVO user : users) {
            cadresVO = new CheckinUserCadresVO();
            cadresVO.setUserId(user.getUserId());
            cadresVO.setUserName(user.getName());
            boolean cadresFlag = false;
            CheckinUserCadres userCadres = cadresMap.get(user.getUserId());
            if (userCadres != null) {
                cadresFlag = true;
            }
            cadresVO.setCadres(cadresFlag);
            cadresList.add(cadresVO);
        }
        return cadresList;
    }

    @Override
    public void updateCheckinStudentCadres(CheckinStudentCadresDTO dto) {
        String createUser = dto.getCreateUser();
        List<String> userIds = dto.getUserIds();
        boolean setting = dto.isSetting();
        Date now = new Date();
        if (setting){
            //设置学生干部
            List<CheckinUserCadres> users = new ArrayList<>(userIds.size());
            CheckinUserCadres user = null;
            for (String userId : userIds) {
                user = new CheckinUserCadres();
                user.setUserId(userId);
                user.setCreateUserId(createUser);
                user.setCreateTime(now);
                users.add(user);
            }
            self.saveBatch(users);
        }else {
            //取消学生干部
            LambdaUpdateWrapper<CheckinUserCadres> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(CheckinUserCadres::getUserId, userIds);
            this.remove(wrapper);
        }
    }

    @Override
    public Map<Long, List<String>> getGradeCadresMap() {
        List<CheckinUserCadres> list = checkinUserCadresMapper.getCadresWithGrade();
        if (CollUtil.isEmpty(list)) {
            return Map.of();
        }
        Map<Long, List<String>> deptUserMap = list.stream()
                .filter(user -> user.getDeptId()  != null)  // 过滤 null
                .collect(Collectors.groupingBy(
                        CheckinUserCadres::getDeptId,          // 按 deptId 分组
                        Collectors.mapping(
                                CheckinUserCadres::getUserId,      // 提取 userId
                                Collectors.toList()                // 收集为 List<String>
                        )
                ));
        return deptUserMap;
    }
}
