package com.lanshan.app.checkin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class CheckinRecordDTO implements Serializable {

    private static final long serialVersionUID = -7290284587011847758L;

    //用户id
    @JsonProperty("userid")
    private String userId;

    //规则 id
    @JsonProperty("groupid")
    private Long groupId;

    //打卡规则名称
    @JsonProperty("groupname")
    private String groupName;

    //打卡类型。字符串，目前有：上班打卡，下班打卡，外出打卡，仅记录打卡时间和位置
    @JsonProperty("checkin_type")
    private String checkinType;

    //异常类型，字符串，包括：时间异常，地点异常，未打卡，wifi异常，非常用设备。如果有多个异常，以分号间隔
    @JsonProperty("exception_type")
    private String exceptionType;

    //打卡地点title
    @JsonProperty("location_title")
    private String locationTitle;

    //打卡地点详情
    @JsonProperty("location_detail")
    private String locationDetail;

    //打卡备注
    private String notes;

    //打卡时间
    private Long checkin_time;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //标准打卡时间，指此次打卡时间对应的标准上班时间或标准下班时间
    private Long sch_checkin_time;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date schCheckinTime;

    //checkin_time表的id
    @JsonProperty("timeline_id")
    private Integer timeId;

    //打卡的附件media_id，可使用media/get获取附件
    @JsonProperty("mediaids")
    private List<String> mediaIds;

    public void setCheckin_time(Long checkin_time) {
        this.checkin_time = checkin_time;
        if (checkin_time != null && checkin_time > 0) {
            this.createTime = new Date(checkin_time * 1000);
        }
    }

    public void setSch_checkin_time(Long sch_checkin_time) {
        this.sch_checkin_time = sch_checkin_time;
        if (sch_checkin_time != null && sch_checkin_time > 0) {
            this.schCheckinTime = new Date(sch_checkin_time * 1000);
        }
    }
}
