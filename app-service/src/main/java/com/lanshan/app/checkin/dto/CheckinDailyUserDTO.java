package com.lanshan.app.checkin.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class CheckinDailyUserDTO implements Serializable {

    private static final long serialVersionUID = 3134065006913577367L;

    @NotNull
    private String userId;

    @NotNull
    private String userName;

    @NotNull
    private Long deptId;

    @NotNull
    private String deptName;

    @NotNull
    // yyyy-MM-dd
    private String belongDay;

}
