package com.lanshan.app.checkin.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class CheckinUserStatusDTO implements Serializable {
    private static final long serialVersionUID = -7135734770721497348L;

    @NotNull
    private String userId;

    @NotNull
    private String userName;

    @NotNull
    // yyyy-MM-dd
    private String belongDay;

    @NotNull
    private Long deptId;

    @NotNull
    private String deptName;

    //是否在校未打卡 0否 1是
    private Integer unSchool;

    //无需打卡备注
    private String remark;

}
