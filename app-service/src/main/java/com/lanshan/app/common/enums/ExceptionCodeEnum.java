package com.lanshan.app.common.enums;


import com.lanshan.app.common.bo.ServiceException;
import lombok.Getter;

/**
 * 异常编码
 *
 * <AUTHOR>
 */
@Getter
public enum ExceptionCodeEnum {

    //系统相关 start
    SUCCESS(0, "成功"),
    SYSTEM_BUSY(-1, "系统繁忙~请稍后再试~"),
    SYSTEM_TIMEOUT(-2, "系统维护中~请稍后再试~"),
    PARAM_EX(-3, "参数类型解析异常"),
    SQL_EX(-4, "运行SQL出现异常"),
    NULL_POINT_EX(-5, "空指针异常"),
    ILLEGAL_ARGUMENT_EX(-6, "无效参数异常"),
    MEDIA_TYPE_EX(-7, "请求类型异常"),
    LOAD_RESOURCES_ERROR(-8, "加载资源出错"),
    BASE_VALID_PARAM(-9, "统一验证参数异常"),
    OPERATION_EX(-10, "操作异常"),
    SERVICE_MAPPER_ERROR(-11, "Mapper类转换异常"),
    CAPTCHA_ERROR(-12, "验证码校验失败"),
    JSON_PARSE_ERROR(-13, "JSON解析异常"),
    PARAM_NOT_EMPTY(-14, "参数[%s]不能为空！"),
    SMS_CODE_ERROR(-15, "验证码错误"),
    SMS_CODE_EXPIRED(-16, "验证码已过期"),
    SMS_TOO_MUCH_REQUESTS(-17, "验证码请求频繁，请稍后再试！"),

    OK(200, "OK"),
    BAD_REQUEST(400, "错误的请求"),
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "没有找到资源"),
    METHOD_NOT_ALLOWED(405, "不支持当前请求类型"),
    TOO_MANY_REQUESTS(429, "请求超过次数限制"),
    INTERNAL_SERVER_ERROR(500, "内部服务错误"),
    BAD_GATEWAY(502, "网关错误"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    REQUIRED_FILE_PARAM_EX(1001, "请求中必须至少包含一个有效文件"),
    DATA_SAVE_ERROR(2000, "新增数据失败"),
    DATA_UPDATE_ERROR(2001, "修改数据失败"),
    TOO_MUCH_DATA_ERROR(2002, "批量新增数据过多"),

    JWT_BASIC_INVALID(40000, "无效的基本身份验证令牌"),
    JWT_TOKEN_EXPIRED(40001, "会话超时，请重新登录"),
    JWT_SIGNATURE(40002, "不合法的token，请认真比对 token 的签名"),
    JWT_ILLEGAL_ARGUMENT(40003, "缺少token参数"),
    JWT_GEN_TOKEN_FAIL(40004, "生成token失败"),
    JWT_PARSER_TOKEN_FAIL(40005, "解析用户身份错误，请重新登录！"),
    JWT_USER_INVALID(40006, "用户名或密码错误"),
    JWT_USER_ENABLED(40007, "用户已经被禁用！"),
    JWT_OFFLINE(40008, "您已在另一个设备登录！"),
    JWT_NOT_LOGIN(40009, "登录超时，请重新登录！"),
    JWT_USER_INVALID_OVER_TIMES(40010, "密码输入错误%s次，帐户锁定%s分钟"),

    FILE_UPLOAD_ERROR(50000, "文件上传服务异常！"),
    FILE_NOT_FOUND(50001, "文件不存在！"),
    FILE_SIZE_EXCEED(50002, "文件过大，单个文件大小不能超过50M！"),
    FILE_TYPE_ERROR(50003, "文件格式不正确，请查看操作手册！"),
    FILE_DOWNLOAD_ERROR(50004, "文件下载异常！"),
    FILE_DELETE_ERROR(50005, "文件删除失败！"),

    EXPORT_EXCEL_ERROR(50006, "导出Excel异常！"),

    CONFIG_DELETED_DISABLED(60000, "内置参数【%s】不能删除"),

    AGENT_NOT_FOUND(70000, "企业微信访问异常，检查应用ID, Secret是否正确！"),
    APP_NOT_FOUND(70001, "应用不存在！"),
    AGENT_CONFIG_NOT_FOUND(70002, "企业微信配置不存在！"),
    AGENT_EXIST(70003, "企业微信应用已存在，请勿重复添加！"),
    APP_NAME_EXIST(70004, "应用名称已存在，请勿重复添加！"),
    THEME_EXIST(70005, "主题已存在，请勿重复添加！"),
    THEME_NOT_EXIST(70006, "主题不存在！"),
    CAN_NOT_DEL_DEFAULT_THEME(70007, "默认主题不能删除！"),
    AGENT_ZONE_EXIST(70008, "应用专区已存在，请勿重复添加！"),

    TAG_NAME_EXIST(80000, "标签名称已存在，请勿重复添加！"),

    //通讯录相关
    USER_NOT_EXIST(90000, "用户不存在！"),
    USER_EXIST(90001, "用户已存在！"),
    DEPT_NOT_EXIST(90002, "部门不存在！"),
    DEPT_EXIST(90003, "部门已存在！"),
    TAG_NOT_EXIST(90004, "标签不存在！"),
    TAG_EXIST(90005, "标签已存在！"),

    //人脸库相关
    IMAGES_FOR_REVIEW_ALREADY_EXIST(100000, "已存在待审核图片，请勿重复提交！"),
    IMAGES_FOR_UPLOAD_OVER_LIMIT(100001, "上传图片数量超过限制，请联系管理员！"),
    IMG_USER_IMAGE_NOT_FOUNT(100002, "图片信息不存在！"),
    IMAGE_AUDIT_PASS_NOT_FOUNT(100003, "未找到审核通过的图片！"),
    ILLEGAL_USER_TYPES(100004, "非法的用户类型！"),
    ILLEGAL_PHOTO_TYPE(100005, "非法的图片类型！"),
    QUALITY_CHECK_FAIL(100006, "图片质量检测未通过！"),
    FAILED_TO_READ_IMG_TYPE_SETTINGS(100007, "读取图片类型设置失败！"),
    IMAGE_NOT_FIND(100008, "未找到对应图片"),
    ILLEGAL_UPLOAD(100009, "不允许上传！"),
    WATERMARK_PARSE_ERROR(100010, "水印解析失败！"),
    NO_AUDIT_PERSON(100011, "无审核人，无法上传！"),

    //API接入
    API_EXIST(110000, "接口已存在！"),
    API_NOT_EXIST(110001, "接口不存在！"),
    COMPANY_EXIST(110002, "接入方已存在！"),
    COMPANY_NOT_EXIST(110003, "接入方不存在！"),
    APP_EXIST(110004, "应用已存在！"),
    APP_NOT_EXIST(110005, "应用不存在！"),
    APP_SECRET_ERROR(110006, "keyId或appSecret错误！"),
    APP_DISABLE(110007, "应用已禁用！"),
    API_PATH_EXIST(110008, "URL路径已存在！"),
    APP_EXPIRED(110009, "应用已过期！"),
    COMPANY_RELATE_APP(110010, "当前接入方有相关应用，无法删除！"),

    // 海康威视相关
    HIK_API_ERROR(120000, "海康威视接口调用异常！"),
    HIK_API_PARAM_ERROR(120003, "海康威视接口参数错误！"),

    //跳蚤市场
    //收藏数量超出限制
    FLEA_COLLECTION_LIMIT(130000, "收藏数量超出限制！"),
    //收藏已存在
    FLEA_COLLECTION_EXIST(130001, "收藏已存在！"),
    //上传图片数量超出限制
    FLEA_UPLOAD_LIMIT(130002, "上传图片数量超出限制！"),
    FLEA_MOBILE_ERROR(130003, "手机号格式错误！"),
    FLEA_MAX_POLISH(130004, "今天的擦亮次数已用完！"),
    FLEA_COMMODITY_TYPE_EXISTS(130005, "商品类型已存在！"),

    //报修
    REPAIR_CATEGORY_SIBLING_EXIST(140000, "报修类目已存在，请勿重复添加！"),
    REPAIR_CATEGORY_CHILDREN_EXIST(140001, "存在子类无法删除！"),
    REPAIR_RECORD_SAVE_FAILED(140001, "提交失败！"),
    REPAIR_RECORD_UPDATE_FAILED(140002, "更新失败！"),
    REPAIR_RECORD_DELETE_FAILED(140003, "删除失败！"),
    REPAIR_BUILDING_NAME_EXIST(140004, "楼栋名称已存在，请勿重复添加！"),
    REPAIR_OPERATE_TYPE_UNKNOWN(140005, "未知操作，请联系管理员处理！"),
    REPAIR_RECORD_COMPLETED_OR_CANCELED(140006, "已接单或已取消，请勿重复操作！"),
    REPAIR_RECORD_CONFIRMED(140007, "已确认或已取消，请勿重复操作！"),
    REPAIR_RECORD_CAN_NOT_COMPLETE(140009, "当前状态不可完成！"),
    REPAIR_RECORD_CAN_NOT_TRANSFER(140008, "当前状态不可转办！"),
    REPAIR_RECORD_CAN_NOT_OPERATE(140010, "不支持的操作！"),

    //意见箱相关
    SUGGEST_CATEGORY_NAME_EXIST(150000, "意见箱分类已存在！"),
    SUGGEST_CATEGORY_NOT_EXIST(150001, "意见箱分类被删除或不存在！"),
    SUGGEST_CATEGORY_TYPE_NOT_EXIST(150002, "意见箱类型不存在，请联系管理员！"),
    SUGGEST_REPLY_ALREADY_EXIST(150003, "意见已回复，无需重复回复！"),

    SYSTEM_COMMON_ERROR(999, "%s");


    private final int code;
    private String msg;

    ExceptionCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ServiceException build(String msg, Object... param) {
        return new ServiceException(String.format(msg, param), this.code);
    }

    public ServiceException param(Object... param) {
        return new ServiceException(String.format(msg, param), this.code);
    }

    public ServiceException toServiceException() {
        return new ServiceException(this);
    }

    public ServiceException toServiceException(String msg) {
        return new ServiceException(msg, this.code);
    }
}
