package com.lanshan.app.common.bo;

import com.lanshan.app.common.enums.ExceptionCodeEnum;
import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Getter
public final class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误提示
     */
    private final String msg;


    public ServiceException(String message, Integer code) {
        super(message);
        this.msg = message;
        this.code = code;
    }

    public ServiceException(ExceptionCodeEnum ex) {
        super(ex.getMsg());
        this.msg = ex.getMsg();
        this.code = ex.getCode();
    }
}