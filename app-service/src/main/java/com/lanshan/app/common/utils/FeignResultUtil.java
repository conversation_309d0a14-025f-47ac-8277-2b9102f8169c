package com.lanshan.app.common.utils;

import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.Result;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description feign 调用工具类
 */
public class FeignResultUtil {

    public static <T> T success(Result<T> result) {
        if (!result.success()) {
            throw ExceptionCodeEnum.INTERNAL_SERVER_ERROR.toServiceException("内部服务 Feign 调用失败，请稍后再试！");
        }
        return result.getData();
    }
}
