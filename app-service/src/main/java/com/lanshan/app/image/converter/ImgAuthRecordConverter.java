package com.lanshan.app.image.converter;


import com.lanshan.app.image.entity.ImgAuthRecord;
import com.lanshan.app.image.vo.ImgAuthRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户照片库授权记录(ImgAuthRecord)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ImgAuthRecordConverter {

    ImgAuthRecordConverter INSTANCE = Mappers.getMapper(ImgAuthRecordConverter.class);

    ImgAuthRecordVO toVO(ImgAuthRecord entity);

    ImgAuthRecord toEntity(ImgAuthRecordVO vo);

    List<ImgAuthRecordVO> toVO(List<ImgAuthRecord> entityList);

    List<ImgAuthRecord> toEntity(List<ImgAuthRecordVO> voList);
}


