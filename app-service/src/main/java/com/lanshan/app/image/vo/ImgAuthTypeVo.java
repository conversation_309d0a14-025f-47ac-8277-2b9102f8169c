package com.lanshan.app.image.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * Description: new java files header
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 10:44
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel("照片授权类型VO")
public class ImgAuthTypeVo {

    @ApiModelProperty(value = "照片授权类型值")
    private Integer value;

    @ApiModelProperty(value = "照片授权类型名")
    private String label;
}
