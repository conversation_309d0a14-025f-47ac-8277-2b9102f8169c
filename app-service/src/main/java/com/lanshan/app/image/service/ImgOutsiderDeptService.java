package com.lanshan.app.image.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.image.entity.ImgOutsiderDept;

import java.util.List;

/**
 * 照片库校外人员部门信息(ImgOutsiderDept)表服务接口
 *
 * <AUTHOR>
 */
public interface ImgOutsiderDeptService extends IService<ImgOutsiderDept> {

    /**
     * 根据树类型查询部门信息
     *
     * @param type 树类型
     * @return 部门信息
     */
    List<ImgOutsiderDept> listByTreeType(Integer type);

    /**
     * 根据部门id和树类型查询部门信息
     *
     * @param deptId   部门id
     * @param treeType 树类型
     * @return 部门信息 0:全部部门 1：外部人员可见部门树；2：外部人员可选部门树
     */
    ImgOutsiderDept getOneByDeptIdAndTreeType(Long deptId, Integer treeType);
}

