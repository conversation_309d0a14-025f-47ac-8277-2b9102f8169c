package com.lanshan.app.image.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 提醒上传 DTO 对象
 */
@ApiModel("提醒上传DTO对象")
@Data
public class RemindToUploadDTO {

    @ApiModelProperty("用户userIds")
    private List<String> userIds;

    @ApiModelProperty("用户类型。1：学生；2：教师；3：校外人员；4：邀请人员")
    private Integer userType;

    @ApiModelProperty("照片类型。1：认证照；2:档案照；3：证件照；4：生活照")
    private Integer imgType;
}
