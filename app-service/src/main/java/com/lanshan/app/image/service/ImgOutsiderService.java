package com.lanshan.app.image.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.image.dto.OutsiderSearchDTO;
import com.lanshan.app.image.dto.SetAuditorDto;
import com.lanshan.app.image.dto.SetDepartmentDto;
import com.lanshan.app.image.entity.ImgOutsider;
import com.lanshan.app.image.vo.ImgOutsiderDeptVO;
import com.lanshan.app.image.vo.ImgOutsiderVO;

import java.util.List;

/**
 * 照片库校外人员信息(ImgOutsider)表服务接口
 *
 * <AUTHOR>
 */
public interface ImgOutsiderService extends IService<ImgOutsider> {

    /**
     * 分页查询校外人员照片上传情况
     *
     * @param dto 查询条件
     * @return IPage<ImgOutsiderVO> 分页查询结果
     */
    IPage<ImgOutsiderVO> pageOutsiderImgInfo(OutsiderSearchDTO dto);

    /**
     * 设置外部人员可见部门树
     *
     * @param dto 部门列表
     * @return Boolean 是否设置成功
     */
    Boolean setDepartmentShowForOutsider(SetDepartmentDto dto);

    /**
     * 设置外部人员可选部门树
     *
     * @param dto 部门列表
     * @return Boolean 是否设置成功
     */
    Boolean setDepartmentOptionForOutsider(SetDepartmentDto dto);

    /**
     * 设置校外人员可选择部门负责人
     *
     * @param dto 部门负责人信息
     * @return
     */
    Boolean setDepartmentShowCustodian(SetAuditorDto dto);

    /**
     * 获取邀请外部人员邀请采集链接
     * 需要附带邀请人信息
     *
     * @return String 邀请采集链接
     */
    String getInviteOutsiderImageCollectUrl();

    /**
     * 查询校外人员可见或可选部门
     *
     * @param type 0:全部部门 1：外部人员可见部门树；2：外部人员可选部门树
     * @return
     */
    List<ImgOutsiderDeptVO> listOutsiderDeptInfo(Integer type);

}

