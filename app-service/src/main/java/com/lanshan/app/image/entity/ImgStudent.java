package com.lanshan.app.image.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 学生照片库基本信息(ImgStudent)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ImgStudent extends Model<ImgStudent> {
    private static final long serialVersionUID = -6907146510171636776L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 用户学工号
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 职务
     */
    private String post;
    /**
     * 学院
     */
    private String college;
    /**
     * 专业
     */
    private String speciality;
    /**
     * 班级
     */
    private String userClass;
    /**
     * 学界
     */
    private String enrolYear;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 性别。男， 女
     */
    private String gender;
    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

