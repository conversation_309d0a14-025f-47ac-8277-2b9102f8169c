package com.lanshan.app.image.enums;

import lombok.Getter;

/**
 * 照片状态枚举
 */
@Getter
public enum ImgStatusEnum {

    EXIST_IMG_NOT_MAIN(1, "有图片没主图"),
    EXIST_MAIN_IMG(2, "有主图,默认显示"),
    NOT_EXIST_IMG(3, "对应照片类型没有图片");

    private final Integer code;
    private final String desc;

    ImgStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImgStatusEnum getEnumById(Integer code) {
        for (ImgStatusEnum typeEnum : ImgStatusEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

}
