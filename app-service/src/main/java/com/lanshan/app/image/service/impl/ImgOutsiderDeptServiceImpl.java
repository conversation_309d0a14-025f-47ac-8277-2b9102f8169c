package com.lanshan.app.image.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.image.dao.ImgOutsiderDeptDao;
import com.lanshan.app.image.entity.ImgOutsiderDept;
import com.lanshan.app.image.service.ImgOutsiderDeptService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 照片库校外人员部门信息(ImgOutsiderDept)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgOutsiderDeptService")
public class ImgOutsiderDeptServiceImpl extends ServiceImpl<ImgOutsiderDeptDao, ImgOutsiderDept> implements ImgOutsiderDeptService {

    @Override
    public List<ImgOutsiderDept> listByTreeType(Integer type) {
        LambdaQueryWrapper<ImgOutsiderDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImgOutsiderDept::getTreeType, type);
        return this.list(wrapper);
    }

    @Override
    public ImgOutsiderDept getOneByDeptIdAndTreeType(Long deptId, Integer treeType) {
        return this.getOne(Wrappers.lambdaQuery(ImgOutsiderDept.class)
                .eq(ImgOutsiderDept::getDeptId, deptId)
                .eq(ImgOutsiderDept::getTreeType, treeType));
    }
}

