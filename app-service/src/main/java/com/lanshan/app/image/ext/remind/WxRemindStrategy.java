package com.lanshan.app.image.ext.remind;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 微信催办通知
 */
@Slf4j
@Service
public class WxRemindStrategy implements RemindStrategy {


    public void remindToUpload(String reminder, String notifiedParty, String remindMessage) {
        log.info("我是通知方法，通知人是：{}，被通知人是：{}，通知具体内容是：{}", reminder, notifiedParty, remindMessage);
    }
}
