package com.lanshan.app.image.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.image.dao.ImgStudentDao;
import com.lanshan.app.image.dto.StudentSearchDTO;
import com.lanshan.app.image.entity.ImgStudent;
import com.lanshan.app.image.service.ImgStudentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 学生照片库基本信息(ImgStudent)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgStudentService")
public class ImgStudentServiceImpl extends ServiceImpl<ImgStudentDao, ImgStudent> implements ImgStudentService {

    @Override
    public ImgStudent getStudentInfoByUserId(String userId) {
        LambdaQueryWrapper<ImgStudent> qw = Wrappers.lambdaQuery(ImgStudent.class);
        qw.eq(StringUtils.isNotEmpty(userId), ImgStudent::getUserId, userId);
        qw.last("limit 1");
        return this.getOne(qw);
    }

    @Override
    public List<ImgStudent> listByUserIds(List<String> userIds) {
        LambdaQueryWrapper<ImgStudent> qw = Wrappers.lambdaQuery(ImgStudent.class);
        qw.in(ImgStudent :: getUserId,userIds);
        return list(qw);
    }

    @Override
    public List<String> listJobOrGrade(int flag) {
        LambdaQueryWrapper<ImgStudent> qw = Wrappers.lambdaQuery(ImgStudent.class);
        if(flag == 1){
            qw.select(ImgStudent :: getPost);
            qw.groupBy(ImgStudent :: getPost);
            return this.listObjs(qw);
        }else{
            qw.select(ImgStudent :: getEnrolYear);
            qw.groupBy(ImgStudent :: getEnrolYear);
            qw.orderByDesc(ImgStudent :: getEnrolYear);
        }
        return this.listObjs(qw);
    }

    @Override
    public IPage<ImgStudent> pageStudentImgInfo(Page<ImgStudent> page, StudentSearchDTO dto) {
        return this.baseMapper.pageStudentImgInfo(page, dto);
    }
}

