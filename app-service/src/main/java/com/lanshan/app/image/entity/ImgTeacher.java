package com.lanshan.app.image.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 照片库教师信息(ImgTeacher)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ImgTeacher extends Model<ImgTeacher> {
    private static final long serialVersionUID = -9020642858799302341L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 教师学工号
     */
    private String userId;
    /**
     * 教师名称
     */
    private String userName;
    /**
     * 所属部门ID
     */
    private Long deptId;
    /**
     * 所属部门名称
     */
    private String deptName;
    /**
     * 职务
     */
    private String post;
    /**
     * 电话号码
     */
    private String phoneNumber;
    /**
     * 性别。男，女，未知
     */
    private String gender;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

