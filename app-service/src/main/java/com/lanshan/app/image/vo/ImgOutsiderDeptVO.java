package com.lanshan.app.image.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 照片库校外人员部门信息(ImgOutsiderDept)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "照片库校外人员部门信息VO")
@Data
@ToString
public class ImgOutsiderDeptVO implements Serializable {

    private static final long serialVersionUID = 4834305916129702272L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "部门Id")
    private Long deptId;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "上级部门ID。 根目录为0")
    private Long parentid;

    @ApiModelProperty(value = "校外人员是否可选此部门")
    private Boolean isSelectedAble;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "部门ID路径")
    private String idPath;

    @ApiModelProperty(value = "部门负责人ID")
    private String deptLeader;

    @ApiModelProperty(value = "部门负责人名称")
    private String deptLeaderName;

    @ApiModelProperty(value = "部门树类型。1：外部人员可见部门树；2：外部人员可选部门树")
    private Integer treeType;

    @ApiModelProperty(value = "是否已被可选部门树选中")
    private Boolean select;
}

