package com.lanshan.app.image.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.image.dao.ImgTeacherDao;
import com.lanshan.app.image.dto.TeacherSearchDTO;
import com.lanshan.app.image.entity.ImgTeacher;
import com.lanshan.app.image.service.ImgTeacherService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 照片库教师信息(ImgTeacher)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgTeacherService")
public class ImgTeacherServiceImpl extends ServiceImpl<ImgTeacherDao, ImgTeacher> implements ImgTeacherService {

    @Override
    public ImgTeacher getTeacherInfoByUserId(String userId) {
        LambdaQueryWrapper<ImgTeacher> qw = Wrappers.lambdaQuery(ImgTeacher.class);
        qw.eq(StringUtils.isNotEmpty(userId), ImgTeacher::getUserId, userId);
        qw.last("limit 1");
        return this.getOne(qw);
    }

    @Override
    public List<ImgTeacher> listByUserIds(List<String> userIds) {
        LambdaQueryWrapper<ImgTeacher> qw = Wrappers.lambdaQuery(ImgTeacher.class);
        qw.in(ImgTeacher :: getUserId,userIds);
        return list(qw);
    }

    @Override
    public List<String> listJob() {
        LambdaQueryWrapper<ImgTeacher> qw = Wrappers.lambdaQuery(ImgTeacher.class);
        qw.select(ImgTeacher :: getPost);
        qw.groupBy(ImgTeacher :: getPost);
        return this.listObjs(qw);
    }

    @Override
    public IPage<ImgTeacher> pageTeacherImgInfo(Page<ImgTeacher> page, TeacherSearchDTO teacherSearchDTO) {
        return this.baseMapper.pageTeacherImgInfo(page, teacherSearchDTO);
    }
}

