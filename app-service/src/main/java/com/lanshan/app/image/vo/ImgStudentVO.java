package com.lanshan.app.image.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 学生照片库基本信息(ImgStudent)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "学生照片库基本信息VO")
@Data
@ToString
public class ImgStudentVO implements Serializable {

    private static final long serialVersionUID = 7393512416839677272L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户学工号")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "职务")
    private String post;

    @ApiModelProperty(value = "学院")
    private String college;

    @ApiModelProperty(value = "专业")
    private String speciality;

    @ApiModelProperty(value = "班级")
    private String userClass;

    @ApiModelProperty(value = "学界")
    private String enrolYear;

    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "性别。男， 女")
    private String gender;

    @ApiModelProperty(value = "所属部门ID")
    private Long deptId;

    @ApiModelProperty(value = "上传情况 true:已上传 false:未上传")
    private Boolean isUpload;

    @ApiModelProperty(value = "已授权应用数")
    private Long authCount;

    @ApiModelProperty(value = "未授权应用数")
    private Long unAuthCount;
}

