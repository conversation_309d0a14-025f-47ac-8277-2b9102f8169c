package com.lanshan.app.image.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.image.converter.ImgSysConfigConverter;
import com.lanshan.app.image.dao.ImgSysConfigDao;
import com.lanshan.app.image.dto.ImgSysConfigDTO;
import com.lanshan.app.image.entity.ImgSysConfig;
import com.lanshan.app.image.service.ImgSysConfigService;
import com.lanshan.app.image.vo.ImgSysConfigVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 参数配置表(ImgSysConfig)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgSysConfigService")
public class ImgSysConfigServiceImpl extends ServiceImpl<ImgSysConfigDao, ImgSysConfig> implements ImgSysConfigService {

    @Resource
    @Lazy
    private ImgSysConfigService imgSysConfigService;

    @Override
    public ImgSysConfigVO getByConfigKey(String configKey) {
        ImgSysConfig imgSysConfig = this.getOne(Wrappers.lambdaQuery(ImgSysConfig.class)
                .eq(ImgSysConfig::getConfigKey, configKey));
        return ImgSysConfigConverter.INSTANCE.toVO(imgSysConfig);
    }

    @Override
    public List<ImgSysConfigVO> listByConfigKey(String configKey) {
        List<ImgSysConfig> imgSysConfigList = this.list(Wrappers.lambdaQuery(ImgSysConfig.class)
                .likeRight(ImgSysConfig::getConfigKey, configKey)
                .orderByAsc(ImgSysConfig::getConfigId)
        );
        return ImgSysConfigConverter.INSTANCE.toVO(imgSysConfigList);
    }

    @Override
    public List<ImgSysConfigVO> listByConfigType(String configType) {
        List<ImgSysConfig> imgSysConfigList = this.list(Wrappers.lambdaQuery(ImgSysConfig.class)
                .eq(ImgSysConfig::getConfigType, configType)
                .orderByAsc(ImgSysConfig::getConfigId)
        );
        return ImgSysConfigConverter.INSTANCE.toVO(imgSysConfigList);
    }


    @Override
    public ImgSysConfigVO buildTree(String configKey, String mainConfigType) {
        List<ImgSysConfigVO> imgSysConfigVOS = this.listByConfigKey(configKey);
        if (CollUtil.isEmpty(imgSysConfigVOS)) {
            return null;
        }
        ImgSysConfigVO parent = null;
        //根据configKey将数据转为 Map 结构
        Map<String, ImgSysConfigVO> configKeyMap = imgSysConfigVOS.stream().collect(Collectors.toMap(ImgSysConfigVO::getConfigKey, v -> v));
        //遍历配置信息列表
        for (ImgSysConfigVO imgSysConfigVO : imgSysConfigVOS) {
            //获取当前节点的 configType
            String configType = imgSysConfigVO.getConfigType();
            //configType 不为 main 代表非顶级节点
            if (!CharSequenceUtil.equals(mainConfigType, configType)) {
                //非顶层节点
                ImgSysConfigVO parentConfigVO = configKeyMap.get(configType);
                List<ImgSysConfigVO> children = parentConfigVO.getChildren();
                //children 为空时初始化集合
                if (CollUtil.isEmpty(children)) {
                    children = new ArrayList<>();
                    parentConfigVO.setChildren(children);
                }
                children.add(imgSysConfigVO);
            } else {
                parent = imgSysConfigVO;
            }
        }
        return parent;
    }

    @Override
    public Boolean saveOrUpdate(List<ImgSysConfigDTO> dtoList) {
        List<ImgSysConfig> entityList = ImgSysConfigConverter.INSTANCE.dtoToEntity(dtoList);
        String userIdStr = SecurityContextHolder.getUserIdStr();
        Date nowDate = new Date();
        for (ImgSysConfig imgSysConfig : entityList) {
            if (Objects.isNull(imgSysConfig.getConfigId())) {
                imgSysConfig.setCreateBy(userIdStr);
                imgSysConfig.setCreateTime(nowDate);
            }
            imgSysConfig.setUpdateBy(userIdStr);
            imgSysConfig.setUpdateTime(nowDate);
        }
        return imgSysConfigService.saveOrUpdateBatch(entityList);
    }
}

