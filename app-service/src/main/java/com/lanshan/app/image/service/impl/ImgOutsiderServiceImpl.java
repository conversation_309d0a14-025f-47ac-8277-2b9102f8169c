package com.lanshan.app.image.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.bo.ServiceException;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.utils.SpringUtils;
import com.lanshan.app.image.converter.ImgOutsiderConverter;
import com.lanshan.app.image.converter.ImgOutsiderDeptConverter;
import com.lanshan.app.image.dao.ImgOutsiderDao;
import com.lanshan.app.image.dto.OutsiderSearchDTO;
import com.lanshan.app.image.dto.SetAuditorDto;
import com.lanshan.app.image.dto.SetDepartmentDto;
import com.lanshan.app.image.entity.ImgOutsider;
import com.lanshan.app.image.entity.ImgOutsiderDept;
import com.lanshan.app.image.enums.OutSiderDeptTreeTypeEnum;
import com.lanshan.app.image.enums.UserTypeEnum;
import com.lanshan.app.image.service.*;
import com.lanshan.app.image.vo.ImgOutsiderDeptVO;
import com.lanshan.app.image.vo.ImgOutsiderVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 照片库校外人员信息(ImgOutsider)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgOutsiderService")
public class ImgOutsiderServiceImpl extends ServiceImpl<ImgOutsiderDao, ImgOutsider> implements ImgOutsiderService {


    @Resource
    private ImgOutsiderDeptService imgOutsiderDeptService;
    @Resource
    private ImgUserImageService imgUserImageService;
    @Resource
    private ImgAuthorizationService imgAuthorizationService;

    @Override
    public IPage<ImgOutsiderVO> pageOutsiderImgInfo(OutsiderSearchDTO dto) {
        Page<ImgOutsider> page = new Page<>(dto.getPage(), dto.getSize());
        // 获取教师照片库基本信息列表
        IPage<ImgOutsider> pageData = this.baseMapper.pageOutsiderImgInfo(page, dto);
        IPage<ImgOutsiderVO> imgOutsiderVoPage = new Page<>();
        if (CollUtil.isNotEmpty(pageData.getRecords())) {
            // 获取校外人员上传情况
            List<String> userIds = pageData.getRecords().stream().map(ImgOutsider::getId).collect(Collectors.toList());
            Map<String, Long> userUploadCount = imgUserImageService.getUserUploadCount(userIds, dto.getImgType());
            Map<String, Long> userAuthCount = imgAuthorizationService.getUserAuthCount(userIds, dto.getImgType());
            imgOutsiderVoPage = pageData.convert(ImgOutsiderConverter.INSTANCE::toVO);
            for (ImgOutsiderVO item : imgOutsiderVoPage.getRecords()) {
                // 填充上传情况
                item.setAuthCount(userAuthCount.getOrDefault(item.getId(), 0L));
                item.setIsUpload(userUploadCount.getOrDefault(item.getId(), 0L) > 0);
                item.setUnAuthCount(1 - item.getAuthCount());

                // 证件号为身份证 脱敏处理
                if (item.getCredentialType() == 1) {
                    item.setCredentialNumber(DesensitizedUtil.idCardNum(item.getCredentialNumber(), 6, 4));
                }
            }
        }
        return imgOutsiderVoPage;
    }

    /**
     * 设置外部人员可见部门树
     *
     * @param dto 部门列表
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDepartmentShowForOutsider(SetDepartmentDto dto) {
        return setImgOutsiderDeptTree(dto.getDeptList(), OutSiderDeptTreeTypeEnum.OUTSIDER_SHOW);
    }

    /**
     * 设置外部人员可选部门树
     *
     * @param dto 部门列表
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDepartmentOptionForOutsider(SetDepartmentDto dto) {
        return setImgOutsiderDeptTree(dto.getDeptList(), OutSiderDeptTreeTypeEnum.OUTSIDER_OPTION);
    }

    /**
     * 设置外部人员可见部门负责人
     *
     * @param dto 部门负责人信息
     * @return
     */
    @Override
    public Boolean setDepartmentShowCustodian(SetAuditorDto dto) {
        // 根据部门ID去设置负责人,不包括全部部门的
        LambdaQueryWrapper<ImgOutsiderDept> qw = Wrappers.<ImgOutsiderDept>lambdaQuery()
                .eq(ImgOutsiderDept::getDeptId, dto.getDeptId())
                .ne(ImgOutsiderDept::getTreeType, OutSiderDeptTreeTypeEnum.ALL_DEPT.getCode());
        List<ImgOutsiderDept> outsiderDeptList = imgOutsiderDeptService.list(qw);
        if (CollUtil.isEmpty(outsiderDeptList)) {
            throw ExceptionCodeEnum.DEPT_NOT_EXIST.toServiceException();
        }
        for (ImgOutsiderDept item : outsiderDeptList) {
            item.setDeptLeader(dto.getDeptLeader());
            item.setDeptLeaderName(dto.getDeptLeaderName());
        }
        return imgOutsiderDeptService.updateBatchById(outsiderDeptList);
    }

    /**
     * 设置外部人员可见或者可选部门树
     *
     * @param deptList
     * @param outSiderDeptTreeTypeEnum
     */
    public boolean setImgOutsiderDeptTree(List<ImgOutsiderDeptVO> deptList, OutSiderDeptTreeTypeEnum outSiderDeptTreeTypeEnum) {
        if (CollUtil.isEmpty(deptList)) {
            throw ExceptionCodeEnum.PARAM_NOT_EMPTY.build("deptList");
        }
        // 将部门列表转换为map
        Map<Long, ImgOutsiderDeptVO> outsiderDeptMap = mapDeptIdToDept(outSiderDeptTreeTypeEnum);

        // 移除老的部门tree
        LambdaQueryWrapper<ImgOutsiderDept> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ImgOutsiderDept::getTreeType, outSiderDeptTreeTypeEnum.getCode());
        this.imgOutsiderDeptService.remove(queryWrapper);

        // 添加对应的部门tree
        List<ImgOutsiderDept> imgOutsiderDeptList = ImgOutsiderDeptConverter.INSTANCE.toEntity(deptList);
        for (ImgOutsiderDept item : imgOutsiderDeptList) {
            item.setTreeType(outSiderDeptTreeTypeEnum.getCode());
            ImgOutsiderDeptVO imgOutsiderDept = outsiderDeptMap.getOrDefault(item.getDeptId(), null);
            if (imgOutsiderDept != null && imgOutsiderDept.getDeptLeader() != null) {
                item.setDeptLeader(String.valueOf(imgOutsiderDept.getDeptLeader()));
                item.setDeptLeaderName(imgOutsiderDept.getDeptLeaderName());
            }
        }
        return this.imgOutsiderDeptService.saveBatch(imgOutsiderDeptList);
    }

    /**
     * 将部门列表转换为map
     *
     * @param outSiderDeptTreeTypeEnum
     * @return
     */
    public Map<Long, ImgOutsiderDeptVO> mapDeptIdToDept(OutSiderDeptTreeTypeEnum outSiderDeptTreeTypeEnum) {
        Map<Long, ImgOutsiderDeptVO> map = new HashMap<>(8);
        List<ImgOutsiderDeptVO> deptList = this.listOutsiderDeptInfo(outSiderDeptTreeTypeEnum.getCode());
        for (ImgOutsiderDeptVO dept : deptList) {
            map.put(dept.getDeptId(), dept);
        }
        return map;
    }

    @Override
    public String getInviteOutsiderImageCollectUrl() {
        return SpringUtils.getBean(ImgManageService.class).getOutsiderQrUrl(UserTypeEnum.OUTSIDER);
    }

    /**
     * 查询校外人员可见或可选部门
     *
     * @param type 0:全部部门 1：可见部门；2：可选部门
     * @return
     */
    @Override
    public List<ImgOutsiderDeptVO> listOutsiderDeptInfo(Integer type) {
        if (type == null) {
            throw ExceptionCodeEnum.PARAM_NOT_EMPTY.build("type");
        }
        LambdaQueryWrapper<ImgOutsiderDept> qw = Wrappers.lambdaQuery();
        qw.eq(ImgOutsiderDept::getTreeType, type);
        qw.orderByAsc(ImgOutsiderDept::getId);
        List<ImgOutsiderDept> imgOutsiderDeptList = imgOutsiderDeptService.list(qw);
        if (CollUtil.isNotEmpty(imgOutsiderDeptList)) {
            // 可见部门是否存在选中部门
            List<ImgOutsiderDeptVO> imgOutsiderDeptVOList = ImgOutsiderDeptConverter.INSTANCE.toVO(imgOutsiderDeptList);
            if (OutSiderDeptTreeTypeEnum.OUTSIDER_SHOW.getCode().equals(type)) {
                checkImgOutsiderDeptSelect(imgOutsiderDeptVOList);
            }
            return imgOutsiderDeptVOList;
        }
        return Collections.emptyList();
    }

    /**
     * 校验可见部门是否存在选中部门
     *
     * @param imgOutsiderDeptList 部门列表
     */
    private void checkImgOutsiderDeptSelect(List<ImgOutsiderDeptVO> imgOutsiderDeptList) {
        // 查询选中部门
        List<ImgOutsiderDept> deptList = imgOutsiderDeptService.listByTreeType(OutSiderDeptTreeTypeEnum.OUTSIDER_OPTION.getCode());
        if (CollUtil.isNotEmpty(deptList)) {
            Map<Long, ImgOutsiderDept> deptMap = deptList.stream().collect(Collectors.toMap(ImgOutsiderDept::getDeptId, imgOutsiderDept -> imgOutsiderDept));
            for (ImgOutsiderDeptVO item : imgOutsiderDeptList) {
                ImgOutsiderDept selectDept = deptMap.getOrDefault(item.getDeptId(), null);
                // 存在选中部门
                if (selectDept != null) {
                    item.setSelect(Boolean.TRUE);
                } else {
                    item.setSelect(Boolean.FALSE);
                }
            }
        }
    }
}

