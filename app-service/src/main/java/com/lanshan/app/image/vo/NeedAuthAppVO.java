package com.lanshan.app.image.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 需要授权的应用
 */
@Data
public class NeedAuthAppVO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "授权类型")
    private Long authType;
    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;
    /**
     * 操作类型 1：调用类型 2：推送类型
     */
    @ApiModelProperty(value = "操作类型 1：调用类型 2：推送类型")
    private Integer operateType;
    /**
     * 应用类型 0：内部应用 1：外部应用
     */
    @ApiModelProperty(value = "应用类型 0：内部应用 1：外部应用")
    private Integer appType;
    /**
     * 应用所属接入方ID
     */
    @ApiModelProperty(value = "应用所属接入方ID")
    private Long companyId;
    /**
     * 应用所属接入方名称
     */
    @ApiModelProperty(value = "应用所属接入方名称")
    private String companyName;
}
