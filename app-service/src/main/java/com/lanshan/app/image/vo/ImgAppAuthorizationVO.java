package com.lanshan.app.image.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户照片授权信息(ImgAuthorization)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户照片应用授权信息VO")
@Data
@ToString
public class ImgAppAuthorizationVO implements Serializable {

    private static final long serialVersionUID = -6410880165685482208L;

    @ApiModelProperty(value = "授权时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "授权类型。10：大门门禁；20：图书馆闸机；30：一卡通制证；40：宿舍闸机")
    private Integer authType;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "所属接入方名称")
    private String companyName;

    @ApiModelProperty(value = "状态 true:已授权  false:未授权")
    private Boolean status;

    public ImgAppAuthorizationVO(Integer authType, String appName) {
        this.authType = authType;
        this.appName = appName;
    }
}

