package com.lanshan.app.image.converter;


import com.lanshan.app.image.dto.ImgSysConfigDTO;
import com.lanshan.app.image.entity.ImgSysConfig;
import com.lanshan.app.image.vo.ImgSysConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 参数配置表(ImgSysConfig)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ImgSysConfigConverter {

    ImgSysConfigConverter INSTANCE = Mappers.getMapper(ImgSysConfigConverter.class);

    ImgSysConfigVO toVO(ImgSysConfig entity);

    ImgSysConfig toEntity(ImgSysConfigVO vo);

    ImgSysConfig dtoToEntity(ImgSysConfigDTO dto);

    List<ImgSysConfig> dtoToEntity(List<ImgSysConfigDTO> dto);

    List<ImgSysConfigVO> toVO(List<ImgSysConfig> entityList);

    List<ImgSysConfig> toEntity(List<ImgSysConfigVO> voList);
}


