package com.lanshan.app.vehicleregister.controller;


import com.lanshan.app.vehicleregister.annotation.VehicleOperLog;
import com.lanshan.app.vehicleregister.enums.VehicleLogBusinessType;
import com.lanshan.app.vehicleregister.enums.VehicleLogUserType;
import com.lanshan.app.vehicleregister.po.VehicleViolationType;
import com.lanshan.app.vehicleregister.service.VehicleViolationTypeService;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 违规类型管理
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 18:50
 */

@Slf4j
@RestController
@RequestMapping("/vehicle/violationTypeManage")
public class VehicleViolationTypeManageController {

    @Resource
    private VehicleViolationTypeService vehicleViolationTypeService;

    /**
     * 违规类型列表
     * <AUTHOR> yang.
     * @since 2025/03/11 18:55
     */
    @PostMapping(value = "/getViolationTypeList", produces = "application/json")
    public Result<Map<String, Object>> getVehicleRegisterPageList() {
        List<VehicleViolationType> list = vehicleViolationTypeService.list();
        Map<String, Object> map = new HashMap<>();
        map.put("records", list);
        return Result.build(map);
    }

    /**
     * 保存或修改违规类型
     * <AUTHOR> yang.
     * @since 2025/03/11 18:57
     */
    @VehicleOperLog(title = "保存或修改违规类型", businessType = VehicleLogBusinessType.UPDATE, userType = VehicleLogUserType.ADMIN)
    @PostMapping(value = "/saveOrUpdateViolationType", produces = "application/json")
    public Result<Object> saveOrUpdateVehicleRegisterPage(@RequestBody VehicleViolationType vehicleViolationType) {
        vehicleViolationTypeService.saveOrUpdate(vehicleViolationType);
        return Result.build();
    }

    /**
     * 删除违规类型
     * <AUTHOR> yang.
     * @since 2025/03/11 18:58
     */
    @VehicleOperLog(title = "删除违规类型", businessType = VehicleLogBusinessType.DELETE, userType = VehicleLogUserType.ADMIN)
    @PostMapping(value = "/removeViolationType/{id}", produces = "application/json")
    public Result<Object> removeVehicleRegisterPage(@PathVariable("id") String id) {
        vehicleViolationTypeService.removeById(id);
        return Result.build();
    }

}
