package com.lanshan.app.vehicleregister.dto;


import com.lanshan.app.common.bo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 14:35
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class VehicleRegisterPageDTO extends PageQo {

    private static final long serialVersionUID = 3058977640155547869L;

    //车牌号
    private String licensePlate;

    //用户id
    private String userId;

    //用户姓名
    private String userName;

    //用户身份
    private Integer userType;

    //用户手机号
    private String phone;

    private String department;

    //申请时间
    private String appStartTime;

    private String appEndTime;

    //审核状态
    private Integer auditStatus;

}
