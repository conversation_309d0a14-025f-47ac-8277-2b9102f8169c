package com.lanshan.app.vehicleregister.dto;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 15:08
 */

@Data
public class VehicleRegisterAuditDTO implements Serializable {

    private static final long serialVersionUID = 7104329832621450065L;

    @NotNull(message = "id不能为空")
    private Long id;

    @NotNull(message = "状态不能为空")
    private Integer auditStatus;

    //不通过理由
    private String rejectReason;

    //审核人 userId
    @NotNull(message = "审核人 userId不能为空")
    private String auditId;

    @NotNull(message = "审核人 姓名不能为空")
    private String auditName;

}
