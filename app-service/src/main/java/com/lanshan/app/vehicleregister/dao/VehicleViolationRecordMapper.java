package com.lanshan.app.vehicleregister.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.vehicleregister.dto.VehicleViolationPageDTO;
import com.lanshan.app.vehicleregister.po.VehicleViolationRecord;
import com.lanshan.app.vehicleregister.vo.VehicleViolationRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电动车违规记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-03-10
 */
@Mapper
public interface VehicleViolationRecordMapper extends BaseMapper<VehicleViolationRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<VehicleViolationRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<VehicleViolationRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<VehicleViolationRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<VehicleViolationRecord> entities);

    List<VehicleViolationRecordVO> getVehicleViolationList(@Param("dto")VehicleViolationPageDTO dto);

    Integer getVehicleViolationList_COUNT(@Param("dto")VehicleViolationPageDTO dto);

}
