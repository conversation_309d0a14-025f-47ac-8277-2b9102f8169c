package com.lanshan.app.vehicleregister.dto;


import com.lanshan.app.vehicleregister.enums.VehicleStatus;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 15:30
 */


@Data
public class VehicleRegisterAuditSaveDTO implements Serializable {

    private static final long serialVersionUID = -3249163877325478844L;

    //用户身份 0审核人 1教师 2学生 3第三类人员 4第四类人员
    @NotNull(message = "用户身份不能为空")
    private Integer userType;

    //用户姓名
    @NotNull(message = "用户姓名不能为空")
    private String userName;

    //车牌号
    @NotNull(message = "车牌号不能为空")
    private String licensePlate;

    @NotNull(message = "手机号不能为空")
    private String phone;

    //行驶证 最多两张
    private List<String> drivingLicense;

    //车辆照片 两张
    @NotNull(message = "车辆照片不能为空")
    private List<String> vehiclePhotos;

    //手写签名图片
    @NotNull(message = "手写签名图片不能为空")
    private String sign;

    private Integer auditStatus = VehicleStatus.REGISTERED.value;

    //审核人 userId
    @NotNull(message = "审核人 userId不能为空")
    private String auditId;

    @NotNull(message = "审核人 姓名不能为空")
    private String auditName;

}
