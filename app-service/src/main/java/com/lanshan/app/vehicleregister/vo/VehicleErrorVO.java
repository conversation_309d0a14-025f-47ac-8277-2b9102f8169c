package com.lanshan.app.vehicleregister.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleErrorVO implements Serializable {
    private static final long serialVersionUID = -7675043759380263606L;

    /**
     * 报错位置
     */
    private String position;
    /**
     * 原因
     */
    private String reason;
    /**
     * 描述
     */
    private String dispose;
}
