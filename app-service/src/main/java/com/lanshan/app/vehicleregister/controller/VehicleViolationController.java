package com.lanshan.app.vehicleregister.controller;


import com.lanshan.app.vehicleregister.annotation.VehicleOperLog;
import com.lanshan.app.vehicleregister.dto.VehicleViolationPageDTO;
import com.lanshan.app.vehicleregister.enums.VehicleLogBusinessType;
import com.lanshan.app.vehicleregister.enums.VehicleLogUserType;
import com.lanshan.app.vehicleregister.po.VehicleViolationRecord;
import com.lanshan.app.vehicleregister.service.VehicleViolationRecordService;
import com.lanshan.app.vehicleregister.vo.VehicleViolationRecordVO;
import com.lanshan.base.api.page.PageResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 电动车违规记录
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 16:30
 */

@Slf4j
@RestController
@RequestMapping("/vehicle/violationRecord")
public class VehicleViolationController {

    @Resource
    private VehicleViolationRecordService vehicleViolationRecordService;

    /**
     * 新增违规记录
     * <AUTHOR> yang.
     * @since 2025/03/11 16:37
     */
    @VehicleOperLog(title = "新增违规记录", businessType = VehicleLogBusinessType.INSERT, userType = VehicleLogUserType.USER)
    @PostMapping(value = "/saveVehicleViolation", produces = "application/json")
    public Result<Object> saveVehicleViolation(@RequestBody VehicleViolationRecord vehicleViolationRecord) {
        vehicleViolationRecordService.saveVehicleViolation(vehicleViolationRecord);
        return Result.build();
    }

    /**
     * 违规记录
     * <AUTHOR> yang.
     * @since 2025/03/11 17:31
     */
    @PostMapping(value = "/getVehicleViolationPageList", produces = "application/json")
    public Result<PageResult<VehicleViolationRecordVO>> getVehicleViolationPageList(@RequestBody VehicleViolationPageDTO dto){
        PageResult<VehicleViolationRecordVO> pg = vehicleViolationRecordService.getVehicleViolationPageList(dto);
        return Result.build(pg);
    }

}
