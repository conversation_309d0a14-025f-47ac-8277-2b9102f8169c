package com.lanshan.app.vehicleregister.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterAuditDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterAuditSaveDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterPageDTO;
import com.lanshan.app.vehicleregister.po.VehicleRegisterRecord;
import com.lanshan.app.vehicleregister.vo.VehicleErrorVO;
import com.lanshan.base.api.utils.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 电动车登记 记录 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-03-10
 */
public interface VehicleRegisterRecordService extends IService<VehicleRegisterRecord> {

    /**
     * 登记电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 14:01
     */
    Result<Object> saveVehicleRegister(@Valid VehicleRegisterDTO dto);

    /**
     * 审核人添加电动车登记
     * <AUTHOR> yang.
     * @since 2025/03/11 15:26
     */
    Result<Object> saveVehicleRegisterByAudit(@Valid VehicleRegisterAuditSaveDTO dto);

    /**
     * 电动车登记 记录
     * <AUTHOR> yang.
     * @since 2025/03/11 17:10
     */
    Page<VehicleRegisterRecord> getVehicleRegisterPageList(VehicleRegisterPageDTO dto);

    /**
     * 审核电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 15:14
     */
    void auditVehicleRegister(VehicleRegisterAuditDTO dto);

    /**
     * 注销电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 17:09
     * @param id 电动车登记记录id
     */
    void offVehicle(Long id);

    boolean updateVehicleOffStatusBatch(List<VehicleRegisterRecord> records);

    /**
     * 导入注销记录
     * <AUTHOR> yang.
     * @since 2025/4/18 13:50
     */
    Result<Object> importOffRecord(MultipartFile file);
}
