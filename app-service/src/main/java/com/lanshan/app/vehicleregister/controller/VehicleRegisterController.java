package com.lanshan.app.vehicleregister.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.vehicleregister.annotation.VehicleOperLog;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterAuditDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterAuditSaveDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterDTO;
import com.lanshan.app.vehicleregister.dto.VehicleRegisterPageDTO;
import com.lanshan.app.vehicleregister.enums.VehicleLogBusinessType;
import com.lanshan.app.vehicleregister.enums.VehicleLogUserType;
import com.lanshan.app.vehicleregister.po.VehicleRegisterRecord;
import com.lanshan.app.vehicleregister.service.VehicleRegisterRecordService;
import com.lanshan.app.vehicleregister.vo.VehicleErrorVO;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 车辆登记
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 10:33
 */

@Slf4j
@RestController
@RequestMapping("/vehicle/registerRecord")
public class VehicleRegisterController {

    @Resource
    private VehicleRegisterRecordService vehicleRegisterRecordService;

    /**
     * 电动车登记 记录
     * <AUTHOR> yang.
     * @since 2025/03/11 14:48
     */
    @PostMapping(value = "/getVehicleRegisterPageList", produces = "application/json")
    public Result<Page<VehicleRegisterRecord>> getVehicleRegisterPageList(@RequestBody VehicleRegisterPageDTO dto) {
        Page<VehicleRegisterRecord> pg = vehicleRegisterRecordService.getVehicleRegisterPageList(dto);
        return Result.build(pg);
    }



    /**
     * 登记电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 14:01
     */
    @VehicleOperLog(title = "登记电动车", businessType = VehicleLogBusinessType.INSERT, userType = VehicleLogUserType.USER)
    @PostMapping(value = "/saveVehicleRegister", produces = "application/json")
    public Result<Object> saveVehicleRegister(@RequestBody @Valid VehicleRegisterDTO dto) {
        Result<Object> result = vehicleRegisterRecordService.saveVehicleRegister(dto);
        return result;
    }

    /**
     * 审核人添加电动车登记
     * <AUTHOR> yang.
     * @since 2025/03/11 15:26
     */
    @VehicleOperLog(title = "审核人直接登记电动车", businessType = VehicleLogBusinessType.INSERT, userType = VehicleLogUserType.USER)
    @PostMapping(value = "/saveVehicleRegisterByAudit", produces = "application/json")
    public Result<Object> saveVehicleRegisterByAudit(@RequestBody @Valid VehicleRegisterAuditSaveDTO dto) {
        Result<Object> result = vehicleRegisterRecordService.saveVehicleRegisterByAudit(dto);
        return result;
    }

    /**
     * 审核电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 15:14
     */
    @VehicleOperLog(title = "审核电动车", businessType = VehicleLogBusinessType.UPDATE, userType = VehicleLogUserType.USER)
    @PostMapping(value = "/auditVehicleRegister", produces = "application/json")
    public Result<Object> auditVehicleRegister(@RequestBody @Valid VehicleRegisterAuditDTO dto) {
        vehicleRegisterRecordService.auditVehicleRegister(dto);
        return Result.build();
    }

    /**
     * 注销电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 15:14
     */
    @VehicleOperLog(title = "注销电动车", businessType = VehicleLogBusinessType.UPDATE, userType = VehicleLogUserType.USER)
    @PostMapping(value = "/offVehicle/{id}", produces = "application/json")
    public Result<Object> offVehicle(@PathVariable("id") Long id) {
        vehicleRegisterRecordService.offVehicle(id);
        return Result.build();
    }

    /**
     * 后管用户注销电动车
     * <AUTHOR> yang.
     * @since 2025/03/11 15:14
     */
    @VehicleOperLog(title = "注销电动车(后管)", businessType = VehicleLogBusinessType.UPDATE, userType = VehicleLogUserType.ADMIN)
    @PostMapping(value = "/offVehicleByAdmin/{id}", produces = "application/json")
    public Result<Object> offVehicleByAdmin(@PathVariable("id") Long id) {
        vehicleRegisterRecordService.offVehicle(id);
        return Result.build();
    }

    @VehicleOperLog(title = "导入电动车注销记录", businessType = VehicleLogBusinessType.IMPORT, userType = VehicleLogUserType.ADMIN)
    @PostMapping(value = "/importOffRecord", produces = "application/json")
    public Result<Object> importOffRecord(@RequestParam("file") MultipartFile file) {
        return vehicleRegisterRecordService.importOffRecord(file);
    }

}
