package com.lanshan.app.vehicleregister.vo;


import com.lanshan.app.vehicleregister.po.VehicleViolationType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/10 20:51
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleEnumResultVO implements Serializable {
    private static final long serialVersionUID = 7784756176175196998L;

    //操作日志业务类型
    private List<VehicleEnumTypeVO> businessTypeList;

    //车辆审核状态
    private List<VehicleEnumTypeVO> auditStatusList;

    //用户身份
    private List<VehicleEnumTypeVO> userTypeList;

    //违规类型
    private List<VehicleViolationType> violationTypeList;

}
