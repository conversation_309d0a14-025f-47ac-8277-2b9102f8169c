package com.lanshan.app.infoflowquery.converter;


import com.lanshan.app.infoflowquery.entity.ZuelBysxx;
import com.lanshan.app.infoflowquery.vo.ZuelBysxxVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * (ZuelBysxx)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ZuelBysxxConverter {

    ZuelBysxxConverter INSTANCE = Mappers.getMapper(ZuelBysxxConverter.class);

    ZuelBysxxVO toVO(ZuelBysxx entity);

    ZuelBysxx toEntity(ZuelBysxxVO vo);

    List<ZuelBysxxVO> toVO(List<ZuelBysxx> entityList);

    List<ZuelBysxx> toEntity(List<ZuelBysxxVO> voList);
}


