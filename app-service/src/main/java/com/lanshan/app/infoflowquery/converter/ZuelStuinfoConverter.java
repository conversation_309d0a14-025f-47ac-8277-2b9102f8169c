package com.lanshan.app.infoflowquery.converter;


import com.lanshan.app.infoflowquery.entity.ZuelStuinfo;
import com.lanshan.app.infoflowquery.vo.ZuelStuinfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * (ZuelStuinfo)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ZuelStuinfoConverter {

    ZuelStuinfoConverter INSTANCE = Mappers.getMapper(ZuelStuinfoConverter.class);

    ZuelStuinfoVO toVO(ZuelStuinfo entity);

    ZuelStuinfo toEntity(ZuelStuinfoVO vo);

    List<ZuelStuinfoVO> toVO(List<ZuelStuinfo> entityList);

    List<ZuelStuinfo> toEntity(List<ZuelStuinfoVO> voList);
}


