package com.lanshan.app.infoflowquery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("学生信息")
public class StuInfoVO {

    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称")
    private String collegeName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 学号
     */
    @ApiModelProperty(value = "学号")
    private String userId;
}
