package com.lanshan.app.infoflowquery.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.infoflowquery.entity.ZuelBysxx;
import com.lanshan.app.infoflowquery.entity.ZuelStuinfo;
import com.lanshan.app.infoflowquery.entity.ZuelUserinfo;
import com.lanshan.app.infoflowquery.service.ZuelBysxxService;
import com.lanshan.app.infoflowquery.service.ZuelInfoFlowQueryService;
import com.lanshan.app.infoflowquery.service.ZuelStuinfoService;
import com.lanshan.app.infoflowquery.service.ZuelUserinfoService;
import com.lanshan.app.infoflowquery.vo.MasterGraduationPaperStatusVO;
import com.lanshan.app.infoflowquery.vo.StuInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
public class ZuelInfoFlowQueryServiceImpl implements ZuelInfoFlowQueryService {

    @Resource
    private ZuelBysxxService zuelBysxxService;

    @Resource
    private ZuelStuinfoService zuelStuinfoService;

    @Resource
    private ZuelUserinfoService zuelUserinfoService;


    @Override
    public MasterGraduationPaperStatusVO queryStatus() {
        String userId = SecurityContextHolder.getUserIdStr();
        log.info("查询用户id:{}", userId);
        ZuelBysxx zuelBysxx = zuelBysxxService.getOne(Wrappers.lambdaQuery(ZuelBysxx.class).eq(ZuelBysxx::getXh, userId).last("limit 1"));
        MasterGraduationPaperStatusVO masterGraduationPaperStatusVO = new MasterGraduationPaperStatusVO();
        //查询学生信息
        StuInfoVO stuInfoVO = zuelBysxxService.getStuInfoByUserId(userId);
        masterGraduationPaperStatusVO.setStuInfo(stuInfoVO);
        if (Objects.isNull(zuelBysxx)) {
            return masterGraduationPaperStatusVO;
        }
        masterGraduationPaperStatusVO.setMasterFlag(true);
        ZuelUserinfo userinfo = zuelUserinfoService.getOne(Wrappers.lambdaQuery(ZuelUserinfo.class).eq(ZuelUserinfo::getUserid, userId).last("limit 1"));
        if (Objects.isNull(userinfo)) {
            return masterGraduationPaperStatusVO;
        }
        masterGraduationPaperStatusVO.setInLibraryFlag(true);
        ZuelStuinfo zuelStuinfo = zuelStuinfoService.getOne(Wrappers.lambdaQuery(ZuelStuinfo.class).eq(ZuelStuinfo::getUserid, userId)
                .eq(ZuelStuinfo::getMarkstate, 2)
                .last("limit 1"));
        if (Objects.isNull(zuelStuinfo)) {
            return masterGraduationPaperStatusVO;
        }
        masterGraduationPaperStatusVO.setGraduationPagerFlag(true);
        return masterGraduationPaperStatusVO;
    }
}
