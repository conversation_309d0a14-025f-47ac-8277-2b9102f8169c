package com.lanshan.app.infoflowquery.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.infoflowquery.dao.ZuelBysxxDao;
import com.lanshan.app.infoflowquery.entity.ZuelBysxx;
import com.lanshan.app.infoflowquery.service.ZuelBysxxService;
import com.lanshan.app.infoflowquery.vo.StuInfoVO;
import org.springframework.stereotype.Service;

/**
 * (ZuelBysxx)表服务实现类
 *
 * <AUTHOR>
 */
@Service("zuelBysxxService")
public class ZuelBysxxServiceImpl extends ServiceImpl<ZuelBysxxDao, ZuelBysxx> implements ZuelBysxxService {

    @Override
    public StuInfoVO getStuInfoByUserId(String userId) {
        return this.baseMapper.getStuInfoByUserId(userId);
    }
}

