package com.lanshan.app.infoflowquery.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * (ZuelUserinfo)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "VO")
@Data
@ToString
public class ZuelUserinfoVO implements Serializable {

    @ApiModelProperty(value = "")
    private String userid;

    @ApiModelProperty(value = "")
    private String username;

    @ApiModelProperty(value = "")
    private String college;
}

