package com.lanshan.app.infoflowquery.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.infoflowquery.entity.ZuelBysxx;
import com.lanshan.app.infoflowquery.vo.StuInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (ZuelBysxx)表数据库访问层
 *
 * <AUTHOR>
 */
public interface ZuelBysxxDao extends BaseMapper<ZuelBysxx> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ZuelBysxx> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ZuelBysxx> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ZuelBysxx> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ZuelBysxx> entities);


    /**
     * 根据userId查询学生信息
     *
     * @param userId 学号
     * @return
     */
    StuInfoVO getStuInfoByUserId(String userId);

}

