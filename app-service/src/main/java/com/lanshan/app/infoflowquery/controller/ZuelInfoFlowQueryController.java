package com.lanshan.app.infoflowquery.controller;


import com.lanshan.app.infoflowquery.service.ZuelInfoFlowQueryService;
import com.lanshan.app.infoflowquery.vo.MasterGraduationPaperStatusVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * (ZuelUserinfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("zuelInfoFlowQuery")
@Api(tags = "(ZuelInfoFlowQuery)控制层", hidden = true)
public class ZuelInfoFlowQueryController {
    /**
     * 服务对象
     */
    @Resource
    private ZuelInfoFlowQueryService zuelInfoFlowQueryService;

    /**
     * 查询状态
     *
     * @return 所有数据
     */
    @ApiOperation("查询状态")
    @GetMapping("/queryStatus")
    public Result<MasterGraduationPaperStatusVO> queryStatus() {
        return Result.build(zuelInfoFlowQueryService.queryStatus());
    }
}

