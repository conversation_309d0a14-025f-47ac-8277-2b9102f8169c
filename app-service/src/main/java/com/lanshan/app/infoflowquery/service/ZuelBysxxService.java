package com.lanshan.app.infoflowquery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.infoflowquery.entity.ZuelBysxx;
import com.lanshan.app.infoflowquery.vo.StuInfoVO;

/**
 * (ZuelBysxx)表服务接口
 *
 * <AUTHOR>
 */
public interface ZuelBysxxService extends IService<ZuelBysxx> {

    /**
     * 根据userId查询学生信息
     *
     * @param userId 学号
     * @return
     */
    StuInfoVO getStuInfoByUserId(String userId);
}

