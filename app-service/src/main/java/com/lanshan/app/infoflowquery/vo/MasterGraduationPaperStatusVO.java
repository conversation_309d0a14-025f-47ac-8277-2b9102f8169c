package com.lanshan.app.infoflowquery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel("研究生毕业论文状态")
public class MasterGraduationPaperStatusVO {

    /**
     * 研究生毕业状态
     */
    @ApiModelProperty(value = "研究生毕业状态")
    private Boolean masterFlag;

    /**
     * 是否在图书馆系统中
     */
    @ApiModelProperty(value = "是否在图书馆系统中")
    private Boolean inLibraryFlag;

    /**
     * 论文是否合格
     */
    @ApiModelProperty(value = "论文是否合格")
    private Boolean graduationPagerFlag;

    /**
     * 学生信息
     */
    @ApiModelProperty(value = "学生信息")
    private StuInfoVO stuInfo;


}
