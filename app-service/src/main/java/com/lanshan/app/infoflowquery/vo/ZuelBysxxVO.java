package com.lanshan.app.infoflowquery.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * (ZuelBysxx)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "VO")
@Data
@ToString
public class ZuelBysxxVO implements Serializable {

    @ApiModelProperty(value = "")
    private String wid;

    @ApiModelProperty(value = "")
    private String xh;

    @ApiModelProperty(value = "")
    private String yqzxf;

    @ApiModelProperty(value = "")
    private String wczxf;

    @ApiModelProperty(value = "")
    private String xwkyqxf;

    @ApiModelProperty(value = "")
    private String xwkwcxf;

    @ApiModelProperty(value = "")
    private String bxhjyqxf;

    @ApiModelProperty(value = "")
    private String bxhjwcxf;

    @ApiModelProperty(value = "")
    private String shzt;

    @ApiModelProperty(value = "")
    private String czz;

    @ApiModelProperty(value = "")
    private String czzxm;

    @ApiModelProperty(value = "")
    private String czrq;
}

