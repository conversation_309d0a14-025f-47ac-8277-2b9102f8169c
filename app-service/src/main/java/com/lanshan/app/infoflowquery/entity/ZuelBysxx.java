package com.lanshan.app.infoflowquery.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * (ZuelBysxx)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class ZuelBysxx extends Model<ZuelBysxx> {
    private String wid;
    private String xh;
    private String yqzxf;
    private String wczxf;
    private String xwkyqxf;
    private String xwkwcxf;
    private String bxhjyqxf;
    private String bxhjwcxf;
    private String shzt;
    private String czz;
    private String czzxm;
    private String czrq;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.wid;
    }
}

