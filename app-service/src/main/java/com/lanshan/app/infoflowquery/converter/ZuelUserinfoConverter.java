package com.lanshan.app.infoflowquery.converter;


import com.lanshan.app.infoflowquery.entity.ZuelUserinfo;
import com.lanshan.app.infoflowquery.vo.ZuelUserinfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * (ZuelUserinfo)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ZuelUserinfoConverter {

    ZuelUserinfoConverter INSTANCE = Mappers.getMapper(ZuelUserinfoConverter.class);

    ZuelUserinfoVO toVO(ZuelUserinfo entity);

    ZuelUserinfo toEntity(ZuelUserinfoVO vo);

    List<ZuelUserinfoVO> toVO(List<ZuelUserinfo> entityList);

    List<ZuelUserinfo> toEntity(List<ZuelUserinfoVO> voList);
}


