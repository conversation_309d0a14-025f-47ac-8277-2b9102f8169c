package com.lanshan.app.infoflowquery.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * (ZuelStuinfo)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "VO")
@Data
@ToString
public class ZuelStuinfoVO implements Serializable {

    @ApiModelProperty(value = "")
    private String userid;

    @ApiModelProperty(value = "")
    private String username;

    @ApiModelProperty(value = "")
    private String college;

    @ApiModelProperty(value = "")
    private Integer markstate;
}

