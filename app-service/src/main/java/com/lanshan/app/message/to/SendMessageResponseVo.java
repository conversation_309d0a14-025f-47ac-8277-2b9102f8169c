package com.lanshan.app.message.to;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @ProjectName: ccadult-work-weixin-platform
 * @Package: com.lanshan.message.feign.to
 * @ClassName: SendMessageResponseVo
 * @Author: zhaoyong
 * @Email: <EMAIL>
 * @Date: 2022/12/28 11:54
 * @Version: 1.0
 */
@Data
@ApiModel(value = "发送应用消息响应结果")
public class SendMessageResponseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "返回码")
    private Integer errcode;
    @ApiModelProperty(value = "对返回码的文本描述内容")
    private String errmsg;
    @ApiModelProperty(value = "不合法的userid，不区分大小写，统一转为小写。多个接收者用‘|’分隔")
    private String invaliduser;
    @ApiModelProperty(value = "不合法的partyid。多个接收者用‘|’分隔")
    private String invalidparty;
    @ApiModelProperty(value = "不合法的标签id。多个接收者用‘|’分隔")
    private String invalidtag;
    @ApiModelProperty(value = "没有基础接口许可(包含已过期)的userid。多个接收者用‘|’分隔")
    private String unlicenseduser;
    @ApiModelProperty(value = "消息id，用于撤回应用消息")
    private String msgid;
    @ApiModelProperty(value = "仅消息类型为“按钮交互型”，“投票选择型”和“多项选择型”的模板卡片消息返回，应用可使用response_code调用更新模版卡片消息接口，72小时内有效，且只能使用一次")
    private String response_code;

}
