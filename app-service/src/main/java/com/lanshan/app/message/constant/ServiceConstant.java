package com.lanshan.app.message.constant;

/**
 * @Description: 微服务常量
 * @ProjectName: ccadult-work-weixin-platform
 * @Package: com.lanshan.commons.tools.constant
 * @ClassName: ServiceConstant
 * @Author: zhaoyong
 * @Email: <EMAIL>
 * @Date: 2022/1/27 12:05
 * @Version: 1.0
 */
public interface ServiceConstant {
    /** 华师数据中心基础服务-上下文路径 */

    /** 系统消息管理服务-名称 */
    public static final String MESSAGE_SERVICE = "message-service";
    /** 系统消息管理服务-Feign路径 */
    public static final String MESSAGE_SERVICE_FEIGN_PATH = "/feign/message";
    /** 系统消息管理服务-上下文路径 */
    public static final String MESSAGE_SERVICE_CONTEXT_PATH = "/message";
}
