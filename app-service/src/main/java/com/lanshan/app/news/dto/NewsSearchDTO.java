package com.lanshan.app.news.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "新闻搜索DTO")
public class NewsSearchDTO implements Serializable {
    private static final long serialVersionUID = -2948663209200623199L;

    @ApiModelProperty(value = "当前页码", example = "1")
    private int page;

    @ApiModelProperty(value = "每页大小", example = "10")
    private int size;

    @ApiModelProperty(value = "新闻类型ID", example = "0")
    private int newsTypeId;

    @ApiModelProperty(value = "搜索关键字")
    private String name;

    @ApiModelProperty(value = "开始日期", example = "2024-03-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期", example = "2024-03-02")
    private String endDate;
}
