package com.lanshan.app.news.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 新闻类型(NewsType)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新闻类型VO")
@Data
@ToString
public class NewsTypeVO implements Serializable {

    private static final long serialVersionUID = 104931293251961149L;
    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "类型名称")
    private String name;
}

