package com.lanshan.app.news.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 新闻信息表(NewsInformation)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新闻信息表VO")
@Data
@ToString
public class NewsInformationVO implements Serializable {

    private static final long serialVersionUID = 5358664044304882612L;
    @ApiModelProperty(value = "新闻唯一标识")
    private Long id;

    @ApiModelProperty(value = "新闻来源地址")
    private String srcUrl;

    @ApiModelProperty(value = "新闻标题")
    private String title;

    @ApiModelProperty(value = "新闻摘要")
    private String summary;

    @ApiModelProperty(value = "新闻内容")
    private String content;

    @ApiModelProperty(value = "新闻分类ID")
    private Integer typeId;

    @ApiModelProperty(value = "新闻创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createdTime;

    @ApiModelProperty(value = "新闻发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date publishTime;

    @ApiModelProperty(value = "新闻查看次数")
    private Integer viewCount;

    @ApiModelProperty(value = "新闻收藏人数")
    private Integer favoriteCount;

    @ApiModelProperty(value = "新闻点赞人数")
    private Integer likeCount;

    @ApiModelProperty(value = "是否有图片")
    private Boolean hasImage;

    @ApiModelProperty(value = "是否有附件")
    private Boolean hasFile;

    @ApiModelProperty(value = "缩略图地址")
    private String thumbImage;

    @ApiModelProperty(value = "是否置顶")
    private Boolean isTop;

    @ApiModelProperty(value = "置顶排序")
    private Integer sort;

    @ApiModelProperty(value = "状态")
    private Integer status;
}

