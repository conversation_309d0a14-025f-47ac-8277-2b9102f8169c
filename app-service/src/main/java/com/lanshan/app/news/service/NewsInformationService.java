package com.lanshan.app.news.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.news.dto.NewsAddDTO;
import com.lanshan.app.news.entity.NewsInformation;
import com.lanshan.app.news.vo.NewsBannerVO;
import com.lanshan.app.news.vo.NewsInformationVO;

import java.util.List;

/**
 * 新闻信息表(NewsInformation)表服务接口
 *
 * <AUTHOR>
 */
public interface NewsInformationService extends IService<NewsInformation> {

    /**
     * 新增新闻
     *
     * @param news 新闻列表
     * @return 是否成功
     */
    Boolean addNews(List<NewsAddDTO> news);

    /**
     * 根据ID列表获取新闻列表
     *
     * @param idList ID列表
     * @return 新闻列表
     */
    List<NewsInformationVO> getNewsList(List<Long> idList);

    /**
     * 更新新闻点赞数
     *
     * @param id    新闻ID
     * @param count 点赞数
     * @return 是否成功
     */
    Boolean updateNewsLikeCount(Long id, Integer count);

    /**
     * 更新新闻收藏数
     *
     * @param id    新闻ID
     * @param count 收藏数
     * @return 是否成功
     */
    Boolean updateNewsFavoriteCount(Long id, Integer count);


    /**
     * 获取轮播图列表
     *
     * @return 轮播图列表
     */
    List<NewsBannerVO> getBannerList();

    /**
     * 创建索引
     *
     * @return 是否成功
     */
    Boolean createIndex();

    /**
     * 更新摘要
     *
     * @return 是否成功
     */
    Boolean updateSummary();
}

