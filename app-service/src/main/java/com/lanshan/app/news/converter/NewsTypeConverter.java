package com.lanshan.app.news.converter;


import com.lanshan.app.news.entity.NewsType;
import com.lanshan.app.news.vo.NewsTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 新闻类型(NewsType)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewsTypeConverter {

    NewsTypeConverter INSTANCE = Mappers.getMapper(NewsTypeConverter.class);

    NewsTypeVO toVO(NewsType entity);

    NewsType toEntity(NewsTypeVO vo);

    List<NewsTypeVO> toVO(List<NewsType> entityList);

    List<NewsType> toEntity(List<NewsTypeVO> voList);
}


