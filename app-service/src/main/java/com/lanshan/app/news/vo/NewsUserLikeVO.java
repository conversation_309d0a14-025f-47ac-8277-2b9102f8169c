package com.lanshan.app.news.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户点赞记录表(NewsUserLike)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户点赞记录表VO")
@Data
@ToString
public class NewsUserLikeVO implements Serializable {

    private static final long serialVersionUID = -6275277479879729716L;
    @ApiModelProperty(value = "点赞唯一标识")
    private Long id;

    @ApiModelProperty(value = "用户标识")
    private String userId;

    @ApiModelProperty(value = "新闻标识")
    private Long newsId;

    @ApiModelProperty(value = "点赞时间")
    private Date createTime;
}

