package com.lanshan.app.news.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户收藏记录表(NewsUserFavorite)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户收藏记录表VO")
@Data
@ToString
public class NewsUserFavoriteVO implements Serializable {

    private static final long serialVersionUID = -8932364665342174739L;
    @ApiModelProperty(value = "收藏唯一标识")
    private Long id;

    @ApiModelProperty(value = "用户标识")
    private String userId;

    @ApiModelProperty(value = "新闻标识")
    private Long newsId;

    @ApiModelProperty(value = "收藏时间")
    private Date createTime;
}

