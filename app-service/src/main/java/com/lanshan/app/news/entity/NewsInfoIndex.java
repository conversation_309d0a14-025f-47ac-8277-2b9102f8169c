package com.lanshan.app.news.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;
import org.dromara.easyes.annotation.rely.RefreshPolicy;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@IndexName(value = "news-info-index", refreshPolicy = RefreshPolicy.IMMEDIATE)
@ApiModel(value = "新闻信息")
public class NewsInfoIndex implements Serializable {
    private static final long serialVersionUID = 7740765244323961530L;
    /**
     * 新闻唯一标识
     */
    @IndexId(type = IdType.CUSTOMIZE)
    @ApiModelProperty(value = "新闻唯一标识")
    private Long id;
    /**
     * 新闻来源地址
     */
    @IndexField(fieldType = FieldType.TEXT)
    @ApiModelProperty(value = "新闻来源地址")
    private String srcUrl;
    /**
     * 新闻标题
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "hanlp_index", searchAnalyzer = "hanlp_index")
    @ApiModelProperty(value = "新闻标题")
    private String title;
    /**
     * 新闻摘要
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "hanlp_index", searchAnalyzer = "hanlp_index")
    @ApiModelProperty(value = "新闻摘要")
    private String summary;
    /**
     * 新闻内容
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "hanlp_index", searchAnalyzer = "hanlp_index")
    @ApiModelProperty(value = "新闻内容")
    private String content;
    /**
     * 新闻分类ID
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "新闻分类ID")
    private Integer typeId;
    /**
     * 新闻创建时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "新闻创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createdTime;
    /**
     * 新闻发布时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss", fieldData = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "新闻发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date publishTime;
    /**
     * 新闻查看次数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "新闻查看次数")
    private Integer viewCount;
    /**
     * 新闻收藏人数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "新闻收藏人数")
    private Integer favoriteCount;
    /**
     * 新闻点赞人数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "新闻点赞人数")
    private Integer likeCount;
    /**
     * 是否有图片
     */
    @IndexField(fieldType = FieldType.BOOLEAN)
    @ApiModelProperty(value = "是否有图片")
    private Boolean hasImage;
    /**
     * 是否有附件
     */
    @IndexField(fieldType = FieldType.BOOLEAN)
    @ApiModelProperty(value = "是否有附件")
    private Boolean hasFile;
    /**
     * 缩略图地址
     */
    @IndexField(fieldType = FieldType.TEXT)
    @ApiModelProperty(value = "缩略图地址")
    private String thumbImage;
    /**
     * 是否置顶
     */
    @IndexField(fieldType = FieldType.BOOLEAN)
    @ApiModelProperty(value = "是否置顶")
    private Boolean isTop;
    /**
     * 置顶排序
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "置顶排序")
    private Integer sort;
    /**
     * 状态
     */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "状态")
    private Integer status;
}
