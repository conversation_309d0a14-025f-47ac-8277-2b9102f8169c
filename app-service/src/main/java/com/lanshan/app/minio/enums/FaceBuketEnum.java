package com.lanshan.app.minio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 复制对象模式枚举
 */
@AllArgsConstructor
@Getter
public enum FaceBuketEnum {

    //未加密到未加密
    FACE_IMG("face-img", "不加密buketName"),
    //未加密到加密
    FACE_IMG_ENCRYPT("face-img-encrypt", "加密buketName"),
    //跳蚤市场
    FLEA_MARKET("flea-market", "跳蚤市场buketName"),

    ;
    private final String name;

    private final String desc;

}
