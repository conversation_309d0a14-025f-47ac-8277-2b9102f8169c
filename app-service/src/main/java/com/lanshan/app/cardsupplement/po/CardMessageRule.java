package com.lanshan.app.cardsupplement.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * Description: 补卡消息推送规则
 * author: jiacheng yang.
 * Date: 2025-02-28
 */

@TableName(value = "card_message_rule",schema = "standard_app")
public class CardMessageRule implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //取卡地点 1武昌校区 2嘉鱼校区
    private Integer cardLocation;

    //卡片状态 3 正常取卡 4 预约取卡 5关闭并退款
    private Integer cardStatus;

    //模板id
    private Long templateId;

    public Long getId() {
    	return id;
    }

    public void setId(Long id) {
    	this.id = id;
    }

    public Integer getCardLocation() {
    	return cardLocation;
    }

    public void setCardLocation(Integer cardLocation) {
    	this.cardLocation = cardLocation;
    }

    public Integer getCardStatus() {
    	return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
    	this.cardStatus = cardStatus;
    }

    public Long getTemplateId() {
    	return templateId;
    }

    public void setTemplateId(Long templateId) {
    	this.templateId = templateId;
    }


    @Override
    public String toString() {
	    return "CardMessageRule{" +
	            ", id=" + id +
	            ", cardLocation=" + cardLocation +
	            ", cardStatus=" + cardStatus +
	            ", templateId=" + templateId +
	    "}";
    }

}
