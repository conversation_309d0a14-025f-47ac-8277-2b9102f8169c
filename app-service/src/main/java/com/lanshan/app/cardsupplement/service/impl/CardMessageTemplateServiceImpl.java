package com.lanshan.app.cardsupplement.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.app.cardsupplement.po.CardMessageRule;
import com.lanshan.app.cardsupplement.po.CardMessageTemplate;
import com.lanshan.app.cardsupplement.dao.CardMessageTemplateMapper;
import com.lanshan.app.cardsupplement.service.CardMessageRuleService;
import com.lanshan.app.cardsupplement.service.CardMessageTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.cardsupplement.vo.CardMessageTemplateVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 补卡消息模板 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-02-27
 */
@Service
public class CardMessageTemplateServiceImpl extends ServiceImpl<CardMessageTemplateMapper, CardMessageTemplate> implements CardMessageTemplateService {

    @Resource
    private CardMessageTemplateMapper cardMessageTemplateMapper;

    @Override
    public CardMessageTemplate getTemplateByRule(Integer cardLocation, Integer cardStatus) {
        return cardMessageTemplateMapper.getTemplateByRule(cardLocation, cardStatus);
    }

    @Override
    public Map<String, CardMessageTemplateVO> getTemplateMap() {
        Map<String, CardMessageTemplateVO> map = new HashMap<>();
        List<CardMessageTemplateVO> templates = cardMessageTemplateMapper.getTemplatesAndRule();
        if (CollUtil.isEmpty(templates)){
            return map;
        }
        for (CardMessageTemplateVO template : templates) {
            String messageKey = CardMessageTemplateVO.getMessageKey(template.getCardLocation(), template.getCardStatus());
            map.put(messageKey, template);
        }

        return map;
    }
}
