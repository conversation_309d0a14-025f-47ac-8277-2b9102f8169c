package com.lanshan.app.cardsupplement.dto;


import com.lanshan.app.common.bo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: TODO
 * Author: ji<PERSON>ng yang.
 * Date: 2025/03/03 9:57
 * Version: 1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class CardOperLogPageDTO extends PageQo {
    private static final long serialVersionUID = 1652760271390487028L;

    //模块标题
    private String title;

    //操作人姓名
    private String userName;

    //业务类型（0其它 1新增 2修改 3删除 4微信下单 5微信付款）
    private Integer businessType;

    //操作状态（0成功 1失败）
    private Integer status;

    //操作时间
    private String startTime;

    private String endTime;

}
