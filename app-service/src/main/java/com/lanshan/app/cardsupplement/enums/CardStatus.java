package com.lanshan.app.cardsupplement.enums;

import com.lanshan.app.cardsupplement.vo.CardStatusVO;
import com.lanshan.app.wechatpay.enums.WeChatPayTradeType;

import java.util.*;
import java.util.stream.Collectors;

public enum CardStatus {

    UNPAID(1,"未支付"),
    PRODUCTION(2,"制卡中"),
    NORMAL(3,"正常取卡"),
    APPOINTMENT(4,"预约取卡"),
    REFUND(5,"关闭并退款");

    public final Integer status;
    public final String name;

    CardStatus(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static String getName(Integer status) {
        for (CardStatus cardStatus : CardStatus.values()) {
            if (cardStatus.status.equals(status)) {
                return cardStatus.name;
            }
        }
        return "-";
    }

    public CardStatusVO toVO() {
        return new CardStatusVO(this.status, this.name);
    }

    public static List<CardStatusVO> getAllCardStatus() {
        return Arrays.stream(CardStatus.values())
                .map(CardStatus::toVO)
                //去掉未支付
                .filter(v -> !(v.getStatus().equals(CardStatus.UNPAID.status)))
                .collect(Collectors.toList());
    }

}
