package com.lanshan.app.cardsupplement.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.cardsupplement.dto.CardOperLogPageDTO;
import com.lanshan.app.cardsupplement.po.CardOperLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 补卡申请操作日志 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-02-28
 */
public interface CardOperLogService extends IService<CardOperLog> {

    void saveCardOperLog(CardOperLog cardOperLog);

    Page<CardOperLog> getCardOperLogPageList(CardOperLogPageDTO dto);
}
