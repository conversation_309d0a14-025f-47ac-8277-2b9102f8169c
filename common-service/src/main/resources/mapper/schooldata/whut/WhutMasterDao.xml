<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutMasterDao">

    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutMaster" id="WhutMasterMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="xh" column="xh" jdbcType="VARCHAR"/>
        <result property="xm" column="xm" jdbcType="VARCHAR"/>
        <result property="wwxm" column="wwxm" jdbcType="VARCHAR"/>
        <result property="xmpy" column="xmpy" jdbcType="VARCHAR"/>
        <result property="cym" column="cym" jdbcType="VARCHAR"/>
        <result property="xbm" column="xbm" jdbcType="VARCHAR"/>
        <result property="csrq" column="csrq" jdbcType="TIMESTAMP"/>
        <result property="csdm" column="csdm" jdbcType="VARCHAR"/>
        <result property="jg" column="jg" jdbcType="VARCHAR"/>
        <result property="mzm" column="mzm" jdbcType="VARCHAR"/>
        <result property="gjdqm" column="gjdqm" jdbcType="VARCHAR"/>
        <result property="sfzjlxm" column="sfzjlxm" jdbcType="VARCHAR"/>
        <result property="sfzjh" column="sfzjh" jdbcType="VARCHAR"/>
        <result property="hyzkm" column="hyzkm" jdbcType="VARCHAR"/>
        <result property="gatqwm" column="gatqwm" jdbcType="VARCHAR"/>
        <result property="zzmmm" column="zzmmm" jdbcType="VARCHAR"/>
        <result property="jkzkm" column="jkzkm" jdbcType="VARCHAR"/>
        <result property="xxm" column="xxm" jdbcType="VARCHAR"/>
        <result property="sfzjyxq" column="sfzjyxq" jdbcType="VARCHAR"/>
        <result property="sfdszn" column="sfdszn" jdbcType="VARCHAR"/>
        <result property="hkxzm" column="hkxzm" jdbcType="VARCHAR"/>
        <result property="hkszd" column="hkszd" jdbcType="VARCHAR"/>
        <result property="xslbm" column="xslbm" jdbcType="VARCHAR"/>
        <result property="dsgh" column="dsgh" jdbcType="VARCHAR"/>
        <result property="jdxwm" column="jdxwm" jdbcType="VARCHAR"/>
        <result property="jdxlm" column="jdxlm" jdbcType="VARCHAR"/>
        <result property="jtzz" column="jtzz" jdbcType="VARCHAR"/>
        <result property="yb" column="yb" jdbcType="VARCHAR"/>
        <result property="sjhm" column="sjhm" jdbcType="VARCHAR"/>
        <result property="ksh" column="ksh" jdbcType="VARCHAR"/>
        <result property="bmh" column="bmh" jdbcType="VARCHAR"/>
        <result property="rxqgzdw" column="rxqgzdw" jdbcType="VARCHAR"/>
        <result property="dxwpdw" column="dxwpdw" jdbcType="VARCHAR"/>
        <result property="hshdsghzjlhcf" column="hshdsghzjlhcf" jdbcType="VARCHAR"/>
        <result property="lqlbm" column="lqlbm" jdbcType="VARCHAR"/>
        <result property="pyztm" column="pyztm" jdbcType="VARCHAR"/>
        <result property="sssfdb" column="sssfdb" jdbcType="VARCHAR"/>
        <result property="lqnf" column="lqnf" jdbcType="VARCHAR"/>
        <result property="sfwgr" column="sfwgr" jdbcType="VARCHAR"/>
        <result property="xjztm" column="xjztm" jdbcType="VARCHAR"/>
        <result property="sfzz" column="sfzz" jdbcType="VARCHAR"/>
        <result property="qrzhfqrz" column="qrzhfqrz" jdbcType="VARCHAR"/>
        <result property="tstamp" column="tstamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_master(xh, xm, wwxm, xmpy, cym, xbm, csrq, csdm, jg, mzm, gjdqm, sfzjlxm, sfzjh, hyzkm, gatqwm, zzmmm, jkzkm, xxm, sfzjyxq, sfdszn, hkxzm, hkszd, xslbm, dsgh, jdxwm, jdxlm, jtzz, yb, sjhm, ksh, bmh, rxqgzdw, dxwpdw, hshdsghzjlhcf, lqlbm, pyztm, sssfdb, lqnf, sfwgr, xjztm, sfzz, qrzhfqrz, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.xh} , #{entity.xm} , #{entity.wwxm} , #{entity.xmpy} , #{entity.cym} , #{entity.xbm} , #{entity.csrq} , #{entity.csdm} , #{entity.jg} , #{entity.mzm} , #{entity.gjdqm} , #{entity.sfzjlxm} , #{entity.sfzjh} , #{entity.hyzkm} , #{entity.gatqwm} , #{entity.zzmmm} , #{entity.jkzkm} , #{entity.xxm} , #{entity.sfzjyxq} , #{entity.sfdszn} , #{entity.hkxzm} , #{entity.hkszd} , #{entity.xslbm} , #{entity.dsgh} , #{entity.jdxwm} , #{entity.jdxlm} , #{entity.jtzz} , #{entity.yb} , #{entity.sjhm} , #{entity.ksh} , #{entity.bmh} , #{entity.rxqgzdw} , #{entity.dxwpdw} , #{entity.hshdsghzjlhcf} , #{entity.lqlbm} , #{entity.pyztm} , #{entity.sssfdb} , #{entity.lqnf} , #{entity.sfwgr} , #{entity.xjztm} , #{entity.sfzz} , #{entity.qrzhfqrz} , #{entity.tstamp})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_master(xh, xm, wwxm, xmpy, cym, xbm, csrq, csdm, jg, mzm, gjdqm, sfzjlxm, sfzjh, hyzkm, gatqwm, zzmmm, jkzkm, xxm, sfzjyxq, sfdszn, hkxzm, hkszd, xslbm, dsgh, jdxwm, jdxlm, jtzz, yb, sjhm, ksh, bmh, rxqgzdw, dxwpdw, hshdsghzjlhcf, lqlbm, pyztm, sssfdb, lqnf, sfwgr, xjztm, sfzz, qrzhfqrz, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.xh}, #{entity.xm}, #{entity.wwxm}, #{entity.xmpy}, #{entity.cym}, #{entity.xbm}, #{entity.csrq}, #{entity.csdm}, #{entity.jg}, #{entity.mzm}, #{entity.gjdqm}, #{entity.sfzjlxm}, #{entity.sfzjh}, #{entity.hyzkm}, #{entity.gatqwm}, #{entity.zzmmm}, #{entity.jkzkm}, #{entity.xxm}, #{entity.sfzjyxq}, #{entity.sfdszn}, #{entity.hkxzm}, #{entity.hkszd}, #{entity.xslbm}, #{entity.dsgh}, #{entity.jdxwm}, #{entity.jdxlm}, #{entity.jtzz}, #{entity.yb}, #{entity.sjhm}, #{entity.ksh}, #{entity.bmh}, #{entity.rxqgzdw}, #{entity.dxwpdw}, #{entity.hshdsghzjlhcf}, #{entity.lqlbm}, #{entity.pyztm}, #{entity.sssfdb}, #{entity.lqnf}, #{entity.sfwgr}, #{entity.xjztm}, #{entity.sfzz}, #{entity.qrzhfqrz}, #{entity.tstamp})
        </foreach>
        ON CONFLICT(id) DO update set
xh = EXCLUDED.xh , xm = EXCLUDED.xm , wwxm = EXCLUDED.wwxm , xmpy = EXCLUDED.xmpy , cym = EXCLUDED.cym , xbm = EXCLUDED.xbm , csrq = EXCLUDED.csrq , csdm = EXCLUDED.csdm , jg = EXCLUDED.jg , mzm = EXCLUDED.mzm , gjdqm = EXCLUDED.gjdqm , sfzjlxm = EXCLUDED.sfzjlxm , sfzjh = EXCLUDED.sfzjh , hyzkm = EXCLUDED.hyzkm , gatqwm = EXCLUDED.gatqwm , zzmmm = EXCLUDED.zzmmm , jkzkm = EXCLUDED.jkzkm , xxm = EXCLUDED.xxm , sfzjyxq = EXCLUDED.sfzjyxq , sfdszn = EXCLUDED.sfdszn , hkxzm = EXCLUDED.hkxzm , hkszd = EXCLUDED.hkszd , xslbm = EXCLUDED.xslbm , dsgh = EXCLUDED.dsgh , jdxwm = EXCLUDED.jdxwm , jdxlm = EXCLUDED.jdxlm , jtzz = EXCLUDED.jtzz , yb = EXCLUDED.yb , sjhm = EXCLUDED.sjhm , ksh = EXCLUDED.ksh , bmh = EXCLUDED.bmh , rxqgzdw = EXCLUDED.rxqgzdw , dxwpdw = EXCLUDED.dxwpdw , hshdsghzjlhcf = EXCLUDED.hshdsghzjlhcf , lqlbm = EXCLUDED.lqlbm , pyztm = EXCLUDED.pyztm , sssfdb = EXCLUDED.sssfdb , lqnf = EXCLUDED.lqnf , sfwgr = EXCLUDED.sfwgr , xjztm = EXCLUDED.xjztm , sfzz = EXCLUDED.sfzz , qrzhfqrz = EXCLUDED.qrzhfqrz , tstamp = EXCLUDED.tstamp     </insert>

</mapper>

