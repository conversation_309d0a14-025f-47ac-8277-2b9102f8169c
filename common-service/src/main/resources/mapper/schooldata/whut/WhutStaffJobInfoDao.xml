<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutStaffJobInfoDao">
    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutStaffJobInfo" id="WhutStaffJobInfoMap">
        <result property="gh" column="gh" jdbcType="VARCHAR"/>
        <result property="zwmc" column="zwmc" jdbcType="VARCHAR"/>
        <result property="zwjb" column="zwjb" jdbcType="VARCHAR"/>
        <result property="rzfs" column="rzfs" jdbcType="VARCHAR"/>
        <result property="rzrq" column="rzrq" jdbcType="VARCHAR"/>
        <result property="khfs" column="khfs" jdbcType="VARCHAR"/>
        <result property="tstamp" column="tstamp" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="gh" useGeneratedKeys="true">
        insert into school_data.whut_staff_job_info(gh, zwmc, zwjb, rzfs, rzrq, khfs, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gh}, #{entity.zwmc}, #{entity.zwjb}, #{entity.rzfs}, #{entity.rzrq}, #{entity.khfs},
             #{entity.tstamp})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="gh" useGeneratedKeys="true">
        insert into school_data.whut_staff_job_info(gh, zwmc, zwjb, rzfs, rzrq, khfs, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gh}, #{entity.zwmc}, #{entity.zwjb}, #{entity.rzfs}, #{entity.rzrq}, #{entity.khfs},
             #{entity.tstamp})
        </foreach>
        ON CONFLICT(gh) DO update set zwmc   = EXCLUDED.zwmc,
                                      zwjb   = EXCLUDED.zwjb,
                                      rzfs   = EXCLUDED.rzfs,
                                      rzrq   = EXCLUDED.rzrq,
                                      khfs   = EXCLUDED.khfs,
                                      tstamp = EXCLUDED.tstamp
    </insert>
</mapper>

