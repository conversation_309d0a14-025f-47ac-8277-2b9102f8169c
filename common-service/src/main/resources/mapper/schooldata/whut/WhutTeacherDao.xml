<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutTeacherDao">
    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutTeacher" id="WhutTeacherMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="qywxUserId" column="qywx_user_id" jdbcType="VARCHAR"/>
        <result property="wybs" column="wybs" jdbcType="VARCHAR"/>
        <result property="szdwbm" column="szdwbm" jdbcType="VARCHAR"/>
        <result property="szdwmc" column="szdwmc" jdbcType="VARCHAR"/>
        <result property="szksdm" column="szksdm" jdbcType="VARCHAR"/>
        <result property="szksmc" column="szksmc" jdbcType="VARCHAR"/>
        <result property="gh" column="gh" jdbcType="VARCHAR"/>
        <result property="xm" column="xm" jdbcType="VARCHAR"/>
        <result property="wwxm" column="wwxm" jdbcType="VARCHAR"/>
        <result property="xmpy" column="xmpy" jdbcType="VARCHAR"/>
        <result property="cym" column="cym" jdbcType="VARCHAR"/>
        <result property="xbm" column="xbm" jdbcType="VARCHAR"/>
        <result property="xbmdmmc" column="xbmdmmc" jdbcType="VARCHAR"/>
        <result property="csrq" column="csrq" jdbcType="VARCHAR"/>
        <result property="csdm" column="csdm" jdbcType="VARCHAR"/>
        <result property="jgm" column="jgm" jdbcType="VARCHAR"/>
        <result property="mzm" column="mzm" jdbcType="VARCHAR"/>
        <result property="mzmdmmc" column="mzmdmmc" jdbcType="VARCHAR"/>
        <result property="gjdqm" column="gjdqm" jdbcType="VARCHAR"/>
        <result property="sfzjlxm" column="sfzjlxm" jdbcType="VARCHAR"/>
        <result property="sfzjlxmdmmc" column="sfzjlxmdmmc" jdbcType="VARCHAR"/>
        <result property="sfzjh" column="sfzjh" jdbcType="VARCHAR"/>
        <result property="sfzjyxq" column="sfzjyxq" jdbcType="VARCHAR"/>
        <result property="hyzkm" column="hyzkm" jdbcType="VARCHAR"/>
        <result property="gatqwm" column="gatqwm" jdbcType="VARCHAR"/>
        <result property="zzmmm" column="zzmmm" jdbcType="VARCHAR"/>
        <result property="jkzkm" column="jkzkm" jdbcType="VARCHAR"/>
        <result property="xyzjm" column="xyzjm" jdbcType="VARCHAR"/>
        <result property="xxm" column="xxm" jdbcType="VARCHAR"/>
        <result property="xqmc" column="xqmc" jdbcType="VARCHAR"/>
        <result property="cjgzny" column="cjgzny" jdbcType="VARCHAR"/>
        <result property="lxrq" column="lxrq" jdbcType="VARCHAR"/>
        <result property="qxrq" column="qxrq" jdbcType="VARCHAR"/>
        <result property="cjny" column="cjny" jdbcType="VARCHAR"/>
        <result property="ygxs" column="ygxs" jdbcType="VARCHAR"/>
        <result property="sfzb" column="sfzb" jdbcType="VARCHAR"/>
        <result property="bzlbm" column="bzlbm" jdbcType="VARCHAR"/>
        <result property="jzglbm" column="jzglbm" jdbcType="VARCHAR"/>
        <result property="jzglbmgb" column="jzglbmgb" jdbcType="VARCHAR"/>
        <result property="jzglym" column="jzglym" jdbcType="VARCHAR"/>
        <result property="jzglymdmmc" column="jzglymdmmc" jdbcType="VARCHAR"/>
        <result property="jzgdqztm" column="jzgdqztm" jdbcType="VARCHAR"/>
        <result property="jzgdqztmdmmc" column="jzgdqztmdmmc" jdbcType="VARCHAR"/>
        <result property="jzgdqztmgb" column="jzgdqztmgb" jdbcType="VARCHAR"/>
        <result property="jzgdqztyyxb" column="jzgdqztyyxb" jdbcType="VARCHAR"/>
        <result property="jzgdqztyyxbdmmc" column="jzgdqztyyxbdmmc" jdbcType="VARCHAR"/>
        <result property="zgxlm" column="zgxlm" jdbcType="VARCHAR"/>
        <result property="zgxlmdmmc" column="zgxlmdmmc" jdbcType="VARCHAR"/>
        <result property="zgxwm" column="zgxwm" jdbcType="VARCHAR"/>
        <result property="zgxwmdmmc" column="zgxwmdmmc" jdbcType="VARCHAR"/>
        <result property="xklbm" column="xklbm" jdbcType="VARCHAR"/>
        <result property="yjxkm" column="yjxkm" jdbcType="VARCHAR"/>
        <result property="ejxkm" column="ejxkm" jdbcType="VARCHAR"/>
        <result property="xcszy" column="xcszy" jdbcType="VARCHAR"/>
        <result property="txdz" column="txdz" jdbcType="VARCHAR"/>
        <result property="yddh" column="yddh" jdbcType="VARCHAR"/>
        <result property="dzyx" column="dzyx" jdbcType="VARCHAR"/>
        <result property="wldz" column="wldz" jdbcType="VARCHAR"/>
        <result property="jstxh" column="jstxh" jdbcType="VARCHAR"/>
        <result property="gzrq" column="gzrq" jdbcType="VARCHAR"/>
        <result property="bgdh" column="bgdh" jdbcType="VARCHAR"/>
        <result property="xykh" column="xykh" jdbcType="VARCHAR"/>
        <result property="sfsjt" column="sfsjt" jdbcType="VARCHAR"/>
        <result property="sjtgwlbm" column="sjtgwlbm" jdbcType="VARCHAR"/>
        <result property="sjtgwlbmdmmc" column="sjtgwlbmdmmc" jdbcType="VARCHAR"/>
        <result property="glzjys" column="glzjys" jdbcType="VARCHAR"/>
        <result property="zjglsm" column="zjglsm" jdbcType="VARCHAR"/>
        <result property="ydw" column="ydw" jdbcType="VARCHAR"/>
        <result property="hzzlm" column="hzzlm" jdbcType="VARCHAR"/>
        <result property="hzhm" column="hzhm" jdbcType="VARCHAR"/>
        <result property="tstamp" column="tstamp" jdbcType="VARCHAR"/>
        <result property="gwlbm" column="gwlbm" jdbcType="VARCHAR"/>
        <result property="gwlbmdmmc" column="gwlbmdmmc" jdbcType="VARCHAR"/>
        <result property="gwdjm" column="gwdjm" jdbcType="VARCHAR"/>
        <result property="gwdjmdmmc" column="gwdjmdmmc" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_teacher(qywx_user_id, wybs, szdwbm, szdwmc, szksdm, szksmc, gh, xm, wwxm, xmpy,
                                             cym, xbm, xbmdmmc, csrq, csdm, jgm, mzm, mzmdmmc, gjdqm, sfzjlxm,
                                             sfzjlxmdmmc, sfzjh, sfzjyxq, hyzkm, gatqwm, zzmmm, jkzkm, xyzjm, xxm, xqmc,
                                             cjgzny, lxrq, qxrq, cjny, ygxs, sfzb, bzlbm, jzglbm, jzglbmgb, jzglym,
                                             jzglymdmmc, jzgdqztm, jzgdqztmdmmc, jzgdqztmgb, jzgdqztyyxb,
                                             jzgdqztyyxbdmmc, zgxlm, zgxlmdmmc, zgxwm, zgxwmdmmc, xklbm, yjxkm, ejxkm,
                                             xcszy, txdz, yddh, dzyx, wldz, jstxh, gzrq, bgdh, xykh, sfsjt, sjtgwlbm,
                                             sjtgwlbmdmmc, glzjys, zjglsm, ydw, hzzlm, hzhm, tstamp, gwlbm, gwlbmdmmc,
                                             gwdjm, gwdjmdmmc)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.qywxUserId}, #{entity.wybs}, #{entity.szdwbm}, #{entity.szdwmc}, #{entity.szksdm},
             #{entity.szksmc}, #{entity.gh}, #{entity.xm}, #{entity.wwxm}, #{entity.xmpy}, #{entity.cym}, #{entity.xbm},
             #{entity.xbmdmmc}, #{entity.csrq}, #{entity.csdm}, #{entity.jgm}, #{entity.mzm}, #{entity.mzmdmmc},
             #{entity.gjdqm}, #{entity.sfzjlxm}, #{entity.sfzjlxmdmmc}, #{entity.sfzjh}, #{entity.sfzjyxq},
             #{entity.hyzkm}, #{entity.gatqwm}, #{entity.zzmmm}, #{entity.jkzkm}, #{entity.xyzjm}, #{entity.xxm},
             #{entity.xqmc}, #{entity.cjgzny}, #{entity.lxrq}, #{entity.qxrq}, #{entity.cjny}, #{entity.ygxs},
             #{entity.sfzb}, #{entity.bzlbm}, #{entity.jzglbm}, #{entity.jzglbmgb}, #{entity.jzglym},
             #{entity.jzglymdmmc}, #{entity.jzgdqztm}, #{entity.jzgdqztmdmmc}, #{entity.jzgdqztmgb},
             #{entity.jzgdqztyyxb}, #{entity.jzgdqztyyxbdmmc}, #{entity.zgxlm}, #{entity.zgxlmdmmc}, #{entity.zgxwm},
             #{entity.zgxwmdmmc}, #{entity.xklbm}, #{entity.yjxkm}, #{entity.ejxkm}, #{entity.xcszy}, #{entity.txdz},
             #{entity.yddh}, #{entity.dzyx}, #{entity.wldz}, #{entity.jstxh}, #{entity.gzrq}, #{entity.bgdh},
             #{entity.xykh}, #{entity.sfsjt}, #{entity.sjtgwlbm}, #{entity.sjtgwlbmdmmc}, #{entity.glzjys},
             #{entity.zjglsm}, #{entity.ydw}, #{entity.hzzlm}, #{entity.hzhm}, #{entity.tstamp}, #{entity.gwlbm},
             #{entity.gwlbmdmmc}, #{entity.gwdjm}, #{entity.gwdjmdmmc})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_teacher(qywx_user_id, wybs, szdwbm, szdwmc, szksdm, szksmc, gh, xm, wwxm, xmpy,
                                             cym, xbm, xbmdmmc, csrq, csdm, jgm, mzm, mzmdmmc, gjdqm, sfzjlxm,
                                             sfzjlxmdmmc, sfzjh, sfzjyxq, hyzkm, gatqwm, zzmmm, jkzkm, xyzjm, xxm, xqmc,
                                             cjgzny, lxrq, qxrq, cjny, ygxs, sfzb, bzlbm, jzglbm, jzglbmgb, jzglym,
                                             jzglymdmmc, jzgdqztm, jzgdqztmdmmc, jzgdqztmgb, jzgdqztyyxb,
                                             jzgdqztyyxbdmmc, zgxlm, zgxlmdmmc, zgxwm, zgxwmdmmc, xklbm, yjxkm, ejxkm,
                                             xcszy, txdz, yddh, dzyx, wldz, jstxh, gzrq, bgdh, xykh, sfsjt, sjtgwlbm,
                                             sjtgwlbmdmmc, glzjys, zjglsm, ydw, hzzlm, hzhm, tstamp, gwlbm, gwlbmdmmc,
                                             gwdjm, gwdjmdmmc)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.qywxUserId}, #{entity.wybs}, #{entity.szdwbm}, #{entity.szdwmc}, #{entity.szksdm},
             #{entity.szksmc}, #{entity.gh}, #{entity.xm}, #{entity.wwxm}, #{entity.xmpy}, #{entity.cym}, #{entity.xbm},
             #{entity.xbmdmmc}, #{entity.csrq}, #{entity.csdm}, #{entity.jgm}, #{entity.mzm}, #{entity.mzmdmmc},
             #{entity.gjdqm}, #{entity.sfzjlxm}, #{entity.sfzjlxmdmmc}, #{entity.sfzjh}, #{entity.sfzjyxq},
             #{entity.hyzkm}, #{entity.gatqwm}, #{entity.zzmmm}, #{entity.jkzkm}, #{entity.xyzjm}, #{entity.xxm},
             #{entity.xqmc}, #{entity.cjgzny}, #{entity.lxrq}, #{entity.qxrq}, #{entity.cjny}, #{entity.ygxs},
             #{entity.sfzb}, #{entity.bzlbm}, #{entity.jzglbm}, #{entity.jzglbmgb}, #{entity.jzglym},
             #{entity.jzglymdmmc}, #{entity.jzgdqztm}, #{entity.jzgdqztmdmmc}, #{entity.jzgdqztmgb},
             #{entity.jzgdqztyyxb}, #{entity.jzgdqztyyxbdmmc}, #{entity.zgxlm}, #{entity.zgxlmdmmc}, #{entity.zgxwm},
             #{entity.zgxwmdmmc}, #{entity.xklbm}, #{entity.yjxkm}, #{entity.ejxkm}, #{entity.xcszy}, #{entity.txdz},
             #{entity.yddh}, #{entity.dzyx}, #{entity.wldz}, #{entity.jstxh}, #{entity.gzrq}, #{entity.bgdh},
             #{entity.xykh}, #{entity.sfsjt}, #{entity.sjtgwlbm}, #{entity.sjtgwlbmdmmc}, #{entity.glzjys},
             #{entity.zjglsm}, #{entity.ydw}, #{entity.hzzlm}, #{entity.hzhm}, #{entity.tstamp}, #{entity.gwlbm},
             #{entity.gwlbmdmmc}, #{entity.gwdjm}, #{entity.gwdjmdmmc})
        </foreach>
        ON CONFLICT(id) DO update set qywx_user_id    = EXCLUDED.qywx_user_id,
                                      wybs            = EXCLUDED.wybs,
                                      szdwbm          = EXCLUDED.szdwbm,
                                      szdwmc          = EXCLUDED.szdwmc,
                                      szksdm          = EXCLUDED.szksdm,
                                      szksmc          = EXCLUDED.szksmc,
                                      gh              = EXCLUDED.gh,
                                      xm              = EXCLUDED.xm,
                                      wwxm            = EXCLUDED.wwxm,
                                      xmpy            = EXCLUDED.xmpy,
                                      cym             = EXCLUDED.cym,
                                      xbm             = EXCLUDED.xbm,
                                      xbmdmmc         = EXCLUDED.xbmdmmc,
                                      csrq            = EXCLUDED.csrq,
                                      csdm            = EXCLUDED.csdm,
                                      jgm             = EXCLUDED.jgm,
                                      mzm             = EXCLUDED.mzm,
                                      mzmdmmc         = EXCLUDED.mzmdmmc,
                                      gjdqm           = EXCLUDED.gjdqm,
                                      sfzjlxm         = EXCLUDED.sfzjlxm,
                                      sfzjlxmdmmc     = EXCLUDED.sfzjlxmdmmc,
                                      sfzjh           = EXCLUDED.sfzjh,
                                      sfzjyxq         = EXCLUDED.sfzjyxq,
                                      hyzkm           = EXCLUDED.hyzkm,
                                      gatqwm          = EXCLUDED.gatqwm,
                                      zzmmm           = EXCLUDED.zzmmm,
                                      jkzkm           = EXCLUDED.jkzkm,
                                      xyzjm           = EXCLUDED.xyzjm,
                                      xxm             = EXCLUDED.xxm,
                                      xqmc            = EXCLUDED.xqmc,
                                      cjgzny          = EXCLUDED.cjgzny,
                                      lxrq            = EXCLUDED.lxrq,
                                      qxrq            = EXCLUDED.qxrq,
                                      cjny            = EXCLUDED.cjny,
                                      ygxs            = EXCLUDED.ygxs,
                                      sfzb            = EXCLUDED.sfzb,
                                      bzlbm           = EXCLUDED.bzlbm,
                                      jzglbm          = EXCLUDED.jzglbm,
                                      jzglbmgb        = EXCLUDED.jzglbmgb,
                                      jzglym          = EXCLUDED.jzglym,
                                      jzglymdmmc      = EXCLUDED.jzglymdmmc,
                                      jzgdqztm        = EXCLUDED.jzgdqztm,
                                      jzgdqztmdmmc    = EXCLUDED.jzgdqztmdmmc,
                                      jzgdqztmgb      = EXCLUDED.jzgdqztmgb,
                                      jzgdqztyyxb     = EXCLUDED.jzgdqztyyxb,
                                      jzgdqztyyxbdmmc = EXCLUDED.jzgdqztyyxbdmmc,
                                      zgxlm           = EXCLUDED.zgxlm,
                                      zgxlmdmmc       = EXCLUDED.zgxlmdmmc,
                                      zgxwm           = EXCLUDED.zgxwm,
                                      zgxwmdmmc       = EXCLUDED.zgxwmdmmc,
                                      xklbm           = EXCLUDED.xklbm,
                                      yjxkm           = EXCLUDED.yjxkm,
                                      ejxkm           = EXCLUDED.ejxkm,
                                      xcszy           = EXCLUDED.xcszy,
                                      txdz            = EXCLUDED.txdz,
                                      yddh            = EXCLUDED.yddh,
                                      dzyx            = EXCLUDED.dzyx,
                                      wldz            = EXCLUDED.wldz,
                                      jstxh           = EXCLUDED.jstxh,
                                      gzrq            = EXCLUDED.gzrq,
                                      bgdh            = EXCLUDED.bgdh,
                                      xykh            = EXCLUDED.xykh,
                                      sfsjt           = EXCLUDED.sfsjt,
                                      sjtgwlbm        = EXCLUDED.sjtgwlbm,
                                      sjtgwlbmdmmc    = EXCLUDED.sjtgwlbmdmmc,
                                      glzjys          = EXCLUDED.glzjys,
                                      zjglsm          = EXCLUDED.zjglsm,
                                      ydw             = EXCLUDED.ydw,
                                      hzzlm           = EXCLUDED.hzzlm,
                                      hzhm            = EXCLUDED.hzhm,
                                      tstamp          = EXCLUDED.tstamp,
                                      gwlbm           = EXCLUDED.gwlbm,
                                      gwlbmdmmc       = EXCLUDED.gwlbmdmmc,
                                      gwdjm           = EXCLUDED.gwdjm,
                                      gwdjmdmmc       = EXCLUDED.gwdjmdmmc
    </insert>

    <select id="listMemberByDwbm" resultType="com.lanshan.base.commonservice.group.vo.MsgDeptGroupChatDetailVO">
        select gh                                              AS userId,
               xm                                              AS name,
               qywx_user_id is not null and qywx_user_id != '' AS hasJoinQywx
        from school_data.whut_teacher
        where szdwbm = #{deptCode}
        union all
        select gh                                              as userId,
               xm                                              AS name,
               qywx_user_id is not null and qywx_user_id != '' AS hasJoinQywx
        from school_data.whut_collective_employee
        where dwbm = #{deptCode}
    </select>

    <select id="getByGh" resultMap="WhutTeacherMap">
        select *
        from school_data.whut_teacher
        where gh = #{gh}
    </select>
</mapper>

