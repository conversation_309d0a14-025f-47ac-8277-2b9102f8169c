<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutInterEduCollegeClassDao">

    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutInterEduCollegeClass" id="WhutInterEduCollegeClassMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="nj" column="nj" jdbcType="VARCHAR"/>
        <result property="zymc" column="zymc" jdbcType="VARCHAR"/>
        <result property="bjmc" column="bjmc" jdbcType="VARCHAR"/>
        <result property="chatid" column="chatid" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_inter_edu_college_class(nj, zymc, bjmc, chatid, status)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.nj} , #{entity.zymc} , #{entity.bjmc} , #{entity.chatid} , #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_inter_edu_college_class(nj, zymc, bjmc, chatid, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.nj}, #{entity.zymc}, #{entity.bjmc}, #{entity.chatid}, #{entity.status})
        </foreach>
        ON CONFLICT(id) DO update set
nj = EXCLUDED.nj , zymc = EXCLUDED.zymc , bjmc = EXCLUDED.bjmc , chatid = EXCLUDED.chatid , status = EXCLUDED.status     </insert>

</mapper>

