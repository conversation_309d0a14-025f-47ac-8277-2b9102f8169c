<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateStatusDao">

    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateStatus" id="WhutUndergraduateStatusMap">
        <result property="xh" column="xh"/>
        <result property="xm" column="xm"/>
        <result property="xqbm" column="xqbm"/>
        <result property="yxbm" column="yxbm"/>
        <result property="zybm" column="zybm"/>
        <result property="bjbm" column="bjbm"/>
        <result property="pycc" column="pycc"/>
        <result property="nj" column="nj"/>
        <result property="xxnx" column="xxnx"/>
        <result property="xz" column="xz"/>
        <result property="xkml" column="xkml"/>
        <result property="rxny" column="rxny"/>
        <result property="rxzf" column="rxzf"/>
        <result property="rxfs" column="rxfs"/>
        <result property="xslb" column="xslb"/>
        <result property="xslbdmmc" column="xslbdmmc"/>
        <result property="lydq" column="lydq"/>
        <result property="byzx" column="byzx"/>
        <result property="wtpydw" column="wtpydw"/>
        <result property="yjbyrq" column="yjbyrq"/>
        <result property="sfzx" column="sfzx"/>
        <result property="sfzj" column="sfzj"/>
        <result property="tstamp" column="tstamp"/>
    </resultMap>

    <select id="listCountByClassCode" resultType="com.lanshan.base.commonservice.group.dto.MsgClassGroupChatCountDTO">
        SELECT bjbm AS classCode, COUNT(1) AS classUserCount
        FROM whut_undergraduate_status
        WHERE sfzx = '1' AND sfzj = '1' AND bjbm IN
        <foreach collection="classCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY bjbm
    </select>
</mapper>

