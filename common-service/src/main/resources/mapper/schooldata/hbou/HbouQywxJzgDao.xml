<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxJzgDao">
    <resultMap type="com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxJzg" id="HbouQywxJzgMap">
        <result property="gh" column="gh" jdbcType="VARCHAR"/>
        <result property="sjh" column="sjh" jdbcType="VARCHAR"/>
        <result property="dwbm" column="dwbm" jdbcType="VARCHAR"/>
        <result property="dwmc" column="dwmc" jdbcType="VARCHAR"/>
        <result property="zt" column="zt" jdbcType="VARCHAR"/>
        <result property="xb" column="xb" jdbcType="VARCHAR"/>
        <result property="xm" column="xm" jdbcType="VARCHAR"/>
        <result property="dzzwjb" column="dzzwjb" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.hbou_qywx_jzg(gh, sjh, dwbm, dwmc, zt, xb, xm, dzzwjb)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gh}, #{entity.sjh}, #{entity.dwbm}, #{entity.dwmc}, #{entity.zt}, #{entity.xb}, #{entity.xm}, #{entity.dzzwjb})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into school_data.hbou_qywx_jzg(gh, sjh, dwbm, dwmc, zt, xb, xm, dzzwjb)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gh}, #{entity.sjh}, #{entity.dwbm}, #{entity.dwmc}, #{entity.zt}, #{entity.xb}, #{entity.xm}, #{entity.dzzwjb})
        </foreach>
        ON CONFLICT(gh) DO update set sjh = EXCLUDED.sjh,
        dwbm = EXCLUDED.dwbm,
        dwmc = EXCLUDED.dwmc,
        zt = EXCLUDED.zt,
        xb = EXCLUDED.xb,
        xm = EXCLUDED.xm,
        dzzwjb = EXCLUDED.dzzwjb
    </insert>
</mapper>

