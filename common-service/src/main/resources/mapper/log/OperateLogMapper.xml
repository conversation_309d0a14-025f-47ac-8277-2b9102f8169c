<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.log.mapper.OperateLogMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.log.entity.OperateLogEntity">
        <!--@Table operate_log-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="threadId" column="thread_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="operation" column="operation" jdbcType="VARCHAR"/>
        <result property="time" column="time" jdbcType="INTEGER"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="params" column="params" jdbcType="VARCHAR"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
id,
  thread_id,
user_id,
operation,
time,
method,
params,
ip,
create_time

    </sql>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into operate_log(user_id, thread_id, operation, time, method, params, ip, create_time)
        values (#{userId},#{threadId},  #{operation}, #{time}, #{method}, #{params}, #{ip}, #{createTime})
    </insert>




</mapper>

