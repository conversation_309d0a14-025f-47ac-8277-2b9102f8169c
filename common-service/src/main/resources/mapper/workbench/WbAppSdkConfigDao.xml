<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.workbench.dao.WbAppSdkConfigDao">
    <resultMap type="com.lanshan.base.commonservice.workbench.entity.WbAppSdkConfig" id="WbAppSdkConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="isIntegrated" column="is_integrated" jdbcType="BOOLEAN"/>
        <result property="isOpen" column="is_open" jdbcType="BOOLEAN"/>
        <result property="integratedTime" column="integrated_time" jdbcType="TIMESTAMP"/>
        <result property="openTime" column="open_time" jdbcType="TIMESTAMP"/>
        <result property="config" column="config"
                typeHandler="com.lanshan.base.commonservice.typehandler.JsonbTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_id, is_integrated, is_open, integrated_time, open_time, config
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_sdk_config(app_id, is_integrated, is_open, integrated_time, open_time, config)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId} , #{entity.isIntegrated} , #{entity.isOpen} , #{entity.integratedTime} , #{entity.openTime}
            , #{entity.config})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_sdk_config(app_id, is_integrated, is_open, integrated_time, open_time, config)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.isIntegrated}, #{entity.isOpen}, #{entity.integratedTime}, #{entity.openTime},
            #{entity.config})
        </foreach>
        ON CONFLICT(id) DO update set
        app_id = EXCLUDED.app_id , is_integrated = EXCLUDED.is_integrated , is_open = EXCLUDED.is_open , integrated_time
        = EXCLUDED.integrated_time , open_time = EXCLUDED.open_time , config = EXCLUDED.config
    </insert>

    <select id="selectByAppId" resultMap="WbAppSdkConfigMap">
        select
        <include refid="Base_Column_List"/>
        from workbench.wb_app_sdk_config
        where app_id = #{appId}
        limit 1
    </select>
</mapper>

