<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.workbench.dao.WbAppTagRelDao">
    <resultMap type="com.lanshan.base.commonservice.workbench.entity.WbAppTagRel" id="WbAppTagRelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="tagId" column="tag_id" jdbcType="INTEGER"/>
        <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
    </resultMap>


    <delete id="deleteByAppId" parameterType="Long">
        delete
        from workbench.wb_app_tag_rel
        where app_id = #{appId}
    </delete>

    <delete id="deleteByTagId" parameterType="Long">
        delete
        from workbench.wb_app_tag_rel
        where tag_id = #{tagId}
    </delete>

    <select id="selectAllAppTagName" resultMap="WbAppTagRelMap">
        select rel.app_id, rel.tag_id, tag.tag_name
        from workbench.wb_app_tag_rel rel
                 left join workbench.wb_tag tag on rel.tag_id = tag.id
        where tag.is_deleted = false
    </select>

    <select id="selectAppTagName" resultMap="WbAppTagRelMap" parameterType="java.util.List">
        select rel.app_id, rel.tag_id, tag.tag_name
        from workbench.wb_app_tag_rel rel
        left join workbench.wb_tag tag on rel.tag_id = tag.id
        where rel.app_id in
        <foreach collection="appIds" item="appId" open="(" close=")" separator=",">
            #{appId}
        </foreach>
        and tag.is_deleted = false
    </select>

    <select id="selectByTagName" resultMap="WbAppTagRelMap" parameterType="String">
        select rel.app_id, rel.tag_id, tag.tag_name
        from workbench.wb_app_tag_rel rel
                 left join workbench.wb_tag tag on rel.tag_id = tag.id
        where tag.tag_name like concat('%', #{tagName}::text, '%')
          and tag.is_deleted = false
    </select>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_tag_rel(app_id, tag_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId} , #{entity.tagId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_tag_rel(app_id, tag_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.tagId})
        </foreach>
        on duplicate key update
        app_id = values(app_id) , tag_id = values(tag_id)
    </insert>
</mapper>

