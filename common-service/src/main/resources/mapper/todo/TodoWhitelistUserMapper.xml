<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.todo.mapper.TodoWhitelistUserMapper">

    <resultMap type="com.lanshan.base.commonservice.todo.entity.TodoWhitelistUser" id="TodoWhitelistUserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="userDepartment" column="user_department" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into todo.todo_whitelist_user(app_id, user_id, user_name, user_department)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.appId} , #{entity.userId} , #{entity.userName} , #{entity.userDepartment})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into todo.todo_whitelist_user(app_id, user_id, user_name, user_department)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.userId}, #{entity.userName}, #{entity.userDepartment})
        </foreach>
        ON CONFLICT(id) DO update set
app_id = EXCLUDED.app_id , user_id = EXCLUDED.user_id , user_name = EXCLUDED.user_name , user_department = EXCLUDED.user_department     </insert>

    <select id="getWhiteUsers" resultMap="TodoWhitelistUserMap">
        select  u.* from todo.todo_whitelist_user u left join todo.todo_whitelist_config c on u.app_id = c.app_id where c.test_status = 1 and c.status = 1
    </select>

</mapper>

