<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.todo.mapper.TodoOperateLogMapper">

    <resultMap type="com.lanshan.base.commonservice.todo.entity.TodoOperateLog" id="TodoOperateLogMap">
        <result property="id" column="id"/>
        <result property="todoDefId" column="todo_def_id"/>
        <result property="apiType" column="api_type"/>
        <result property="requestParam" column="request_param"/>
        <result property="responseStatus" column="response_status"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

</mapper>

