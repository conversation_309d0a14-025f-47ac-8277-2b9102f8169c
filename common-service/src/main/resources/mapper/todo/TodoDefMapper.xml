<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.todo.mapper.TodoDefMapper">

    <resultMap type="com.lanshan.base.commonservice.todo.entity.TodoDef" id="TodoDefMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="completeMode" column="complete_mode" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="linkUrl" column="link_url"/>
        <result property="linkUrlPc" column="link_url_pc"/>
        <result property="isRepeat" column="is_repeat"/>
        <result property="repeatUntil" column="repeat_until"/>
        <result property="repeatType" column="repeat_type"/>
        <result property="repeatInterval" column="repeat_interval"/>
        <result property="repeatDayOfWeek" column="repeat_day_of_week" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="repeatDayOfMonth" column="repeat_day_of_month" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="isRemind" column="is_remind"/>
        <result property="remindTimeDiffs" column="remind_time_diffs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="calId" column="cal_id"/>
        <result property="userCount" column="user_count"/>
        <result property="creator" column="creator"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorType" column="creator_type"/>
        <result property="agentId" column="agentId"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="isFlow" column="is_flow"/>
        <result property="flowId" column="flow_id"/>
        <result property="nodeId" column="node_id"/>

    </resultMap>

    <resultMap type="com.lanshan.base.commonservice.todo.vo.TodoVo" id="TodoVoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="completeMode" column="complete_mode" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="linkUrl" column="link_url"/>
        <result property="linkUrlPc" column="link_url_pc"/>
        <result property="remindTimeDiffs" column="remind_time_diffs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="creatorName" column="creator_name"/>
        <result property="isRepeat" column="is_repeat"/>
        <result property="repeatType" column="repeat_type"/>
        <result property="repeatInterval" column="repeat_interval"/>
        <result property="repeatUntil" column="repeat_until"/>
        <result property="repeatDayOfWeek" column="repeat_day_of_week" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="repeatDayOfMonth" column="repeat_day_of_month" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="completeTime" column="complete_time"/>
        <result property="isAutoComplete" column="is_auto_complete"/>
        <result property="isFlow" column="is_flow"/>
        <result property="serialNo" column="serial_no"/>
        <result property="submitterId" column="submitter_id"/>
        <result property="submitterName" column="submitter_name"/>
        <result property="submitInfo" column="submit_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="flowNodeList" column="flow_node_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="isDelay" column="is_delay"/>
        <result property="creatorType" column="creator_type"/>
        <result property="type" column="type"/>
    </resultMap>
    <resultMap type="com.lanshan.base.commonservice.todo.vo.TodoFlowVo" id="TodoFlowVoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creator" column="creator"/>
        <result property="completeDate" column="completeDate"/>
        <result property="createDate" column="create_date"/>
        <result property="serialNo" column="serial_no"/>
        <result property="submitterId" column="submitter_id"/>
        <result property="submitterName" column="submitter_name"/>
        <result property="submitInfo" column="submit_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="flowNodeList" column="flow_node_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="type" column="type"/>
    </resultMap>


    <sql id="todoSql">
        SELECT tur.id, td.id AS todoDefId, td.name, td.description, td.complete_mode, tdtl.id AS todoDetailId, tdtl.start_time, tdtl.end_time, td.link_url, td.link_url_pc, td.remind_time_diffs, td.creator, td.creator_name, td.creator_type, td.create_date, tur.complete_time, tur.is_auto_complete,
               td.is_repeat, td.repeat_type, td.repeat_interval, td.repeat_until, td.repeat_day_of_week, td.repeat_day_of_month, td.is_flow, tfi.serial_no, tfi.submitter_id, tfi.submitter_name, tfi.submit_info, tfi.flow_node_list, tdtl.is_delay, tur.userid, tur.name AS userName, tur.status, tfi.type
    </sql>

    <select id="pageTodoIncomplete" resultMap="TodoVoMap">
        SELECT *
        FROM (
        <include refid="todoSql"/>
        ,
        RANK() OVER (PARTITION BY tdtl.todo_def_id ORDER BY tdtl.start_time) AS rank
        FROM todo_def td
        INNER JOIN todo_detail tdtl ON td.id = tdtl.todo_def_id
        INNER JOIN todo_user_relation tur ON tdtl.id = tur.todo_detail_id
        LEFT JOIN todo_flow_info tfi ON td.flow_id = tfi.id
        WHERE tur.status = 0
        AND tur.userid = #{param.userid}
        AND tdtl.end_time::DATE &gt;= CURRENT_DATE
        <if test="param.startTime != null">
            <choose>
                <when test="param.endTime != null">
                    AND tdtl.start_time &gt; #{param.startTime}
                    AND tdtl.start_time &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
                </when>
                <otherwise>
                    AND tdtl.start_time &lt; (DATE(#{param.startTime}) + INTERVAL '1D')
                </otherwise>
            </choose>
        </if>
        <if test="param.name != null and param.name != ''">
            AND td.name like concat('%', #{param.name}::text, '%')
        </if>
        <if test="param.appId != null">
            AND td.creator = #{param.appId} AND td.creator_type = 1
        </if>
        <if test="param.blackListAppId != null and param.blackListAppId.size() > 0">
            AND td.creator NOT IN
            <foreach item="item" index="index" collection="param.blackListAppId"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.serialNo != null and param.serialNo != ''">
            AND tfi.serial_no like concat('%', #{param.serialNo}::text, '%')
        </if>
        <if test="param.isDelay != null">
            AND tdtl.is_delay = #{param.isDelay}
        </if>
        <if test="param.type != null and param.type != ''">
            AND tfi.type = #{param.type}
        </if>
        <if test="param.nameOrSerialNo != null and param.nameOrSerialNo != ''">
            AND (tfi.name like concat('%', #{param.nameOrSerialNo}::text, '%')
            OR tfi.serial_no like concat('%', #{param.nameOrSerialNo}::text, '%'))
        </if>
        ) t
        WHERE t.rank = 1
        <choose>
            <when test="param.orderType != null and param.orderType == 1">
                ORDER BY t.start_time DESC, t.create_date DESC
            </when>
            <otherwise>
                ORDER BY t.start_time, t.create_date
            </otherwise>
        </choose>
    </select>

    <select id="pageTodoComplete" resultMap="TodoVoMap">
        SELECT *
        FROM (
        <include refid="todoSql"/>
        ,
        RANK() OVER (PARTITION BY tdtl.todo_def_id ORDER BY tur.complete_time DESC) AS rank
        FROM todo_def td
        INNER JOIN todo_detail tdtl ON td.id = tdtl.todo_def_id
        INNER JOIN todo_user_relation tur ON tdtl.id = tur.todo_detail_id
        LEFT JOIN todo_flow_info tfi ON td.flow_id = tfi.id
        WHERE tur.userid = #{param.userid}
        AND tur.status = 1
        <if test="param.startTime != null and param.endTime != null">
            AND tur.complete_time &gt;= #{param.startTime} AND
            tur.complete_time &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
        </if>
        <if test="param.name != null and param.name != ''">
            AND td.name like concat('%', #{param.name}::text, '%')
        </if>
        <if test="param.appId != null">
            AND td.creator = #{param.appId} AND td.creator_type = 1
        </if>
        <if test="param.blackListAppId != null and param.blackListAppId.size() > 0">
            AND td.creator NOT IN
            <foreach item="item" index="index" collection="param.blackListAppId"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.serialNo != null and param.serialNo != ''">
            AND tfi.serial_no like concat('%', #{param.serialNo}::text, '%')
        </if>
        <if test="param.type != null and param.type != ''">
            AND tfi.type = #{param.type}
        </if>
        <if test="param.nameOrSerialNo != null and param.nameOrSerialNo != ''">
            AND (tfi.name like concat('%', #{param.nameOrSerialNo}::text, '%')
            OR tfi.serial_no like concat('%', #{param.nameOrSerialNo}::text, '%'))
        </if>
        ) t
        WHERE t.rank = 1
        <choose>
            <when test="param.orderType != null and param.orderType == 0">
                ORDER BY t.complete_time, t.create_date
            </when>
            <otherwise>
                ORDER BY t.complete_time DESC, t.create_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="listOverdueTodo" resultMap="TodoVoMap">
        SELECT *
        FROM (
        <include refid="todoSql"/>
        ,
        RANK() OVER (PARTITION BY tdtl.todo_def_id ORDER BY tdtl.start_time) AS rank
        FROM todo_def td
        INNER JOIN todo_detail tdtl ON td.id = tdtl.todo_def_id
        INNER JOIN todo_user_relation tur ON tdtl.id = tur.todo_detail_id
        LEFT JOIN todo_flow_info tfi ON td.flow_id = tfi.id
        WHERE tur.status = 0
        AND tdtl.end_time::DATE &lt; CURRENT_DATE
        AND tur.userid = #{param.userid}
        <if test="param.name != null and param.name != ''">
            AND td.name like concat('%', #{param.name}::text, '%')
        </if>
        <if test="param.appId != null">
            AND td.creator = #{param.appId} AND td.creator_type = 1
        </if>
        <if test="param.serialNo != null and param.serialNo != ''">
            AND tfi.serial_no like concat('%', #{param.serialNo}::text, '%')
        </if>
        <if test="param.isDelay != null">
            AND tdtl.is_delay = #{param.isDelay}
        </if>
        <if test="param.type != null and param.type != ''">
            AND tfi.type = #{param.type}
        </if>
        <if test="param.nameOrSerialNo != null and param.nameOrSerialNo != ''">
            AND (tfi.name like concat('%', #{param.nameOrSerialNo}::text, '%')
            OR tfi.serial_no like concat('%', #{param.nameOrSerialNo}::text, '%'))
        </if>
        ) t
        WHERE t.rank = 1
        <choose>
            <when test="param.orderType != null and param.orderType == 1">
                ORDER BY t.start_time DESC, t.create_date DESC
            </when>
            <otherwise>
                ORDER BY t.start_time, t.create_date
            </otherwise>
        </choose>
    </select>

    <select id="existTodoByMonth" resultType="java.lang.Integer">
        SELECT DISTINCT EXTRACT(DAY FROM tdtl.start_time) AS day
        FROM todo_detail tdtl,
        todo_user_relation tur,
        todo_def def
        WHERE tdtl.id = tur.todo_detail_id
            AND tur.todo_def_id = def.id
            AND tur.userid = #{param.userid}
            AND tur.status = 0
            AND tdtl.start_time &gt;= DATE_TRUNC('MONTH', #{param.month}::DATE)
            AND tdtl.start_time &lt; (DATE_TRUNC('MONTH', #{param.month}::DATE) + INTERVAL '1 MONTH')
            <if test="param.blackAppListStr != null and param.blackAppListStr.size() > 0">
                AND def.creator NOT IN
                <foreach item="item" index="index" collection="param.blackAppListStr"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        UNION
        SELECT DISTINCT EXTRACT(DAY FROM tur.complete_time) AS day
        FROM todo_detail tdtl,
        todo_user_relation tur,
        todo_def def
        WHERE tdtl.id = tur.todo_detail_id
        AND tur.todo_def_id = def.id
        AND tur.userid = #{param.userid}
        AND tur.status = 1
        AND tur.complete_time &gt;= DATE_TRUNC('MONTH', #{param.month}::DATE)
        AND tur.complete_time &lt; (DATE_TRUNC('MONTH', #{param.month}::DATE) + INTERVAL '1 MONTH')
        <if test="param.blackAppListStr != null and param.blackAppListStr.size() > 0">
            AND def.creator NOT IN
            <foreach item="item" index="index" collection="param.blackAppListStr"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listAutoCompleteList" resultType="com.lanshan.base.commonservice.todo.dto.TodoAutoCompleteDto">
        SELECT tur.id, tdtl.end_time AS endTime
        FROM todo_def td,
             todo_detail tdtl,
             todo_user_relation tur
        WHERE td.id = tdtl.todo_def_id
          AND tdtl.id = tur.todo_detail_id
          AND td.complete_mode @> '1'
          AND tdtl.end_time &lt;= NOW()
          AND tdtl.end_time &gt;= #{startDate}
          AND tur.status = 0
    </select>

    <select id="listCompleteCalendar" resultType="com.lanshan.base.commonservice.todo.entity.TodoDef">
        <!-- 重复待办：按重复结束时间比对 -->
        SELECT cal_id, agent_id
        FROM todo_def
        WHERE is_repeat = 1
        AND is_remind = 1
        AND cal_id IS NOT NULL
        AND repeat_until &gt;= DATE(#{date})
        AND repeat_until &lt; (DATE(#{date}) + INTERVAL '1D')
    </select>

    <select id="pageCopyToMeTodo" resultMap="TodoFlowVoMap">
        SELECT tfi.name,
        tfi.description,
        tfi.flow_status,
        tfi.serial_no,
        tfi.submitter_id,
        tfi.submitter_name,
        tfi.create_date,
        tfcu.create_date AS completeDate,
        tfi.app_id       AS creator,
        tfi.app_name     AS creator_name,
        tfi.submit_info,
        tfi.flow_node_list,
        tfi.type
        FROM todo_flow_copy_user tfcu,
        todo_flow_info tfi
        WHERE tfcu.flow_id = tfi.id
        AND tfcu.userid = #{param.userid}
        <if test="param.startTime != null and param.endTime != null">
            AND tfcu.create_date &gt;= #{param.startTime}
            AND tfcu.create_date &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
        </if>
        <if test="param.name != null and param.name != ''">
            AND tfi.name like concat('%', #{param.name}::text, '%')
        </if>
        <if test="param.appId != null">
            AND tfi.app_id = #{param.appId}
        </if>
        <if test="param.blackListAppId != null and param.blackListAppId.size() > 0">
            AND tfi.app_id NOT IN
            <foreach item="item" index="index" collection="param.blackListAppId"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.serialNo != null">
            AND tfi.serial_no like concat('%', #{param.serialNo}::text, '%')
        </if>
        <if test="param.flowStatus != null">
            AND tfi.flow_status = #{param.flowStatus}
        </if>
        <if test="param.type != null and param.type != ''">
            AND tfi.type = #{param.type}
        </if>
        <if test="param.nameOrSerialNo != null and param.nameOrSerialNo != ''">
            AND (tfi.name like concat('%', #{param.nameOrSerialNo}::text, '%')
            OR tfi.serial_no like concat('%', #{param.nameOrSerialNo}::text, '%'))
        </if>
        <choose>
            <when test="param.orderType != null and param.orderType == 0">
                ORDER BY tfcu.create_date
            </when>
            <otherwise>
                ORDER BY tfcu.create_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectAppIdList" resultType="java.lang.Long">
        SELECT DISTINCT td.creator
        FROM todo.todo_def td,
        todo.todo_user_relation tur
        WHERE td.id = tur.todo_def_id
        AND td.creator_type = 1
        AND tur.status = 0
        <if test="userid != null and userid != ''">
            AND tur.userid = #{userid}
        </if>
        <if test="blackListStr != null and blackListStr.size() > 0">
            AND td.creator NOT IN
            <foreach item="item" index="index" collection="blackListStr"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="listTodoUserByFlowNode" resultType="java.lang.String">
        SELECT tur.userid
        FROM todo_def td,
             todo_user_relation tur,
             todo_flow_info tfi
        WHERE td.id = tur.todo_def_id
          AND td.flow_id = tfi.id
          AND tfi.serial_no = #{param.serialNo}
          AND tfi.app_id = #{param.appId}
          AND td.node_id = #{param.nodeId}
    </select>

    <select id="pageISubmitTodo" resultMap="TodoFlowVoMap">
        SELECT tfi.name,
        tfi.description,
        tfi.flow_status,
        tfi.serial_no,
        tfi.submitter_id,
        tfi.submitter_name,
        tfi.create_date,
        tfi.app_id   AS creator,
        tfi.app_name AS creator_name,
        tfi.submit_info,
        tfi.flow_node_list,
        tfi.type
        FROM todo_flow_info tfi
        WHERE tfi.submitter_id = #{param.userid}
        <if test="param.startTime != null and param.endTime != null">
            AND tfi.create_date &gt;= #{param.startTime}
            AND tfi.create_date &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
        </if>
        <if test="param.name != null and param.name != ''">
            AND tfi.name like concat('%', #{param.name}::text, '%')
        </if>
        <if test="param.appId != null">
            AND tfi.app_id = #{param.appId}
        </if>
        <if test="param.blackListAppId != null and param.blackListAppId.size() > 0">
            AND tfi.app_id NOT IN
            <foreach item="item" index="index" collection="param.blackListAppId"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.serialNo != null">
            AND tfi.serial_no like concat('%', #{param.serialNo}::text, '%')
        </if>
        <if test="param.flowStatus != null">
            AND tfi.flow_status = #{param.flowStatus}
        </if>
        <if test="param.type != null and param.type != ''">
            AND tfi.type = #{param.type}
        </if>
        <if test="param.nameOrSerialNo != null and param.nameOrSerialNo != ''">
            AND (tfi.name like concat('%', #{param.nameOrSerialNo}::text, '%')
            OR tfi.serial_no like concat('%', #{param.nameOrSerialNo}::text, '%'))
        </if>
        <choose>
            <when test="param.orderType != null and param.orderType == 0">
                ORDER BY tfi.create_date
            </when>
            <otherwise>
                ORDER BY tfi.create_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="listMyTodoCalendar" resultMap="TodoVoMap">
        <include refid="todoSql"/>
        , tdtl.start_time::DATE AS startDate
        FROM todo_def td
        INNER JOIN todo_detail tdtl ON td.id = tdtl.todo_def_id
        INNER JOIN todo_user_relation tur ON tdtl.id = tur.todo_detail_id
        LEFT JOIN todo_flow_info tfi ON td.flow_id = tfi.id
        WHERE tur.status = 0
        AND tur.userid = #{param.userid}
        AND tdtl.start_time &gt;= #{param.startTime}
        AND tdtl.start_time &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
        <if test="param.appIdList != null and param.appIdList.size > 0 ">
            AND td.creator_type = 1
            AND td.creator in
            <foreach collection="param.appIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="param.blackListAppId != null and param.blackListAppId.size() > 0">
            AND td.creator NOT IN
            <foreach item="item" index="index" collection="param.blackListAppId"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY tdtl.start_time
    </select>

    <select id="listCompleteTodoDef" resultType="com.lanshan.base.commonservice.todo.dto.TodoCompleteDefDto">
        SELECT tur.todo_def_id  AS todoDefId,
        max(td.agent_id) AS agentId,
        max(td.cal_id)   AS calId
        FROM todo_def td,
        todo_user_relation tur
        WHERE td.id = tur.todo_def_id
        AND td.is_repeat = 0
        AND td.is_remind = 1
        AND td.cal_id IS NOT NULL
        AND td.id in
        <foreach collection="defIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY tur.todo_def_id
        HAVING COUNT(1) = SUM(CASE WHEN tur.status = 1 THEN 1 ELSE 0 END)
    </select>

    <select id="pageTodoRecord" resultMap="TodoVoMap">
        <include refid="todoSql"/>
        FROM todo_def td
        INNER JOIN todo_detail tdtl ON td.id = tdtl.todo_def_id
        INNER JOIN todo_user_relation tur ON tdtl.id = tur.todo_detail_id
        LEFT JOIN todo_flow_info tfi ON td.flow_id = tfi.id
        <where>
            <if test="param.startTime != null and param.endTime != null" >
                AND tdtl.start_time &gt;= #{param.startTime}
                AND tdtl.start_time &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
            </if>
            <if test="param.name != null and param.name != ''">
                AND td.name like concat('%',#{param.name}::text,'%')
            </if>
            <if test="param.appIdStr != null  and param.appIdStr != ''">
                AND td.creator = #{param.appIdStr}
            </if>
            <if test="param.appName != null and param.appName != ''">
                AND td.creator_name like concat('%',#{param.appName}::text,'%') AND td.creator_type = 1
            </if>
            <if test="param.userid != null and param.userid != ''">
                AND tur.userid like concat('%',#{param.userid}::text,'%')
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND tur.name like concat('%',#{param.userName}::text,'%')
            </if>
            <if test="param.status != null">
                AND tur.status = #{param.status}
            </if>
        </where>
        ORDER BY td.create_date DESC
    </select>

    <select id="listUserCreateDate" resultType="com.lanshan.base.commonservice.todo.dto.TodoUserCreateDateDto">
        SELECT tur.userid, td.create_date AS createDate
        FROM todo_def td,
             todo_user_relation tur
        WHERE td.id = tur.todo_def_id
          AND td.flow_id = #{flowId}
          AND td.node_id = #{nodeId}
    </select>

    <select id="listUserTodoSerialNo" resultType="com.lanshan.base.commonservice.todo.dto.TodoUserDto">
        SELECT tfi.serial_no, td.create_date
        FROM todo_def td,
             todo_user_relation tur,
             todo_flow_info tfi
        WHERE td.id = tur.todo_def_id
          AND td.flow_id = tfi.id
          AND tur.userid = #{userid}
        ORDER BY td.create_date DESC
    </select>

    <select id="selectTypeList" resultType="java.lang.String">
        SELECT DISTINCT tfi.type
        FROM todo.todo_flow_info tfi,
             todo.todo_def td,
             todo.todo_user_relation tur
        WHERE tfi.id = td.flow_id
          AND td.id = tur.todo_def_id
          AND td.creator_type = 1
          AND tfi.type IS NOT NULL
          AND tur.userid = #{userid}
    </select>

    <select id="listTodoBySerialNo" resultType="java.lang.String">
        SELECT DISTINCT tfi.serial_no
        FROM todo_flow_info tfi
        WHERE app_id = #{appId}
        <if test="serialNoList != null and serialNoList.size() != 0">
            AND tfi.serial_no IN
            <foreach collection="serialNoList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>

