<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.todo.mapper.TodoUserAppStatMapper">
    <resultMap type="com.lanshan.base.commonservice.todo.entity.TodoUserAppStat" id="TodoUserAppStatMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="todayCompleted" column="today_completed" jdbcType="INTEGER"/>
        <result property="currentYearCompleted" column="current_year_completed" jdbcType="INTEGER"/>
        <result property="totalCompleted" column="total_completed" jdbcType="INTEGER"/>
        <result property="todayCreateTodo" column="today_create_todo" jdbcType="INTEGER"/>
        <result property="currentYearCreateTodo" column="current_year_create_todo" jdbcType="INTEGER"/>
        <result property="totalCreateTodo" column="total_create_todo" jdbcType="INTEGER"/>
        <result property="hourAgoCompleted" column="hour_ago_completed"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="hourAgoCreateTodo" column="hour_ago_create_todo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="dayAgoCompleted" column="day_ago_completed"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="dayAgoCreateTodo" column="day_ago_create_todo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result column="today_todo" property="todayTodo" jdbcType="INTEGER"/>
        <result column="today_delay_todo" property="todayDelayTodo" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="UserAppTPeriodTodoDTO" type="com.lanshan.base.commonservice.todo.dto.UserAppTPeriodTodoDTO">
        <result column="userId" property="userId"/>
        <result column="appId" property="appId"/>
        <result column="completedCount" property="completedCount"/>
        <result column="todoCreateCount" property="todoCreateCount"/>
        <result column="hourAgoCompleted" property="hourAgoCompleted"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="dayAgoCompleted" property="dayAgoCompleted"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="hourAgoCreateTodo" property="hourAgoCreateTodo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="dayAgoCreateTodo" property="dayAgoCreateTodo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="todayTodoCount" property="todayTodoCount"/>
        <result column="todayDelayTodoCount" property="todayDelayTodoCount"/>
    </resultMap>
    <resultMap id="CompletedStatVO" type="com.lanshan.base.commonservice.todo.vo.CompletedStatVO">
        <result column="hourAgoCompleted" property="hourOfCompletedVOMap"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="dayAgoCompleted" property="dayOfCompletedVOMap"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <resultMap id="TodoCreateStatVO" type="com.lanshan.base.commonservice.todo.vo.TodoCreateStatVO">
        <result column="hourAgoCreate" property="hourAgoCreate"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="dayAgoCreate" property="dayAgoCreate"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into todo.todo_user_app_stat(user_id, today_completed, current_year_completed, total_completed,
                                            today_todo, current_year_create_todo, total_create_todo, hour_ago_completed,
                                            hour_ago_create_todo, day_ago_completed, day_ago_create_todo,
                                            app_id, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.todayCompleted}, #{entity.currentYearCompleted}, #{entity.totalCompleted},
             #{entity.todayCreateTodo}, #{entity.currentYearCreateTodo}, #{entity.totalCreateTodo},
             #{entity.hourAgoCompleted},
             #{entity.hourAgoCreateTodo}, #{entity.dayAgoCompleted}, #{entity.dayAgoCreateTodo}, #{entity.appId},
             #{entity.createDate}, #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into todo.todo_user_app_stat(user_id, today_completed, current_year_completed, total_completed,
                                            today_todo, current_year_create_todo, total_create_todo, hour_ago_completed,
                                            hour_ago_create_todo, day_ago_completed, day_ago_create_todo,
                                            app_id, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.todayCompleted}, #{entity.currentYearCompleted}, #{entity.totalCompleted},
             #{entity.todayCreateTodo}, #{entity.currentYearCreateTodo}, #{entity.totalCreateTodo},
             #{entity.hourAgoCompleted},
             #{entity.hourAgoCreateTodo}, #{entity.dayAgoCompleted}, #{entity.dayAgoCreateTodo}, #{entity.appId},
             #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set user_id                  = EXCLUDED.user_id
                                    , today_completed          = EXCLUDED.today_completed
                                    , current_year_completed   =
                EXCLUDED.current_year_completed
                                    , total_completed          = EXCLUDED.total_completed
                                    , today_todo               = EXCLUDED.today_todo
                                    , current_year_create_todo = EXCLUDED.current_year_create_todo
                                    , total_create_todo        = EXCLUDED.total_create_todo
                                    , hour_ago_completed       =
                EXCLUDED.hour_ago_completed
                                    , hour_ago_create_todo     = EXCLUDED.hour_ago_create_todo
                                    , day_ago_completed        =
                EXCLUDED.day_ago_completed
                                    , day_ago_create_todo      = EXCLUDED.day_ago_create_todo
                                    , app_id                   = EXCLUDED.app_id
                                    , create_date              =
                EXCLUDED.create_date
                                    , update_date              = EXCLUDED.update_date
    </insert>
    <select id="groupUserAppStat" resultMap="UserAppTPeriodTodoDTO">
        <!--待办创建小时数据-->
        WITH create_hourly_data AS (SELECT tur.userid,
                                           td.creator,
                                           DATE_TRUNC('hour', td.create_date) AS hour_start,
                                           COUNT(tur.id)                      AS currentCreateTodo
                                    FROM todo.todo_user_relation tur
                                             LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.ID
        WHERE td.create_date BETWEEN #{startDate} AND #{endDate}
        <if test="userId != null and userId != ''">
            AND tur.userid = #{userId}
        </if>
        GROUP BY tur.userid,
                 td.creator,
                 hour_start
        ),
        <!--待办创建天数据-->
        create_daily_data AS (SELECT tur.userid,
                                     td.creator,
                                     DATE_TRUNC('day', td.create_date) AS day_start,
                                     SUM(CASE
                                             WHEN td.create_date BETWEEN #{startDate} AND #{endDate}
                                                 THEN 1
                                             ELSE 0 END)               AS currentCreateTodo
                              FROM todo.todo_user_relation tur
                                       LEFT JOIN todo.todo_detail tde ON tur.todo_detail_id = tde.id
                                       LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.ID
        WHERE td.create_date BETWEEN #{startDate} AND #{endDate}
        <if test="userId != null and userId != ''">
            AND tur.userid = #{userId}
        </if>
        GROUP BY tur.userid,
                 td.creator,
                 day_start
        ),
        <!--待办完成时间数据-->
        completed_hourly_data AS (SELECT tur.userid,
                                         td.creator,
                                         DATE_TRUNC('hour', tur.complete_time) AS hour_start,
                                         COUNT(tur.id)                         AS currentCompleted
                                  FROM todo.todo_user_relation AS tur
                                           LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.ID
        WHERE tur.complete_time BETWEEN #{startDate} AND #{endDate}
          AND tur.status = 1
        <if test="userId != null and userId != ''">
            AND tur.userid = #{userId}
        </if>
        GROUP BY tur.userid,
                 td.creator,
                 hour_start
        ),
        <!--待办完成天数据-->
        completed_daily_data AS (SELECT tur.userid,
                                        td.creator,
                                        DATE_TRUNC('day', tur.complete_time) AS day_start,
                                        COUNT(tur.id)                        AS currentCompleted
                                 FROM todo.todo_user_relation tur
                                          LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.ID
        WHERE tur.complete_time BETWEEN #{startDate} AND #{endDate}
          AND tur.status = 1
        <if test="userId != null and userId != ''">
            AND tur.userid = #{userId}
        </if>
        GROUP BY tur.userid,
                 td.creator,
                 day_start
        ),
        <!--今日待办以及今日延期数据-->
        today_todo_data AS (SELECT tur.userid,
                                   td.creator,
                                   COUNT(tur.id) FILTER (
                                       WHERE tur.status = 0
                                           AND DATE_TRUNC('day', tde.start_time::timestamp) &lt;=
                                               DATE_TRUNC('day', #{endDate}::timestamp)) AS todayTodoCount,
                                   COUNT(tur.id) FILTER (
                                       WHERE tur.status = 0 AND
                                             tde.is_delay = 1
                                           AND DATE_TRUNC('day', tde.start_time::timestamp) &lt;=
                                               DATE_TRUNC('day', #{endDate}::timestamp)) AS todayDelayTodoCount
                            FROM todo.todo_user_relation tur
                                     LEFT JOIN
                                 todo.todo_detail tde
                                 ON tur.todo_detail_id = tde.id
                                     LEFT JOIN
                                 todo.todo_def td
                                 ON tur.todo_def_id = td.ID
        <where>
            <if test="userId != null and userId != ''">
                AND tur.userid = #{userId}
            </if>
        </where>
        GROUP BY tur.userid,
                 td.creator
        )
        <!--today_todo_data 包含所有数据，故可以用 today_todo_data 作为主表，然后用 left join 关联其他表-->
        SELECT ttd.userid  AS userId,
               ttd.creator AS appId,
               cphd.hourAgoCompleted,
               crhd.hourAgoCreateTodo,
               cpdd.dayAgoCompleted,
               crdd.dayAgoCreateTodo,
               COALESCE(cphd.completedCount,
                        0) AS completedCount,
               COALESCE(crhd.todoCreateCount,
                        0) AS todoCreateCount,
               COALESCE(ttd.todayTodoCount,
                        0) AS todayTodoCount,
               COALESCE(ttd.todayDelayTodoCount,
                        0) AS todayDelayTodoCount
        FROM today_todo_data ttd
                 LEFT JOIN
             (SELECT userid,
                     creator,
                     jsonb_object_agg(hour_start::TEXT,
                                      currentCreateTodo) AS hourAgoCreateTodo,
                     SUM(currentCreateTodo)              AS todoCreateCount
              FROM create_hourly_data
              group by userid, creator) crhd
             ON ttd.userid = crhd.userid
                 AND ttd.creator = crhd.creator
                 LEFT JOIN
             (SELECT userid,
                     creator,
                     jsonb_object_agg(day_start::TEXT,
                                      currentCreateTodo) AS dayAgoCreateTodo
              FROM create_daily_data
              group by userid, creator) crdd ON ttd.userid = crdd.userid
                 AND ttd.creator = crdd.creator
                 LEFT JOIN
             (SELECT userid,
                     creator,
                     jsonb_object_agg(hour_start::TEXT, currentCompleted) AS hourAgoCompleted,
                     SUM(currentCompleted)                                AS completedCount
              FROM completed_hourly_data
              group by userid, creator) cphd
             ON ttd.userid = cphd.userid
                 AND ttd.creator = cphd.creator
                 LEFT JOIN (SELECT userid,
                                   creator,
                                   jsonb_object_agg(day_start::TEXT, currentCompleted) AS dayAgoCompleted
                            FROM completed_daily_data
                            group by userid, creator) cpdd ON ttd.userid = cpdd.userid
            AND ttd.creator = cpdd.creator;
    </select>

    <select id="getTodoStatInfoByUserId" resultType="com.lanshan.base.commonservice.todo.vo.UserTodoCompletedStatVO">
        SELECT SUM(today_completed)                                   AS todayCompleted,
               SUM(current_year_completed) + SUM(today_completed)     AS currentYearCompleted,
               SUM(total_completed) + SUM(today_completed)            AS totalCompleted,
               SUM(today_create_todo)                                 AS todayCreateTodo,
               SUM(current_year_create_todo) + SUM(today_create_todo) AS currentYearCreateTodo,
               SUM(total_create_todo) + SUM(today_create_todo)        AS totalCreateTodo,
               SUM(today_todo)                                        AS todayTodo,
               SUM(today_delay_todo)                                  AS todayDelayTodo,
               COUNT(DISTINCT app_id)                                 AS appCount,
        (SELECT COUNT(DISTINCT wa.dept_id) FILTER ( WHERE wa.dept_id IS NOT NULL and wa.dept_id != '' )
         FROM workbench.wb_app wa
        WHERE wa.id IN (SELECT DISTINCT app_id::numeric
                        FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        )) AS deptCount
        FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAgoCompletedMap" resultMap="CompletedStatVO">
        WITH hour_ago_completed AS (
        SELECT key,
               value
        FROM (SELECT hour_ago_completed
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) AS filtered_data, jsonb_each(hour_ago_completed)),
        day_ago_completed AS (
        SELECT key,
               value
        FROM (SELECT day_ago_completed
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) AS filtered_data, jsonb_each(day_ago_completed))
        SELECT jsonb_object_agg(a.key::TEXT, a.value) AS hourAgoCompleted,
               jsonb_object_agg(b.key::TEXT, b.value) AS dayAgoCompleted
        FROM (SELECT key,
                     SUM(value::numeric) AS value
              FROM hour_ago_completed
              GROUP BY key) a,
             (SELECT key,
                     SUM(value::numeric) AS value
              FROM day_ago_completed
              GROUP BY key) b
    </select>

    <select id="getDayAgoCompletedMap" resultType="java.util.Map">
        SELECT key,
               SUM(value::numeric)
        FROM (SELECT key,
                     value
        FROM (SELECT day_ago_completed
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
        </where>
        ) AS filtered_data,
            jsonb_each(day_ago_completed) AS data(key, value)) AS subquery
        GROUP BY key;
    </select>

    <select id="getAgoCreateTodo" resultMap="TodoCreateStatVO">
        WITH hour_ago_create AS (
        SELECT key,
               value
        FROM (SELECT hour_ago_create_todo
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) AS filtered_data, jsonb_each(hour_ago_create_todo)),
        day_ago_create AS (
        SELECT key,
               value
        FROM (SELECT day_ago_create_todo
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
            <if test="blackList != null and blackList.size() > 0">
                AND app_id NOT IN
                <foreach item="item" index="index" collection="blackList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) AS filtered_data, jsonb_each(day_ago_create_todo))
        SELECT jsonb_object_agg(a.key::TEXT, a.value) AS hourAgoCreate,
               jsonb_object_agg(b.key::TEXT, b.value) AS dayAgoCreate
        FROM (SELECT key,
                     SUM(value::numeric) AS value
              FROM hour_ago_create
              GROUP BY key) a,
             (SELECT key,
                     SUM(value::numeric) AS value
              FROM day_ago_create
              GROUP BY key) b
    </select>

    <select id="getDayAgoTodoMap" resultType="java.util.Map">
        SELECT key,
               SUM(value::numeric)
        FROM (SELECT key,
                     value
        FROM (SELECT day_ago_create_todo
              FROM todo.todo_user_app_stat
        <where>
            <if test="userId != null and userId != ''">
                user_id = #{userId}
            </if>
        </where>
        ) AS filtered_data,
            jsonb_each(day_ago_create_todo) AS data(key, value)) AS subquery
        GROUP BY key;
    </select>

    <select id="groupAppStatInfo" resultType="com.lanshan.base.commonservice.todo.vo.AppStatVO">
        WITH app_rank_data AS (SELECT td.creator AS appId,
                COUNT(tur.id) FILTER ( WHERE tur.status = 1
        <if test="startDate != null and endDate != null">
            AND tur.complete_time BETWEEN #{startDate} AND #{endDate}
        </if>
        ) AS totalCompleted,
                COUNT(tur.id)
        <if test="startDate != null and endDate != null">
            FILTER (WHERE td.create_date BETWEEN #{startDate} AND #{endDate})
        </if>
        AS totalTodo
        FROM todo.todo_user_relation tur
                 LEFT JOIN
             todo.todo_def td
             ON tur.todo_def_id = td.ID
        <where>
            <if test="startDate != null and endDate != null">
                AND td.create_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="userId != null and userId != ''">
                AND tur.userid = #{userId}
            </if>
            <if test="blackAppListStr != null and blackAppListStr.size() > 0">
                AND td.creator NOT IN
                <foreach item="item" index="index" collection="blackAppListStr"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY td.creator)
        SELECT ard.*, wa.app_name AS appName
        FROM app_rank_data ard
                 JOIN workbench.wb_app wa ON ard.appId = wa.id::text
        <where>
            <if test="blackAppListStr != null and blackAppListStr.size() > 0">
                AND wa.id NOT IN
                <foreach item="item" index="index" collection="blackAppListStr"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getUserTodoRankByTime" resultType="com.lanshan.base.commonservice.todo.vo.UserRankVO">
        SELECT tur.userid,
               COUNT(tur.id) AS totalTodo
        FROM todo.todo_user_relation tur
                 LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.id
        <where>
            <if test="startDate != null">
                td.create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND td.create_date &lt; #{endDate}
            </if>
        </where>
        GROUP BY tur.userid
        ORDER BY totalTodo DESC
        LIMIT 10
    </select>

    <select id="listTodayTodo" resultType="com.lanshan.base.commonservice.todo.dto.TodoTodayDto">
        SELECT user_id              AS userid,
               SUM(today_todo)      AS todayTodo,
               SUM(today_completed) AS todayCompleted
        FROM todo.todo_user_app_stat
        GROUP BY user_id
    </select>

    <select id="listTodayTodoWithConfig" resultType="com.lanshan.base.commonservice.todo.dto.TodoTodayDto">
        SELECT tuas.user_id               AS userid,
               SUM(tuas.today_todo)       AS todayTodo,
               SUM(tuas.today_delay_todo) AS todayDelayTodo
        FROM todo.todo_user_app_stat tuas
                 LEFT JOIN todo.todo_config trc ON tuas.user_id = trc.userid
        <where>
            <choose>
                <!-- 没有配置的默认为8点提醒 -->
                <when test="hour == 8">
                    AND ((trc.is_today_remind = 1 AND trc.today_remind_hour = 8) OR
                         (trc.is_today_remind IS NULL AND trc.today_remind_hour IS NULL))
                </when>
                <otherwise>
                    AND trc.is_today_remind = 1
                    AND trc.today_remind_hour = #{hour}
                </otherwise>
            </choose>
        </where>
        GROUP BY tuas.user_id
    </select>

    <select id="hourWeekStat" resultType="com.lanshan.base.commonservice.todo.vo.TodoHourWeekStatVO">
        SELECT EXTRACT(DOW FROM td.create_date)  AS week,
               EXTRACT(HOUR FROM td.create_date) AS hour,
               count(DISTINCT tur.id)            AS totalAddTodo
        FROM todo.todo_user_relation tur
                 LEFT JOIN todo.todo_def td ON tur.todo_def_id = td.ID
        WHERE td.create_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY week,
                 hour
    </select>

    <select id="listTodoUserWithConfig" resultType="string">
        SELECT tuas.user_id               AS userid
        FROM todo.todo_user_app_stat tuas
        LEFT JOIN todo.todo_config trc ON tuas.user_id = trc.userid
        <where>
            <choose>
                <!-- 没有配置的默认为8点提醒 -->
                <when test="hour == 8">
                    AND ((trc.is_today_remind = 1 AND trc.today_remind_hour = 8) OR
                    (trc.is_today_remind IS NULL AND trc.today_remind_hour IS NULL))
                </when>
                <otherwise>
                    AND trc.is_today_remind = 1
                    AND trc.today_remind_hour = #{hour}
                </otherwise>
            </choose>
        </where>
        GROUP BY tuas.user_id
    </select>

    <select id="getAppTodoRank" resultType="com.lanshan.base.commonservice.todo.vo.CommonRankVO">
        SELECT appRank.*,
               wa.app_name AS name FROM (SELECT td.creator   AS appId,
                                                COUNT(td.id) AS total
                                         FROM todo.todo_def td
        <where>
            <if test="startDate != null">
                td.create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND td.create_date &lt; #{endDate}
            </if>
        </where>
        GROUP BY td.creator) AS appRank
            JOIN workbench.wb_app wa ON appRank.appId = wa.id::text
        ORDER BY total DESC
        LIMIT 10
    </select>

    <select id="getDeptTodoRank" resultType="com.lanshan.base.commonservice.todo.vo.CommonRankVO">
        WITH app_rank_data AS (
        SELECT td.creator   AS appId,
               COUNT(td.id) AS total
        FROM todo.todo_def td
        <where>
            <if test="startDate != null">
                td.create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND td.create_date &lt; #{endDate}
            </if>
        </where>
        GROUP BY td.creator)
        SELECT wa.dept_id     AS deptId,
               wa.dept_name   AS name,
               SUM(ard.total) AS total
        FROM app_rank_data AS ard
                 JOIN workbench.wb_app wa ON ard.appId = wa.id::text
                 LEFT JOIN addressbook.cp_department cd ON wa.dept_id = cd.id::text
        WHERE wa.dept_id IS NOT NULL
          AND wa.dept_id != ''
        GROUP BY wa.dept_id, wa.dept_name
        ORDER BY total DESC
        LIMIT 10
    </select>
</mapper>

