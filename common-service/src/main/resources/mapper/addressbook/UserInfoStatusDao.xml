<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.UserInfoStatusDao">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.UserInfoStatus" id="UserInfoStatusMap">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="authStatus" column="auth_status" jdbcType="BOOLEAN"/>
        <result property="openStatus" column="open_status" jdbcType="BOOLEAN"/>
        <result property="authTime" column="auth_time" jdbcType="TIMESTAMP"/>
        <result property="openTime" column="open_time" jdbcType="TIMESTAMP"/>
        <result property="phoneNo" column="phone_no" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into addressbook.user_info_status(auth_status, open_status, auth_time, open_time, phone_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.authStatus} , #{entity.openStatus} , #{entity.authTime} , #{entity.openTime} , #{entity.phoneNo})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into addressbook.user_info_status(auth_status, open_status, auth_time, open_time, phone_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.authStatus}, #{entity.openStatus}, #{entity.authTime}, #{entity.openTime}, #{entity.phoneNo})
        </foreach>
        ON CONFLICT(id) DO update set
        auth_status = EXCLUDED.auth_status , open_status = EXCLUDED.open_status , auth_time = EXCLUDED.auth_time ,
        open_time = EXCLUDED.open_time , phone_no = EXCLUDED.phone_no
    </insert>
</mapper>

