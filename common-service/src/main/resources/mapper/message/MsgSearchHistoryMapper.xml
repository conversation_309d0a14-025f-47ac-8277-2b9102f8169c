<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.message.dao.MsgSearchHistoryMapper">

    <resultMap type="com.lanshan.base.commonservice.message.entity.MsgSearchHistory" id="MsgSearchHistoryMap">
        <result property="id" column="id"/>
        <result property="userid" column="userid"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="createDate" column="create_date"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

</mapper>

