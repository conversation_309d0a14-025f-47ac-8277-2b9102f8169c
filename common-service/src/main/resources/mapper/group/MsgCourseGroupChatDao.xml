<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.group.dao.MsgCourseGroupChatDao">
    <resultMap type="com.lanshan.base.commonservice.group.entity.MsgCourseGroupChat" id="MsgCourseGroupChatMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="semester" column="semester"/>
        <result property="courseCode" column="course_code"/>
        <result property="courseName" column="course_name"/>
        <result property="teachingClass" column="teaching_class"/>
        <result property="studentType" column="student_type"/>
        <result property="chatId" column="chat_id"/>
        <result property="chatName" column="chat_name"/>
        <result property="managerUserid" column="manager_userid"/>
        <result property="managerName" column="manager_name"/>
        <result property="joinCount" column="join_count"/>
        <result property="joinUserList" column="join_user_list"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="pageCourseGroupChat" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT wuct.xkkh      AS code,
               wuct.xnxq      AS semester,
               wuct.kcbm      AS courseCode,
               wuct.kcmc      AS courseName,
               1              AS studentType,
               wuct.jxbmc     AS teachingClass,
               mcc.chat_id    AS chatId,
               mcc.join_count AS joinCount,
               mcc.id         AS courseGroupChatId,
               mcc.chat_name  AS chatName,
               mcc.manager_name AS managerName
        FROM school_data.whut_undergraduate_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.xkkh = mcc.code and mcc.student_type = 1
        WHERE wuct.skjsgh = #{param.userid}
        UNION
        SELECT DISTINCT wuct.kbbh                         AS code,
                        CONCAT(wuct.kkxn, '-', wuct.kkxq) AS semester,
                        wuct.kcbm                        AS courseCode,
                        wuct.kcmc                        AS courseName,
                        2                               AS studentType,
                        NULL                            AS teachingClass,
                        mcc.chat_id                     AS chatId,
                        mcc.join_count                  AS joinCount,
                        mcc.id                          AS courseGroupChatId,
                        mcc.chat_name  AS chatName,
                        mcc.manager_name AS managerName
        FROM school_data.whut_master_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.kbbh = mcc.code and mcc.student_type = 2
        WHERE wuct.gh = #{param.userid}
        order by semester desc
    </select>

    <select id="pageByParam" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT wuct.xkkh      AS code,
               wuct.xnxq      AS semester,
               wuct.kcbm      AS courseCode,
               wuct.kcmc      AS courseName,
               1              AS studentType,
               wuct.jxbmc     AS teachingClass,
               mcc.chat_id    AS chatId,
               mcc.join_count AS joinCount,
               mcc.id         AS classGroupChatId
        FROM school_data.whut_undergraduate_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.xkkh = mcc.code
        UNION
        SELECT DISTINCT wuct.kbbh                         AS code,
                        CONCAT(wuct.kkxn, '-', wuct.kkxq) AS semester,
                        wuct.kcbm                         AS courseCode,
                        wuct.kcmc                         AS courseName,
                        2                                 AS studentType,
                        NULL                              AS teachingClass,
                        mcc.chat_id                       AS chatId,
                        mcc.join_count                    AS joinCount,
                        mcc.id                            AS classGroupChatId
        FROM school_data.whut_master_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.kbbh = mcc.code
        order by semester desc
    </select>


    <select id="pageUndergraduateByParam" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT wuct.xkkh                                    AS code,
               wuct.xnxq                                    AS semester,
               wuct.kcbm                                    AS courseCode,
               wuct.kcmc                                    AS courseName,
               1                                            AS studentType,
               wuct.jxbmc                                   AS teachingClass,
               MAX(mcc.chat_id)                             AS chatId,
               MAX(mcc.join_count)                          AS joinCount,
               MAX(mcc.id)                                  AS chatId,
               array_to_string(array_agg(wuct.skjsgh), ',') AS managerUserid
        FROM school_data.whut_undergraduate_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.xkkh = mcc.code
        <where>
            <if test="qo.chatName != null and qo.chatName != ''">
                AND mcc.chat_name LIKE '%' || #{qo.chatName} || '%'
            </if>
            <if test="qo.courseName != null and qo.courseName != ''">
                AND wuct.kcmc LIKE '%' || #{qo.courseName} || '%'
            </if>
            <if test="qo.courseTeacher != null and qo.courseTeacher != ''">
                AND EXISTS(SELECT userid
                           FROM addressbook.cp_user
                           WHERE name LIKE '%' || #{qo.courseTeacher} || '%' AND userid = wuct.skjsgh)
            </if>
            <if test="qo.courseTeacherGh != null and qo.courseTeacherGh != ''">
                AND wuct.skjsgh LIKE '%' || #{qo.courseTeacherGh} || '%'
            </if>
            <if test="qo.courseCode != null and qo.courseCode != ''">
                AND wuct.kcbm LIKE '%' || #{qo.courseCode} || '%'
            </if>
            <if test="qo.chatStatus != null">
                <if test="qo.chatStatus == 0">
                    AND mcc.chat_id is null
                </if>
                <if test="qo.chatStatus == 1">
                    AND mcc.chat_id is not null
                </if>
            </if>
        </where>
        GROUP BY wuct.xnxq, wuct.xkkh, wuct.kcbm, wuct.kcmc, wuct.jxbmc;
    </select>

    <select id="pageMasterByParam" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT DISTINCT wuct.kbbh                         AS code,
                        CONCAT(wuct.kkxn, '-', wuct.kkxq) AS semester,
                        MAX(wuct.kcbm)                    AS courseCode,
                        MAX(wuct.kcmc)                    AS courseName,
                        2                                 AS studentType,
                        NULL                              AS teachingClass,
                        MAX(mcc.chat_id)                  AS chatId,
                        MAX(mcc.join_count)               AS joinCount,
                        MAX(mcc.id)                       AS chatId
        FROM school_data.whut_master_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.kbbh = mcc.code
        <where>
            <if test="qo.chatName != null and qo.chatName != ''">
                AND mcc.chat_name LIKE '%' || #{qo.chatName} || '%'
            </if>
            <if test="qo.courseName != null and qo.courseName != ''">
                AND wuct.kcmc LIKE '%' || #{qo.courseName} || '%'
            </if>
            <if test="qo.courseTeacher != null and qo.courseTeacher != ''">
                AND wuct.rkjsmc LIKE '%' || #{qo.courseTeacher} || '%'
            </if>
            <if test="qo.courseTeacherGh != null and qo.courseTeacherGh != ''">
                AND wuct.gh LIKE '%' || #{qo.courseTeacherGh} || '%'
            </if>
            <if test="qo.courseCode != null and qo.courseCode != ''">
                AND wuct.kcbm LIKE '%' || #{qo.courseCode} || '%'
            </if>
            <if test="qo.chatStatus != null">
                <if test="qo.chatStatus == 0">
                    AND mcc.chat_id is null
                </if>
                <if test="qo.chatStatus == 1">
                    AND mcc.chat_id is not null
                </if>
            </if>
        </where>
        GROUP BY semester, wuct.kbbh
        ORDER BY semester desc
    </select>

    <select id="pageInternationalByParam" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">

    </select>

    <select id="pageCourseGroupChatByParam" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT wuct.xkkh                                             AS code,
               wuct.xnxq                                             AS semester,
               wuct.kcbm                                             AS courseCode,
               wuct.kcmc                                             AS courseName,
               1                                                     AS studentType,
               MAX(mcc.chat_id)                                      AS chatId,
               MAX(mcc.chat_name)                                    AS chatName,
               MAX(mcc.join_count)                                   AS joinCount,
               MAX(mcc.id)                                           AS courseGroupChatId,
               MAX(mcc.manager_userid)                               AS managerUserid,
               MAX(mcc.manager_name)                                 AS managerName,
               array_to_string(array_agg(DISTINCT wuct.skjsxm), ',') AS canCreateName,
               array_to_string(array_agg(DISTINCT wuct.skjsgh), ',') AS canCreateGh
        FROM school_data.whut_undergraduate_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.xkkh = mcc.code
        <where>
            <if test="qo.chatName != null and qo.chatName != ''">
                AND mcc.chat_name LIKE '%' || #{qo.chatName} || '%'
            </if>
            <if test="qo.courseName != null and qo.courseName != ''">
                AND wuct.kcmc LIKE '%' || #{qo.courseName} || '%'
            </if>
            <if test="qo.courseTeacher != null and qo.courseTeacher != ''">
                AND wuct.skjsxm LIKE '%' || #{qo.courseTeacher} || '%'
            </if>
            <if test="qo.courseTeacherGh != null and qo.courseTeacherGh != ''">
                AND wuct.skjsgh LIKE '%' || #{qo.courseTeacherGh} || '%'
            </if>
            <if test="qo.courseCode != null and qo.courseCode != ''">
                AND wuct.kcbm LIKE '%' || #{qo.courseCode} || '%'
            </if>
            <if test="qo.chatStatus != null">
                <if test="qo.chatStatus == 0">
                    AND mcc.chat_id is null
                </if>
                <if test="qo.chatStatus == 1">
                    AND mcc.chat_id is not null
                </if>
            </if>
        </where>
        GROUP BY wuct.xnxq, wuct.xkkh, wuct.kcbm, wuct.kcmc
        UNION ALL
        SELECT DISTINCT wuct.kbbh                                             AS code,
                        CONCAT(MAX(wuct.kkxn), '-', MAX(wuct.kkxq))           AS semester,
                        MAX(wuct.kcbm)                                        AS courseCode,
                        MAX(wuct.kcmc)                                        AS courseName,
                        2                                                     AS studentType,
                        MAX(mcc.chat_id)                                      AS chatId,
                        MAX(mcc.chat_name)                                    AS chatName,
                        MAX(mcc.join_count)                                   AS joinCount,
                        MAX(mcc.id)                                           AS courseGroupChatId,
                        MAX(mcc.manager_userid)                               AS managerUserid,
                        MAX(mcc.manager_name)                                 AS managerName,
                        array_to_string(array_agg(DISTINCT wuct.rkjsmc), ',') AS canCreateName,
                        array_to_string(array_agg(DISTINCT wuct.gh), ',')     AS canCreateGh
        FROM school_data.whut_master_course_teacher wuct
                 LEFT JOIN message.msg_course_group_chat mcc ON wuct.kbbh = mcc.code
        <where>
            <if test="qo.chatName != null and qo.chatName != ''">
                AND mcc.chat_name LIKE '%' || #{qo.chatName} || '%'
            </if>
            <if test="qo.courseName != null and qo.courseName != ''">
                AND wuct.kcmc LIKE '%' || #{qo.courseName} || '%'
            </if>
            <if test="qo.courseTeacher != null and qo.courseTeacher != ''">
                AND wuct.rkjsmc LIKE '%' || #{qo.courseTeacher} || '%'
            </if>
            <if test="qo.courseTeacherGh != null and qo.courseTeacherGh != ''">
                AND wuct.gh LIKE '%' || #{qo.courseTeacherGh} || '%'
            </if>
            <if test="qo.courseCode != null and qo.courseCode != ''">
                AND wuct.kcbm LIKE '%' || #{qo.courseCode} || '%'
            </if>
            <if test="qo.chatStatus != null">
                <if test="qo.chatStatus == 0">
                    AND mcc.chat_id is null
                </if>
                <if test="qo.chatStatus == 1">
                    AND mcc.chat_id is not null
                </if>
            </if>
        </where>
        GROUP BY semester, wuct.kbbh
        ORDER BY semester desc
    </select>

    <select id="getUndergRkjsGhStrByCode" resultType="java.lang.String">
        select array_to_string(array_agg(DISTINCT skjsgh), ',')
        from school_data.whut_undergraduate_course_teacher
        where xkkh = #{code}
    </select>

    <select id="getMasterRkjsGhStrByCode" resultType="java.lang.String">
        select array_to_string(array_agg(DISTINCT gh), ',')
        from school_data.whut_master_course_teacher
        where kbbh = #{code}
    </select>

    <select id="undergGroupByCode" resultType="com.lanshan.base.commonservice.group.vo.CourseCodeSkjsInfoVO">
        select wuct.xkkh                                             as courseCode,
               array_to_string(array_agg(DISTINCT wuct.skjsgh), ',') as canCreateGh,
               array_to_string(array_agg(DISTINCT wuct.skjsxm), ',') as canCreateName
        from school_data.whut_undergraduate_course_teacher wuct
        group by wuct.xkkh
    </select>

    <select id="masterGroupByCode" resultType="com.lanshan.base.commonservice.group.vo.CourseCodeSkjsInfoVO">
        select wmct.kbbh                                             as courseCode,
               array_to_string(array_agg(DISTINCT wmct.gh), ',')    as canCreateGh,
               array_to_string(array_agg(DISTINCT wmct.rkjsmc), ',') as canCreateName
        from school_data.whut_master_course_teacher wmct
        group by wmct.kbbh
    </select>

    <select id="getUndergStuCount" resultType="java.lang.Integer">
        select count(1)
        from school_data.whut_undergraduate_course_student
        where xkkh = #{code}
    </select>

    <select id="getMasterStuCount" resultType="java.lang.Integer">
        select count(1)
        from school_data.whut_master_course_student
        where kbbh = #{code}
    </select>

    <select id="pageCourseGroupChatCommon" resultType="com.lanshan.base.commonservice.group.vo.MsgCourseGroupChatVO">
        SELECT dct.jxbdm        AS code,
               dct.xnxq         AS semester,
               dct.kcbm         AS courseCode,
               dct.kcmc         AS courseName,
               1                AS studentType,
               dct.jxbmc        AS teachingClass,
               mcc.chat_id      AS chatId,
               mcc.join_count   AS joinCount,
               mcc.id           AS courseGroupChatId,
               mcc.chat_name    AS chatName,
               mcc.manager_name AS managerName
        FROM dws.dws_course_teacher dct
                 LEFT JOIN message.msg_course_group_chat mcc ON dct.jxbdm = mcc.code and mcc.student_type = 1
        WHERE dct.skjsgh = #{param.userid}
    </select>
</mapper>

