<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.group.dao.GroupChatScopeDao">

    <resultMap type="com.lanshan.base.commonservice.group.entity.GroupChatScope" id="GroupChatScopeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="chatid" column="chatid" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="dataId" column="data_id" jdbcType="VARCHAR"/>
        <result property="dataName" column="data_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into public.group_chat_scope(chatid, type, data_id, data_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatid} , #{entity.type} , #{entity.dataId} , #{entity.dataName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into public.group_chat_scope(chatid, type, data_id, data_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatid}, #{entity.type}, #{entity.dataId}, #{entity.dataName})
        </foreach>
        ON CONFLICT(id) DO update set
        chatid = EXCLUDED.chatid , type = EXCLUDED.type , data_id = EXCLUDED.data_id , data_name = EXCLUDED.data_name
    </insert>

</mapper>

