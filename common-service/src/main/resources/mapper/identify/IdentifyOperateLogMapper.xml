<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.identify.mapper.IdentifyOperateLogMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.identify.entity.IdentifyOperateLogEntity">
        <!--@Table identify_operate_log-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
        <result property="operateObject" column="operate_object" jdbcType="VARCHAR"/>
        <result property="operateDetail" column="operate_detail" jdbcType="VARCHAR"/>
        <result property="operateStatus" column="operate_status" jdbcType="INTEGER"/>
        <result property="failDetail" column="fail_detail" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="lastUpdateTime" column="last_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
operator,
department,
operate_type,
operate_object,
operate_detail,
operate_status,
fail_detail,

create_time,
last_update_time

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.identify_operate_log(operator, department, operate_type, operate_object,
        operate_detail, operate_status, fail_detail, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.operator}, #{entity.department}, #{entity.operateType}, #{entity.operateObject},
            #{entity.operateDetail}, #{entity.operateStatus}, #{entity.failDetail}, #{entity.createTime},
            #{entity.lastUpdateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.identify_operate_log(operator, department, operate_type, operate_object,
        operate_detail, operate_status, fail_detail, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.operator}, #{entity.department}, #{entity.operateType}, #{entity.operateObject},
            #{entity.operateDetail}, #{entity.operateStatus}, #{entity.failDetail}, #{entity.createTime},
            #{entity.lastUpdateTime})
        </foreach>
        on duplicate key update
        operator = values(operator) , department = values(department) , operate_type = values(operate_type) ,
        operate_object = values(operate_object) , operate_detail = values(operate_detail) , operate_status =
        values(operate_status) , fail_detail = values(fail_detail) , create_time = values(create_time) ,
        last_update_time = values(last_update_time)
    </insert>
    <insert id="insertByMe">
        insert into standard_data.identify_operate_log(operator, department, operate_type, operate_object,
        operate_detail, operate_status, fail_detail)
        values
            (#{entity.operator}, #{entity.department}, #{entity.operateType}, #{entity.operateObject},
            #{entity.operateDetail}, #{entity.operateStatus}, #{entity.failDetail})
    </insert>
</mapper>

