<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.identify.mapper.StaffMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.identify.entity.StaffEntity">
        <!--@Table staff-->
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="staffName" column="staff_name" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="VARCHAR"/>
            <result property="idCardType" column="id_card_type" jdbcType="VARCHAR"/>
            <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateTime" column="last_update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="staffId" column="staff_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        staff_id,
staff_name,
sex,
id_card_type,
id_card,
phone,
create_time,
last_update_time,

status

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.staff(staff_id,staff_name, sex, id_card_type, id_card, phone, create_time, last_update_time, status)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.staffId},#{entity.staffName}, #{entity.sex}, #{entity.idCardType}, #{entity.idCard}, #{entity.phone}, #{entity.createTime}, #{entity.lastUpdateTime}, #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.staff(staff_id,staff_name, sex, id_card_type, id_card, phone, create_time, last_update_time, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.staffId},#{entity.staffName}, #{entity.sex}, #{entity.idCardType}, #{entity.idCard}, #{entity.phone}, #{entity.createTime}, #{entity.lastUpdateTime}, #{entity.status})
        </foreach>
        on duplicate key update staff_id = values(staff_id)
staff_name = values(staff_name) , sex = values(sex) , id_card_type = values(id_card_type) , id_card = values(id_card) , phone = values(phone) , create_time = values(create_time) , last_update_time = values(last_update_time) , status = values(status)     </insert>

    <insert id="insertOne" parameterType="com.lanshan.base.commonservice.identify.entity.StaffEntity">
        <selectKey resultType="java.lang.Long"  order="BEFORE" keyProperty="id" >
            select nextval('standard_data.staff_id_seq'::regclass) as id
        </selectKey>
        insert into standard_data.staff(staff_id,staff_name, sex, id_card_type, id_card, phone)
        values  (#{entity.staffId}, #{entity.staffName}, #{entity.sex}, #{entity.idCardType}, #{entity.idCard}, #{entity.phone})
    </insert>

    <select id="selectByParam" resultType="java.lang.String">
        select s.staff_id from standard_data.staff s left join standard_data.staff_identify_relation r on s.staff_id = r.staff_id
        where s.status = 1 and r.status=1
        <if test="idCard != null and idCard!= '' ">
            and  s.id_card=#{idCard}
        </if>
        <if test="stuOrStaffNo != null and stuOrStaffNo!= '' ">
            and r.staff_no_type in ('学号','工号') and r.staff_no=#{stuOrStaffNo}
        </if>
        <if test="yktCode != null and yktCode!= '' ">
            and r.staff_no_type='一卡通号' and r.staff_no=#{yktCode}
        </if>
    </select>

    <select id="selectByStaffId" resultMap="BaseResultMap">
        select * from  standard_data.staff where staff_id = #{staffId} and status =1
    </select>
    <select id="selectByIdCard" resultMap="BaseResultMap">
        select * from  standard_data.staff where id_card_type = #{idCardType} and id_card=#{idCard}  and status =1
    </select>
</mapper>

