<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.identify.mapper.StaffIdentifyRelationMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation">
        <!--@Table staff_identify_relation-->
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="staffId" column="staff_id" jdbcType="VARCHAR"/>
            <result property="identifyId" column="identify_id" jdbcType="INTEGER"/>
            <result property="fClassIdentify" column="f_class_identify" jdbcType="VARCHAR"/>
            <result property="sClassIdentify" column="s_class_identify" jdbcType="VARCHAR"/>
            <result property="staffNoType" column="staff_no_type" jdbcType="VARCHAR"/>
            <result property="staffNo" column="staff_no" jdbcType="VARCHAR"/>
            <result property="fClassDept" column="f_class_dept" jdbcType="VARCHAR"/>
            <result property="sClassDept" column="s_class_dept" jdbcType="VARCHAR"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateTime" column="last_update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="identifyStatus" column="identify_status" jdbcType="VARCHAR"/>
            <result property="defaultIdentify" column="default_identify" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
id,
staff_id,
identify_id,
f_class_identify,
s_class_identify,
staff_no_type,
staff_no,
f_class_dept,

s_class_dept,
end_time,
create_time,
last_update_time,
status,
start_time,
source,
identify_status,
  default_identify

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.staff_identify_relation(staff_id, identify_id, f_class_identify, s_class_identify, staff_no_type, staff_no, f_class_dept, s_class_dept, end_time, create_time, last_update_time, status, start_time, source, identify_status,default_identify)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.staffId}, #{entity.identifyId}, #{entity.fClassIdentify}, #{entity.sClassIdentify}, #{entity.staffNoType}, #{entity.staffNo}, #{entity.fClassDept}, #{entity.sClassDept}, #{entity.endTime}, #{entity.createTime}, #{entity.lastUpdateTime}, #{entity.status}, #{entity.startTime}, #{entity.source}, #{entity.identifyStatus}, #{entity.defaultIdentify})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.staff_identify_relation(staff_id, identify_id, f_class_identify, s_class_identify, staff_no_type, staff_no, f_class_dept, s_class_dept, end_time, create_time, last_update_time, status, start_time, source, identify_status,default_identify)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.staffId}, #{entity.identifyId}, #{entity.fClassIdentify}, #{entity.sClassIdentify}, #{entity.staffNoType}, #{entity.staffNo}, #{entity.fClassDept}, #{entity.sClassDept}, #{entity.endTime}, #{entity.createTime}, #{entity.lastUpdateTime}, #{entity.status}, #{entity.startTime}, #{entity.source}, #{entity.identifyStatus},#{entity.defaultIdentify})
        </foreach>
        on duplicate key update
staff_id = values(staff_id) , identify_id = values(identify_id) , f_class_identify = values(f_class_identify) , s_class_identify = values(s_class_identify) , staff_no_type = values(staff_no_type) , staff_no = values(staff_no) , f_class_dept = values(f_class_dept) , s_class_dept = values(s_class_dept) , end_time = values(end_time) , create_time = values(create_time) , last_update_time = values(last_update_time) , status = values(status) , start_time = values(start_time) , source = values(source) , identify_status = values(identify_status)   , default_identify = values(defaultIdentify)    </insert>

    <select id="getByStaffId" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
                 where (staff_no_type = '学号' or staff_no_type = '工号' or staff_no_type = '一卡通号') and status = 1   and staff_id = #{staffId}
    </select>

    <select id="getByNo" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where (staff_no_type = '学号' or staff_no_type = '工号' or staff_no_type = '一卡通号') and status = 1 and staff_no = #{staffNo}
    </select>

    <select id="selectListByStaffId" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status != 0 and staff_id = #{lanshanUserId}
    </select>
    <select id="selectCurrentIdentify" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1  and staff_id = #{lanshanUserId} and default_identify =1
    </select>

    <select id="selectIdentifyByActive" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1  and staff_id = #{lanshanUserId} order by start_time asc limit 1
    </select>
    <select id="selectIdentifyByStaffIdAndIdentifyId" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1  and staff_id = #{lanshanUserId} and identify_id = #{identifyId}
    </select>

    <insert id="insertByMe">
        insert into standard_data.staff_identify_relation(staff_id, identify_id, f_class_identify, s_class_identify, staff_no_type, staff_no, f_class_dept, s_class_dept, end_time, start_time, source, identify_status,default_identify)
        values
            (#{entity.staffId}, #{entity.identifyId}, #{entity.fClassIdentify}, #{entity.sClassIdentify}, #{entity.staffNoType}, #{entity.staffNo}, #{entity.fClassDept}, #{entity.sClassDept}, #{entity.endTime}, #{entity.startTime}, #{entity.source}, #{entity.identifyStatus}, #{entity.defaultIdentify})
    </insert>
    <select id="selectAllIdentifyAfterEndTime" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1   AND end_time::DATE &lt; CURRENT_DATE
    </select>
    <update id="updateByIds">
        update standard_data.staff_identify_relation set status = 2

            where  staff_id IN
            <foreach collection="staffIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </update>


    <select id="getIdentifysByFilter" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1
              and s_class_identify in
              <foreach collection="identifyTypes" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>

        and identify_status in
        <foreach collection="identifyStatus" item="is" open="(" close=")" separator=",">
            #{is}
        </foreach>

    </select>

    <select id="getIdentifysByF" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1
        and s_class_identify =#{sClassIdentify}

        and identify_status =#{identifyStatus}

    </select>



    <update id="removeIdentify">
        update standard_data.staff_identify_relation set status = 2 , identify_status='已入学'

        where   staff_id IN
        <foreach collection="staffIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
         and identify_id IN
        <foreach collection="identifyIds" item="identifyId" open="(" close=")" separator=",">
            #{identifyId}
        </foreach>
    </update>


    <select id="selectByIdentifyStaus" resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1
          and identify_status IN ('毕业','离职','退休')

    </select>

    <update id="updateByStaffAndIdentify">
        update standard_data.staff_identify_relation set status = 2 ,identify_status='失效' where
              staff_id = #{staffId} and identify_id= #{identifyId}

    </update>

    <select id="selectByIdentifyByStatus"  resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1
        <if test="statusNameList != null and statusNameList.size() > 0">
            AND identify_status IN
            <foreach collection="statusNameList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <update id="updateByStatus">
        update standard_data.staff_identify_relation set default_identify = #{entity.defaultIdentify} where id = #{entity.id}
    </update>
    <select id="getNewIdentify"  resultMap="BaseResultMap">
        select * from standard_data.staff_identify_relation
        where status =1  and identify_status='待入学' and s_class_identify = '新生'
        and   staff_id IN
        <foreach collection="staffIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>

