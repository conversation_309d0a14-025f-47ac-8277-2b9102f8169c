<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdClassRoomInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdClassRoomInfo" id="StdClassRoomInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="classRoomNumber" column="class_room_number" jdbcType="VARCHAR"/>
        <result property="classRoomName" column="class_room_name" jdbcType="VARCHAR"/>
        <result property="buildingNumber" column="building_number" jdbcType="VARCHAR"/>
        <result property="affiliatedvBuildingName" column="affiliatedv_building_name" jdbcType="VARCHAR"/>
        <result property="campusNumber" column="campus_number" jdbcType="VARCHAR"/>
        <result property="affiliatedCampusName" column="affiliated_campus_name" jdbcType="VARCHAR"/>
        <result property="rightLongitude" column="right_longitude" jdbcType="VARCHAR"/>
        <result property="leftLongitude" column="left_longitude" jdbcType="VARCHAR"/>
        <result property="upperLatitude" column="upper_latitude" jdbcType="VARCHAR"/>
        <result property="lowerLatitude" column="lower_latitude" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_class_room_info(class_room_number, class_room_name, building_number, affiliatedv_building_name, campus_number, affiliated_campus_name, right_longitude, left_longitude, upper_latitude, lower_latitude, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.classRoomNumber} , #{entity.classRoomName} , #{entity.buildingNumber} , #{entity.affiliatedvBuildingName} , #{entity.campusNumber} , #{entity.affiliatedCampusName} , #{entity.rightLongitude} , #{entity.leftLongitude} , #{entity.upperLatitude} , #{entity.lowerLatitude} , #{entity.createDate} , #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_class_room_info(class_room_number, class_room_name, building_number, affiliatedv_building_name, campus_number, affiliated_campus_name, right_longitude, left_longitude, upper_latitude, lower_latitude, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.classRoomNumber}, #{entity.classRoomName}, #{entity.buildingNumber}, #{entity.affiliatedvBuildingName}, #{entity.campusNumber}, #{entity.affiliatedCampusName}, #{entity.rightLongitude}, #{entity.leftLongitude}, #{entity.upperLatitude}, #{entity.lowerLatitude}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set
class_room_number = EXCLUDED.class_room_number , class_room_name = EXCLUDED.class_room_name , building_number = EXCLUDED.building_number , affiliatedv_building_name = EXCLUDED.affiliatedv_building_name , campus_number = EXCLUDED.campus_number , affiliated_campus_name = EXCLUDED.affiliated_campus_name , right_longitude = EXCLUDED.right_longitude , left_longitude = EXCLUDED.left_longitude , upper_latitude = EXCLUDED.upper_latitude , lower_latitude = EXCLUDED.lower_latitude , create_date = EXCLUDED.create_date , update_date = EXCLUDED.update_date     </insert>

</mapper>

