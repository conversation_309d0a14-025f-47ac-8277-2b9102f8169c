<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdSalarInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdSalarInfo" id="SalarInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="providentFundAccount" column="provident_fund_account" jdbcType="VARCHAR"/>
        <result property="unitName" column="unit_name" jdbcType="VARCHAR"/>
        <result property="month" column="month" jdbcType="VARCHAR"/>
        <result property="salarTotal" column="salar_total" jdbcType="VARCHAR"/>
        <result property="salarInfo" column="salar_info" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.salar_info(userId, user_name, provident_fund_account, unit_name, month, salar_total,
        salar_info, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId} , #{entity.userName} , #{entity.providentFundAccount} , #{entity.unitName} ,
            #{entity.month} , #{entity.salarTotal} , #{entity.salarInfo} , #{entity.createTime} , #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.salar_info(userId, user_name, provident_fund_account, unit_name, month, salar_total,
        salar_info, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.providentFundAccount}, #{entity.unitName}, #{entity.month},
            #{entity.salarTotal}, #{entity.salarInfo}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set
        user_id = EXCLUDED.user_id , user_name = EXCLUDED.user_name , provident_fund_account =
        EXCLUDED.provident_fund_account , unit_name = EXCLUDED.unit_name , month = EXCLUDED.month , salar_total =
        EXCLUDED.salar_total , salar_info = EXCLUDED.salar_info , create_time = EXCLUDED.create_time , update_time =
        EXCLUDED.update_time
    </insert>

</mapper>

