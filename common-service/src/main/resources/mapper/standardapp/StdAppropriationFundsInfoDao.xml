<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdAppropriationFundsInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdAppropriationFundsInfo" id="StdAppropriationFundsInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="unitPayment" column="unit_payment" jdbcType="VARCHAR"/>
        <result property="summaryInfo" column="summary_info" jdbcType="VARCHAR"/>
        <result property="amountAllocated" column="amount_allocated" jdbcType="VARCHAR"/>
        <result property="amountArrived" column="amount_arrived" jdbcType="VARCHAR"/>
        <result property="availableAmount" column="available_amount" jdbcType="VARCHAR"/>
        <result property="arrivalDate" column="arrival_date" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_appropriation_funds_info(user_id, user_name, unit_payment, summary_info, amount_allocated, amount_arrived, available_amount, arrival_date, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.userId} , #{entity.userName} , #{entity.unitPayment} , #{entity.summaryInfo} , #{entity.amountAllocated} , #{entity.amountArrived} , #{entity.availableAmount} , #{entity.arrivalDate} , #{entity.createTime} , #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_appropriation_funds_info(user_id, user_name, unit_payment, summary_info, amount_allocated, amount_arrived, available_amount, arrival_date, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.unitPayment}, #{entity.summaryInfo}, #{entity.amountAllocated}, #{entity.amountArrived}, #{entity.availableAmount}, #{entity.arrivalDate}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set
user_id = EXCLUDED.user_id , user_name = EXCLUDED.user_name , unit_payment = EXCLUDED.unit_payment , summary_info = EXCLUDED.summary_info , amount_allocated = EXCLUDED.amount_allocated , amount_arrived = EXCLUDED.amount_arrived , available_amount = EXCLUDED.available_amount , arrival_date = EXCLUDED.arrival_date , create_time = EXCLUDED.create_time , update_time = EXCLUDED.update_time     </insert>

</mapper>

