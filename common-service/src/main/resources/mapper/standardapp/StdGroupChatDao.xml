<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdGroupChatDao">
    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdGroupChat" id="StdGroupChatMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uniqueId" column="unique_id" jdbcType="VARCHAR"/>
        <result property="createMethod" column="create_method" jdbcType="VARCHAR"/>
        <result property="groupChatType" column="group_chat_type" jdbcType="VARCHAR"/>
        <result property="groupChatId" column="group_chat_id" jdbcType="VARCHAR"/>
        <result property="groupChatName" column="group_chat_name" jdbcType="VARCHAR"/>
        <result property="manageUserid" column="manage_userid" jdbcType="VARCHAR"/>
        <result property="manageName" column="manage_name" jdbcType="VARCHAR"/>
        <result property="joinInUserid" column="join_in_userid"
                typeHandler="com.lanshan.base.commonservice.typehandler.StringListTypeHandler"/>
        <result property="errorMsg" column="error_msg" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_group_chat(unique_id, create_method, group_chat_type, group_chat_id,
                                                group_chat_name, manage_userid, manage_name, join_in_userid, error_msg,
                                                create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.uniqueId}, #{entity.createMethod}, #{entity.groupChatType}, #{entity.groupChatId},
             #{entity.groupChatName}, #{entity.manageUserid}, #{entity.manageName}, #{entity.joinInUserid},
             #{entity.errorMsg}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_group_chat(unique_id, create_method, group_chat_type, group_chat_id,
                                                group_chat_name, manage_userid, manage_name, join_in_userid, error_msg,
                                                create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.uniqueId}, #{entity.createMethod}, #{entity.groupChatType}, #{entity.groupChatId},
             #{entity.groupChatName}, #{entity.manageUserid}, #{entity.manageName}, #{entity.joinInUserid},
             #{entity.errorMsg}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set unique_id       = EXCLUDED.unique_id,
                                      create_method   = EXCLUDED.create_method,
                                      group_chat_type = EXCLUDED.group_chat_type,
                                      group_chat_id   = EXCLUDED.group_chat_id,
                                      group_chat_name = EXCLUDED.group_chat_name,
                                      manage_userid   = EXCLUDED.manage_userid,
                                      manage_name     = EXCLUDED.manage_name,
                                      join_in_userid  = EXCLUDED.join_in_userid,
                                      error_msg       = EXCLUDED.error_msg,
                                      create_time     = EXCLUDED.create_time,
                                      update_time     = EXCLUDED.update_time
    </insert>
</mapper>

