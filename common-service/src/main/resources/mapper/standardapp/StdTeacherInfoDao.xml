<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdTeacherInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdTeacherInfo" id="StdTeacherInfoMap">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="affiliatedUnitNumber" column="affiliated_unit_number" jdbcType="VARCHAR"/>
        <result property="affiliatedUnitName" column="affiliated_unit_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into standard_app.std_teacher_info(name, affiliated_unit_number, affiliated_unit_name, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.affiliatedUnitNumber} , #{entity.affiliatedUnitName} , #{entity.createDate} , #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into standard_app.std_teacher_info(name, affiliated_unit_number, affiliated_unit_name, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.affiliatedUnitNumber}, #{entity.affiliatedUnitName}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , affiliated_unit_number = EXCLUDED.affiliated_unit_number , affiliated_unit_name = EXCLUDED.affiliated_unit_name , create_date = EXCLUDED.create_date , update_date = EXCLUDED.update_date     </insert>

</mapper>

