<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdCourseStudentDao">
    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdCourseStudent" id="StdCourseStudentMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="studentId" column="student_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_course_student(course_id, student_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.courseId} , #{entity.studentId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_course_student(course_id, student_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.courseId}, #{entity.studentId})
        </foreach>
        ON CONFLICT(id) DO update set
        course_id = EXCLUDED.course_id , student_id = EXCLUDED.student_id
    </insert>

    <select id="listCourseUserVO" resultType="com.lanshan.base.commonservice.standardapp.vo.CourseUserVO">
        SELECT course_user.course_id,
               cp_user.userid,
               cp_user.name,
               CASE
                   WHEN cp_user.userid is not null
                       THEN true
                   ELSE false END                                    AS joinInCpFlag,
               CASE WHEN cp_user.status = 1 THEN true ELSE false END AS activeFlag,
               course_user.student_type                              AS courseUserType
        FROM (SELECT *
              FROM standard_app.std_course_student
              WHERE course_id = #{uniqueId}) course_user
                 LEFT JOIN addressbook.cp_user
                           ON cp_user.userid = course_user.student_id
        WHERE userid is not null
          AND cp_user.status IN (1, 4)
    </select>
</mapper>

