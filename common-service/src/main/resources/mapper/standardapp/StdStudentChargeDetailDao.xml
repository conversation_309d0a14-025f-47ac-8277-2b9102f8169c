<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdStudentChargeDetailDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdStudentChargeDetail"
               id="StdStudentChargeDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="projectSection" column="project_section" jdbcType="VARCHAR"/>
        <result property="chargeTime" column="charge_time" jdbcType="VARCHAR"/>
        <result property="payableNum" column="payable_num" jdbcType="VARCHAR"/>
        <result property="paidNum" column="paid_num" jdbcType="VARCHAR"/>
        <result property="derateNum" column="derate_num" jdbcType="VARCHAR"/>
        <result property="returnPremiumNum" column="return_premium_num" jdbcType="VARCHAR"/>
        <result property="arrearageNum" column="arrearage_num" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_student_charge_detail(user_id, type, project_name, project_section, charge_time,
        payable_num, paid_num, derate_num, return_premium_num, arrearage_num)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId} , #{entity.type} , #{entity.projectName} , #{entity.projectSection} , #{entity.chargeTime}
            , #{entity.payableNum} , #{entity.paidNum} , #{entity.derateNum} , #{entity.returnPremiumNum} ,
            #{entity.arrearageNum})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_student_charge_detail(user_id, type, project_name, project_section, charge_time,
        payable_num, paid_num, derate_num, return_premium_num, arrearage_num)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.type}, #{entity.projectName}, #{entity.projectSection}, #{entity.chargeTime},
            #{entity.payableNum}, #{entity.paidNum}, #{entity.derateNum}, #{entity.returnPremiumNum},
            #{entity.arrearageNum})
        </foreach>
        ON CONFLICT(id) DO update set
        user_id = EXCLUDED.user_id , type = EXCLUDED.type , project_name = EXCLUDED.project_name , project_section =
        EXCLUDED.project_section , charge_time = EXCLUDED.charge_time , payable_num = EXCLUDED.payable_num , paid_num =
        EXCLUDED.paid_num , derate_num = EXCLUDED.derate_num , return_premium_num = EXCLUDED.return_premium_num ,
        arrearage_num = EXCLUDED.arrearage_num
    </insert>

</mapper>

