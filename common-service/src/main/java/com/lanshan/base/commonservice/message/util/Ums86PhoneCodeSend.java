package com.lanshan.base.commonservice.message.util;

import org.springframework.stereotype.Component;

@Component("ums86PhoneCodeSend")
public class Ums86PhoneCodeSend implements SmsPhoneCodeSend{
    /**
     * @param phone 手机号
     * @param code  验证码
     */
    @Override
    public void sendPhoneCode(String phone, String code) {
        Ums86SmsSend.sendPhoneCode(phone, code);
    }

    /**
     * @param phone  手机号
     * @param code   验证码
     * @param userId 用户id
     */
    @Override
    public void sendPhoneCode(String phone, String code, String userId) {
        sendPhoneCode(phone, code);
    }
}
