package com.lanshan.base.commonservice.access.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统开放API调用日志(AcApiCallLog)表实体类
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AcApiCallLog extends Model<AcApiCallLog> {
    private static final long serialVersionUID = 1242214341003902307L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 接口ID
     */
    private Long apiId;
    /**
     * 接口名称
     */
    private String apiName;
    /**
     * 接口调用接入方ID
     */
    private Long companyId;
    /**
     * 接口调用接入方名称
     */
    private String companyName;
    /**
     * 接口调用应用ID
     */
    private Long appId;
    /**
     * 接口调用应用名称
     */
    private String appName;
    /**
     * 服务ID
     */
    private Long serviceId;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 接口调用请求参数
     */
    private String requestParam;
    /**
     * 接口调用结果状态
     */
    private Integer responseStatus;
    /**
     * 接口调用时间
     */
    private Date createTime;

    /**
     * 接口调用耗时
     */
    private Long execTime;

    /**
     * 返回值
     */
    private String result;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

