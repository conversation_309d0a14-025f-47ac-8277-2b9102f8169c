package com.lanshan.base.commonservice.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.workbench.dto.WbAppStatSearchDTO;
import com.lanshan.base.commonservice.workbench.entity.WbAppStat;
import com.lanshan.base.commonservice.workbench.enums.AppStatTypeEnum;
import com.lanshan.base.commonservice.workbench.enums.AppStatUserRealmEnum;
import com.lanshan.base.commonservice.workbench.vo.AppStatOverviewVO;
import com.lanshan.base.commonservice.workbench.vo.AppStatTimeScaleVO;
import com.lanshan.base.commonservice.workbench.vo.AppUserStatInfoVO;
import com.lanshan.base.commonservice.workbench.vo.WbAppStatVO;

import java.util.List;
import java.util.Map;

/**
 * 应用统计(WbAppStat)表服务接口
 *
 * <AUTHOR>
 */
public interface WbAppStatService extends IService<WbAppStat> {

    /**
     * 分页查询应用统计列表
     *
     * @param searchDTO 查询条件
     * @return 应用统计列表
     */
    List<AppStatTimeScaleVO> listAppStatTimeScale(WbAppStatSearchDTO searchDTO);

    /**
     * 获取应用统计概览
     *
     * @param appId 查询条件
     * @param tagId
     * @return 应用统计概览
     */
    AppStatOverviewVO getAppOverview(Long appId, Long tagId);

    /**
     * 获取应用统计时间范围内的使用情况
     *
     * @param type          类型 1:日 2:月
     * @param number        时间范围
     * @param appId         应用ID
     * @param statTypeEnum  统计类型 USED:使用量 CLICK:点击量
     * @param userRealmEnum
     * @param tagId
     * @return
     */
    Map<String, Integer> groupMonthAppStat(String type, Integer number, Long appId, AppStatTypeEnum statTypeEnum,
                                           AppStatUserRealmEnum userRealmEnum, Long tagId);

    /**
     * 获取应用使用情况
     *
     * @param appId 应用ID
     * @param tagId 标签ID
     * @return 应用使用情况
     */
    AppUserStatInfoVO getAppUserStatInfo(Long appId, String tagId);

    /**
     * 批量更新应用统计
     *
     * @param wbAppStatVOS 应用统计列表
     */
    void updateBatchByDateAndAppId(List<WbAppStatVO> wbAppStatVOS);

    /**
     * 批量更新应用月统计
     *
     * @param monthAppStatVOS 应用月统计列表
     */
    void updateBatchByDateMonthAndAppId(List<WbAppStatVO> monthAppStatVOS);
}

