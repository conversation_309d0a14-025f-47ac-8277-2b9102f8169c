package com.lanshan.base.commonservice.inviteoutside.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.inviteoutside.pojo.InviteSetting;
import com.lanshan.base.commonservice.inviteoutside.service.InviteManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 邀请外部人员-邀请管理端接口
 */
@RestController
@RequestMapping("/invite-outside/inviteManager")
@Api(tags = "邀请外部人员-邀请管理端接口")
public class InviteManagerController {

    @Resource
    private InviteManagerService inviteManagerService;

    @ApiOperation("获取邀请设置")
    @GetMapping("/getInviteSetting")
    public Result<InviteSetting> getInviteSetting() {
        return Result.build(inviteManagerService.getInviteSetting());
    }

    @ApiOperation("启用禁用")
    @PostMapping("/enableDisable")
    public Result<Boolean> enableDisable(Long companyId, Boolean status) {
        return Result.build(inviteManagerService.enableDisable(companyId, status));
    }

    @ApiOperation("修改邀请设置")
    @PostMapping("/changeInviteSetting")
    public Result<Boolean> changeInviteSetting(@RequestBody InviteSetting inviteSetting) {
        return Result.build(inviteManagerService.changeInviteSetting(inviteSetting));
    }


}
