package com.lanshan.base.commonservice.minio.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 对象文件信息(ComMinioFile)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "对象文件信息VO")
@Data
@ToString
public class ComMinioFileVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "文件相对路径，不包含bucket名称")
    private String path;

    @ApiModelProperty(value = "文件所属bucket名称")
    private String bucket;

    @ApiModelProperty(value = "文件原始名称")
    private String originalName;

    @ApiModelProperty(value = "服务器上文件名称，不包含路径")
    private String fileName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识。1：删除。0：未删除")
    private String isDeleted;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    private String fileType;
}

