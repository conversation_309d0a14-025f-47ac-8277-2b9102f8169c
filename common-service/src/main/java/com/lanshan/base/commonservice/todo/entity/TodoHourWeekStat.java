package com.lanshan.base.commonservice.todo.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 周次小时统计表(TodoHourWeekStat)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName("todo_hour_week_stat")
public class TodoHourWeekStat extends Model<TodoHourWeekStat> implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 小时
     */
    private Integer hour;
    /**
     * 周几
     */
    private Integer week;
    /**
     * 添加代办总次数
     */
    private Integer totalAddTodo;
    /**
     * 总统计次数
     */
    private Integer totalStat;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

