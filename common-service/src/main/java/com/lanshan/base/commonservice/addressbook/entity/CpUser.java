package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户表(CpUser)实体
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "cp_user", autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpUser implements Serializable {
    private static final long serialVersionUID = -69397266025346950L;

    @TableId
    @ApiModelProperty("成员UserID。对应管理端的帐号，企业内必须唯一。不区分大小写，长度为1~64个字节")
    private String userid;

    @ApiModelProperty("成员名称；第三方不可获取，调用时返回userid以代替name；对于非第三方创建的成员，第三方通讯录应用也不可获取；第三方页面需要通过通讯录展示组件来展示名字")
    private String name;

    @ApiModelProperty("成员所属部门id列表，仅返回该应用有查看权限的部门id；成员授权模式下，固定返回根部门id，即固定为1")
    @TableField(typeHandler = ListIntegerToListLongTypeHandler.class)
    private List<Long> department;

    @ApiModelProperty("部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)。成员授权模式下不返回该字段")
    @TableField(value = "\"order\"", typeHandler = JacksonTypeHandler.class)
    private List<Integer> order;

    @ApiModelProperty("职务信息；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String position;

    @ApiModelProperty("手机号码，第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String mobile;

    @ApiModelProperty("性别。0表示未定义，1表示男性，2表示女性")
    private String gender;

    @ApiModelProperty("邮箱，第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String email;

    @ApiModelProperty("企业邮箱，代开发自建应用需要管理员授权且成员oauth2授权获取；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段")
    @JsonProperty("biz_mail")
    private String bizMail;

    @ApiModelProperty("表示在所在的部门内是否为上级。；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonProperty("is_leader_in_dept")
    private List<Integer> isLeaderInDept;

    @ApiModelProperty("直属上级UserID")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonProperty("direct_leader")
    private List<String> directLeader;

    @ApiModelProperty("头像url。 第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String avatar;

    @ApiModelProperty("头像缩略图url。第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    @JsonProperty("thumb_avatar")
    private String thumbAvatar;

    @ApiModelProperty("座机。第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String telephone;

    @ApiModelProperty("别名；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String alias;

    @ApiModelProperty("地址。第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    private String address;

    @ApiModelProperty("全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取")
    @JsonProperty("open_userid")
    private String openUserid;

    @ApiModelProperty("主部门")
    @JsonProperty("main_department")
    private Long mainDepartment;

    @ApiModelProperty("激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业。，6=处理中")
    private Integer status;

    @ApiModelProperty("员工个人二维码，扫描可添加为外部联系人(注意返回的是一个url，可在浏览器上打开该url以展示二维码)；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取")
    @JsonProperty("qr_code")
    private String qrCode;

    @ApiModelProperty("扩展属性，代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ObjectNode extattr;

    @ApiModelProperty("对外职务")
    @JsonProperty("external_position")
    private String externalPosition;

    @ApiModelProperty("成员对外属性")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonProperty("external_profile")
    private ObjectNode externalProfile;

    @ApiModelProperty("用户类型 0：其他人员 1：教职工 2：本科生 3：研究生")
    private Integer userType;

}

