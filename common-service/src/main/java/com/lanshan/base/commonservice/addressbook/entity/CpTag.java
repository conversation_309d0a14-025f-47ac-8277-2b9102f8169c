package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 标签表(CpTag)实体
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpTag implements Serializable {
    private static final long serialVersionUID = 150028264791732262L;

    @TableId
    @ApiModelProperty("标签id，非负整型")
    private Long tagid;

    @ApiModelProperty("标签名称，长度限制为32个字以内（汉字或英文字母），标签名不可与其他标签重名")
    private String tagname;

    @ApiModelProperty("应用ID")
    private Integer agentId;
}

