package com.lanshan.base.commonservice.access.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 系统开放API权限控制(AcApiControl)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "系统开放API权限控制VO")
@Data
@ToString
public class AcApiControlVO implements Serializable {

    private static final long serialVersionUID = 2502252819297969930L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "接口ID")
    private Long apiId;

    @ApiModelProperty(value = "控制类型。1：接入方；2：接入应用")
    private Integer controlType;

    @ApiModelProperty(value = "由控制类型决定接入方ID或应用ID")
    private Long controlId;
}

