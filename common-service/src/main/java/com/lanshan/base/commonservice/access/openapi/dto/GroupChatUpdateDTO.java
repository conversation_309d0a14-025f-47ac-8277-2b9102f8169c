package com.lanshan.base.commonservice.access.openapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("修改群聊请求对象")
public class GroupChatUpdateDTO {

    @NotEmpty(message = "群聊id不能为空")
    @ApiModelProperty(value = "群聊id", required = true)
    private String chatid;

    @ApiModelProperty(value = "新的群聊名。若不需更新，请忽略此参数。最多50个utf8字符，超过将截断", required = false)
    private String name;

    @ApiModelProperty(value = "新群主的id。若不需更新，请忽略此参数。课程群聊群主必须拥有课程群创建权限，del_user_list包含群主时本字段必填", required = false)
    private String owner;

    @ApiModelProperty(value = "添加成员的id列表", required = false)
    private List<String> add_user_list;

    @ApiModelProperty(value = "踢出成员的id列表", required = false)
    private List<String> del_user_list;
}