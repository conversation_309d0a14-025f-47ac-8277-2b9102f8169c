package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppThemeFile;
import com.lanshan.base.commonservice.workbench.vo.WbAppThemeFileVO;
import com.lanshan.base.commonservice.workbench.vo.WbAppThemeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * (WbAppThemeFile)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppThemeFileConverter {

    WbAppThemeFileConverter INSTANCE = Mappers.getMapper(WbAppThemeFileConverter.class);

    WbAppThemeFileVO toVO(WbAppThemeFile entity);

    WbAppThemeFile toEntity(WbAppThemeFileVO vo);

    List<WbAppThemeFileVO> toVO(List<WbAppThemeFile> entityList);

    List<WbAppThemeFile> toEntity(List<WbAppThemeFileVO> voList);

    @Mapping(source = "logoFilePath", target = "logoUrl")
    List<WbAppThemeVO.LogoInfo> toLogoInfo(List<WbAppThemeFile> entityList);

    @Mapping(source = "logoFilePath", target = "logoUrl")
    WbAppThemeVO.LogoInfo wbAppThemeFileToLogoInfo(WbAppThemeFile wbAppThemeFile);
}


