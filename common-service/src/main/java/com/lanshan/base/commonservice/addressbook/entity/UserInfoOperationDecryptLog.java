package com.lanshan.base.commonservice.addressbook.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * (UserInfoOperationDecryptLog)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class UserInfoOperationDecryptLog extends Model<UserInfoOperationDecryptLog> {
    private static final long serialVersionUID = 6356362644547700179L;
    private Long id;
    /**
     * 操作人
     */
    private String operationUser;
    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 被查看人姓名
     */
    private String userName;
    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

