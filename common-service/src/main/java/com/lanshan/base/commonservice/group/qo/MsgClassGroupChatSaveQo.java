package com.lanshan.base.commonservice.group.qo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class MsgClassGroupChatSaveQo implements Serializable {

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "学院编码")
    private String instituteCode;

    @ApiModelProperty(value = "专业编码")
    private String majorCode;

    @ApiModelProperty(value = "班级编码")
    private String classCode;

    @ApiModelProperty(value = "群聊名称")
    @NotBlank(message = "群聊名称不能为空")
    private String chatName;

    @ApiModelProperty(value = "学生类型 1：本科生 2：研究生")
    private Integer studentType;

    @ApiModelProperty(value = "辅导员列表", hidden = true)
    private List<String> counselorList;
}

