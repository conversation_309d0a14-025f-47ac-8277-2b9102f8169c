package com.lanshan.base.commonservice.standardapp.converter;

import com.lanshan.base.commonservice.standardapp.entity.RefundOpLog;
import com.lanshan.base.commonservice.standardapp.qo.RefundOpLogQO;
import com.lanshan.base.commonservice.standardapp.vo.RefundOpLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RefundOpLogConverter {

    RefundOpLogConverter INSTANCE = Mappers.getMapper(RefundOpLogConverter.class);

    RefundOpLogVO toVO(RefundOpLog entity);

    RefundOpLog toEntity(RefundOpLogVO vo);

    List<RefundOpLogVO> toVO(List<RefundOpLog> entityList);

    List<RefundOpLog> toEntity(List<RefundOpLogVO> voList);

    RefundOpLog toEntity(RefundOpLogQO vo);
}
