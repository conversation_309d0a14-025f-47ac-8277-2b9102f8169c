package com.lanshan.base.commonservice.system.entity;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lanshan.base.api.dto.system.SysUserChargeDeptVO;
import com.lanshan.base.api.dto.system.SysUserVo;
import com.lanshan.base.commonservice.system.dto.SysUserDTO;
import com.lanshan.base.commonservice.system.dto.SysUserV2DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息表(SysUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:15:51
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SysUser extends Model<SysUser> {
    private static final long serialVersionUID = -5634694263360778936L;
    //用户ID
    private Long userId;
    //部门ID
    private Long deptId;
    //用户账号
    private String userName;
    //用户昵称
    private String nickName;
    //用户类型（00系统用户）
    private String userType;
    //用户邮箱
    private String email;
    //手机号码
    private String phonenumber;
    //用户性别（0男 1女 2未知）
    private String sex;
    //头像地址
    private String avatar;
    //密码
    private String password;
    //帐号状态（0正常 1停用）
    private String status;
    //删除标志（0代表存在 2代表删除）
    private String delFlag;
    //最后登录IP
    private String loginIp;
    //最后登录时间
    private Date loginDate;
    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //更新者
    private String updateBy;
    //更新时间
    private Date updateTime;
    //备注
    private String remark;

    //系统用户所属企业ID
    private String corpId;

    /**
     * 部门对象
     */
    private SysDept dept;

    /**
     * 角色对象
     */
    private List<SysRole> roles;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    @TableField(exist = false)
    @ApiModelProperty(value = "用户所管部门ID列表")
    private List<Long> chargeDeptIdList;

    @TableField(exist = false)
    @ApiModelProperty(value = "用户企业微信所在部门")
    private List<SysUserChargeDeptVO> wxInDeptList;

    @TableField(exist = false)
    @ApiModelProperty(value = "用户企业微信所管理的部门")
    private List<SysUserChargeDeptVO> chargeDeptList;
    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private transient Map<String, Object> params = new HashMap<>(8);

    public SysUser() {
    }

    public SysUser(Long userId) {
        this.userId = userId;
    }

    public SysUser(SysUserVo vo) {
        BeanUtil.copyProperties(vo, this);
    }

    public SysUser(SysUserDTO dto) {
        BeanUtil.copyProperties(dto, this);
    }

    public SysUser(SysUserV2DTO dto) {
        BeanUtil.copyProperties(dto, this);
    }

    @JsonIgnore
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    public SysUserVo toSysUserVo() {
        SysUserVo vo = new SysUserVo();
        BeanUtil.copyProperties(this, vo);
        return vo;
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.userId;
    }

    @JsonIgnore
    public boolean isAdmin() {
        return isAdmin(this.userId);
    }
}

