package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用标签信息主表(WbTag)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WbTag extends Model<WbTag> {
    private static final long serialVersionUID = -8071070355631183131L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 标签名
     */
    private String tagName;
    /**
     * 标签分类
     */
    private String tagCatalog;
    /**
     * 是否已删除。true已删除
     */
    private Boolean isDeleted;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

