package com.lanshan.base.commonservice.standardapp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@EqualsAndHashCode
@Data
@ApiModel("学生成绩信息查询DTO")
public class ScoreSearchDTO {

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "考试性质")
    private String examNature;

    @ApiModelProperty(value = "课程标记 1 必修 2 选修")
    private String courseMarker;

    @ApiModelProperty(value = "学年学期集合，例如 学年：2022 学期 1，则 传入 2022-1")
    private Set<String> schoolYearTermSet;
}
