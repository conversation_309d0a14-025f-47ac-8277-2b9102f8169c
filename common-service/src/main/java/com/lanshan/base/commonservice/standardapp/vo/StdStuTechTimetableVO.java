package com.lanshan.base.commonservice.standardapp.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 课表-教师学生课表关联表(StdStuTechTimetable)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "课表-教师学生课表关联表VO")
@Data
@ToString
public class StdStuTechTimetableVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "学生 user_id")
    private String stuUserId;

    @ApiModelProperty(value = "教师 user_id")
    private String techUserId;

    @ApiModelProperty(value = "课表主键")
    private Long timId;
}

