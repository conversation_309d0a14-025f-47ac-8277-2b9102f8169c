package com.lanshan.base.commonservice.access.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.access.entity.AcData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限数据集(AcData)表数据库访问层
 *
 * <AUTHOR>
 */
public interface AcDataDao extends BaseMapper<AcData> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcData> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AcData> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcData> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AcData> entities);

}

