package com.lanshan.base.commonservice.group.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 群聊信息表(GroupChat)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class GroupChat extends Model<GroupChat> {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 群聊ID（企业微信的群聊唯一标识）
     */
    private String chatid;
    /**
     * 群名称
     */
    private String name;
    /**
     * 群主工号
     */
    private String owner;
    /**
     * 群主名称
     */
    private String ownerName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改人名称
     */
    private String updaterName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改时间
     */
    private Date updateDate;
    /**
     * 群聊类型
     */
    private Integer type;
    /**
     * 群聊状态(0:未激活 1:已激活)
     */
    private Integer status;
    /**
     * 群聊成员md5加密字符（用来对比所有成员是否已存在另一个群聊中）
     */
    private String memberIdEncrypt;
    /**
     * 建群消息
     */
    private String message;
    /**
     * 选择群主类型（0：指定群主，1：群内任一成员）
     */
    private Integer ownerType;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

