package com.lanshan.base.commonservice.workbench.entity;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler;
import com.lanshan.base.commonservice.typehandler.JsonbTypeHandler;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用悬浮球SDK配置表(WbAppSdkConfig)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@Builder
@TableName(autoResultMap = true)
public class WbAppSdkConfig extends Model<WbAppSdkConfig> {
    private static final long serialVersionUID = -1617373462063188258L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * SDK是否接入。默认：false
     */
    private Boolean isIntegrated;
    /**
     * SDK是否开启。默认：false，关闭
     */
    private Boolean isOpen;
    /**
     * 第一次接入时间
     */
    private Date integratedTime;
    /**
     * 上一次开启时间
     */
    private Date openTime;

    /**
     * SDK配置
     */
    @TableField(value = "config", typeHandler = JsonbTypeHandler.class)
    private Object config;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

