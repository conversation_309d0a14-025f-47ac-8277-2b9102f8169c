package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门标签-关联备份表(CpDepartmentTagRelationOperate)实体
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpDepartmentTagRelationOperate implements Serializable {
    private static final long serialVersionUID = -43992969763887295L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("部门id")
    private Long departmentid;

    @ApiModelProperty("标签id")
    private Long tagid;

    @ApiModelProperty("操作状态 0：未处理 1：成功 2：失败")
    private Integer operateStatus;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}

