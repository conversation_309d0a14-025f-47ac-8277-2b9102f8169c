package com.lanshan.base.commonservice.addressbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.qo.user.UserInfoPageQo;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * 用户表(User)数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Mapper
public interface CpUserMapper extends BaseMapper<CpUser> {

    //清空表
    @Update("TRUNCATE TABLE cp_user")
    void truncate();

    //复制备份表的数据到标准表
    @Insert("INSERT INTO cp_user SELECT * FROM cp_user_bak")
    void copyBakToStandard();

    /**
     * 获取部门成员(仅当前部门)
     *
     * @param departmentid 部门id
     * @return 部门成员列表
     */
    List<CpUser> listUsersByDepartmentid(Long departmentid);

    /**
     * 获取部门成员（包含当前部门及其下全部子部门，路径匹配查询）
     *
     * @param idPath 部门id路径
     * @return 部门成员列表
     */
    List<CpUser> listAllUsersByDepartmentid(String idPath);

    /**
     * 分页获取用户详情信息（用于完整信息查询搜索）-查询总条数
     *
     * @param userInfoPageQo 用户信息参数
     * @return 总条数
     */
    Integer pageUserInfoVoCount(UserInfoPageQo userInfoPageQo);

    /**
     * 分页获取用户详情信息（用于完整信息查询搜索）
     *
     * @param param 用户信息参数
     * @return 用户信息列表
     */
    List<CpUser> pageUserInfoVo(Page<?> page, UserInfoPageQo param);

    /**
     * 根据idPathList查询用户
     * @param idPathList
     * @return
     */
    List<CpUser> listAllUsersByDepartmentids(@Param("pathList") List<String> idPathList);

    Collection<UserInfoPartVO> getActiveUsers();

    CpUser selectByUserId(@Param("staffId") String staffId);

    List<CpUser> selectByAccount(@Param("staffNoList") List<String> staffNoList);

    List<UserInfoPartVO> listUsersByDeptIds(@Param("deptIds") List<Long> deptIds);
}

