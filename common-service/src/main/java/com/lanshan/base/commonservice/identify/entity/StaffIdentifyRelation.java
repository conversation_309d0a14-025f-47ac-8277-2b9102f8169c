package com.lanshan.base.commonservice.identify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * (StaffIdentifyRelation)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-10 14:06:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StaffIdentifyRelation {
@TableId(value = "id", type = IdType.AUTO)
    private Long id;
@TableField(value = "staff_id")
    private String staffId;
@TableField(value = "identify_id")
    private Long identifyId;
@TableField(value = "f_class_identify")
    private String fClassIdentify;
@TableField(value = "s_class_identify")
    private String sClassIdentify;
/**
     * 学号，工号，一卡通号
     */
@TableField(value = "staff_no_type")
    private String staffNoType;
@TableField(value = "staff_no")
    private String staffNo;
@TableField(value = "f_class_dept")
    private String fClassDept;
@TableField(value = "s_class_dept")
    private String sClassDept;
@TableField(value = "end_time")
    private Date endTime;
@TableField(value = "create_time")
    private Date createTime;
@TableField(value = "last_update_time")
    private Date lastUpdateTime;
@TableField(value = "status")
    private Integer status;
@TableField(value = "start_time")
    private Date startTime;
@TableField(value = "source")
    private String source;
@TableField(value = "identify_status")
    private String identifyStatus;
    @TableField(value = "default_identify")
    private Integer defaultIdentify;

    public StaffIdentifyRelation(String staffId, Long identifyId, String fClassIdentify, String sClassIdentify, String staffNoType, String staffNo, String fClassDept, String sClassDept, Date endTime, String source, Integer defaultIdentify) {
        this.staffId = staffId;
        this.identifyId = identifyId;
        this.fClassIdentify = fClassIdentify;
        this.sClassIdentify = sClassIdentify;
        this.staffNoType = staffNoType;
        this.staffNo = staffNo;
        this.fClassDept = fClassDept;
        this.sClassDept = sClassDept;
        this.endTime = endTime;
        this.source = source;
        this.defaultIdentify = defaultIdentify;
        if("新生".equals(sClassIdentify)){
            this.identifyStatus = "待入学";
        }else{
            this.identifyStatus = "正常";
        }

    }
}

