package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 空教室信息(StdEmptyClassroomInfo)表实体类
 */
@TableName(autoResultMap = true)
@Data
public class StdEmptyClassroomInfo implements Serializable{

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "校区代码")
    private String campusCode;

    @ApiModelProperty(value = "校区名称")
    private String campusName;

    @ApiModelProperty(value = "楼栋代码")
    private String buildingCode;

    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "教室代码")
    private String classroomCode;

    @ApiModelProperty(value = "教室名称")
    private String classroomName;

    @ApiModelProperty(value = "日期")
    private Date date;

    @ApiModelProperty(value = "节次是否空闲")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> sessionFree;

    @ApiModelProperty(value = "教室容量")
    private Integer capacity;
}

