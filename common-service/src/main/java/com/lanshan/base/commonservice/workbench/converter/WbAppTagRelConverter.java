package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppTagRel;
import com.lanshan.base.commonservice.workbench.vo.WbAppTagRelVO;
import com.lanshan.base.commonservice.workbench.vo.WbTagVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 应用标签关联表(WbAppTagRel)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppTagRelConverter {

    WbAppTagRelConverter INSTANCE = Mappers.getMapper(WbAppTagRelConverter.class);

    WbAppTagRelVO toVO(WbAppTagRel entity);

    WbAppTagRel toEntity(WbAppTagRelVO vo);

    List<WbAppTagRelVO> toVO(List<WbAppTagRel> entityList);

    List<WbAppTagRel> toEntity(List<WbAppTagRelVO> voList);

    @Mapping(target = "id", source = "tagId")
    WbTagVO toTagVO(WbAppTagRel entity);

    @Mapping(target = "id", source = "tagId")
    List<WbTagVO> toTagVO(List<WbAppTagRel> entity);
}


