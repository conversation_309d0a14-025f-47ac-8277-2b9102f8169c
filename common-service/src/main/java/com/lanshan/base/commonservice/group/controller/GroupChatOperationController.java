package com.lanshan.base.commonservice.group.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.group.qo.GroupChatOperationSearchQo;
import com.lanshan.base.commonservice.group.service.GroupChatOperationService;
import com.lanshan.base.commonservice.group.converter.GroupChatOperationConverter;
import com.lanshan.base.commonservice.group.vo.GroupChatOperationVO;
import com.lanshan.base.commonservice.log.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 群聊成员操作表(GroupChatOperation)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("groupChatOperation")
@Api(tags = "群聊成员操作表(GroupChatOperation)控制层", hidden = true)
public class GroupChatOperationController {
    /**
     * 服务对象
     */
    @Resource
    private GroupChatOperationService groupChatOperationService;

    /**
     * 分页查询所有数据
     *
     * @param qo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<GroupChatOperationVO>> selectAll(GroupChatOperationSearchQo qo) {
        IPage<GroupChatOperationVO> pageData = this.groupChatOperationService.pageGroupChatOperation(qo);
        return Result.build(pageData);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<GroupChatOperationVO> selectOne(@PathVariable Serializable id) {
        return Result.build(GroupChatOperationConverter.INSTANCE.toVO(this.groupChatOperationService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    @OperateLog("新增数据")
    public Result<Boolean> insert(@RequestBody GroupChatOperationVO vo) {
        return Result.build(this.groupChatOperationService.save(GroupChatOperationConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    @OperateLog("修改数据")
    public Result<Boolean> update(@RequestBody GroupChatOperationVO vo) {
        return Result.build(this.groupChatOperationService.updateById(GroupChatOperationConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    @OperateLog("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.groupChatOperationService.removeByIds(idList));
    }
}

