package com.lanshan.base.commonservice.message.handler;

import com.lanshan.base.api.dto.message.BaseMsgBody;
import com.lanshan.base.api.utils.Result;

public interface ChannelHandler<T extends BaseMsgBody> {

    /**
     * 处理消息总流程
     *
     * @param msgBody 消息体
     */
    Result<Object> handle(BaseMsgBody msgBody);

    /**
     * 保存发送消息日志
     *
     * @param msgBody 消息体
     */
    void saveLog(T msgBody);

    /**
     * 通过渠道发送消息
     *
     * @param msgBody 消息体
     */
    Result<Object> sendMsg(T msgBody);
}
