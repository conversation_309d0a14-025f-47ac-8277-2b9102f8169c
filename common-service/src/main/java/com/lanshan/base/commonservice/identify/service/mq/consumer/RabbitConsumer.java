package com.lanshan.base.commonservice.identify.service.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lanshan.base.api.qo.user.UserSaveWithTagQo;
import com.lanshan.base.commonservice.addressbook.dao.UserRemoveQueueMapper;
import com.lanshan.base.commonservice.addressbook.dao.UserRemoveRuleMapper;
import com.lanshan.base.commonservice.addressbook.entity.*;
import com.lanshan.base.commonservice.addressbook.mapper.CpDepartmentMapper;
import com.lanshan.base.commonservice.addressbook.mapper.CpTagMapper;
import com.lanshan.base.commonservice.addressbook.mapper.CpUserMapper;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.config.rabbitmq.MqConstant;
import com.lanshan.base.commonservice.identify.entity.StaffEntity;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import com.lanshan.base.commonservice.identify.mapper.StaffIdentifyRelationMapper;
import com.lanshan.base.commonservice.identify.mapper.StaffMapper;
import com.lanshan.base.commonservice.system.service.impl.SysConfigServiceImpl;
import com.lanshan.base.commonservice.workbench.dto.AppShowStyleDTO;
import com.lanshan.base.commonservice.workbench.service.impl.WbAppServiceImpl;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RabbitConsumer {

    @Value("${identifyworkbench.agentId}")
    private String agentId;
    @Value("${identifyworkbench.url-pre}")
    private String urlPrefix;
    @Value("${identifyworkbench.corpId}")
    private String corpId;

    @Resource
    StaffIdentifyRelationMapper staffIdentifyRelationMapper;

    @Autowired
    private SysConfigServiceImpl sysConfigService;

    @Resource
    CpUserMapper cpUserMapper;
    @Resource
    CpDepartmentMapper cpDepartmentMapper;

    @Resource
    CpTagMapper cpTagMapper;

    @Resource
    UserService userService;

    @Resource
    StaffMapper staffMapper;

    @Resource
    UserRemoveQueueMapper userRemoveQueueMapper;
    @Resource
    UserRemoveRuleMapper userRemoveRuleMapper;

    @Resource
    private WbAppServiceImpl wbAppService;

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;


    @RabbitListener(queues = "#{messageConfig.queuename}")
    public void receiveMessageHandler(Message message, @Header(AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) throws WxErrorException {
        byte[] body = message.getBody();
        String content = new String(body, StandardCharsets.UTF_8);
        log.info("------------------routingKey:{}---------------------收到消息: {}", routingKey, content);
        Boolean isActive = Boolean.parseBoolean(sysConfigService.selectConfigByKey("shenfen.isactive") == null ? "false" : sysConfigService.selectConfigByKey("shenfen.isactive"));
        if (isActive.booleanValue()) {
            try {
                if (routingKey.equals(MqConstant.IDENTIFY_ROUTER_KEY)) {
                    List<StaffIdentifyRelation> list = new Gson().fromJson(content,
                            new TypeToken<List<StaffIdentifyRelation>>() {
                            }.getType());
                    if (CollUtil.isEmpty(list)) {
                        log.info("消息为空");
                        return;
                    }
                    for (StaffIdentifyRelation sr : list) {
                        if (StringUtils.isEmpty(sr.getStaffId())) {
                            log.info("消息内容的staffId为空");
                            return;
                        }
                        CpUser cpUser = cpUserMapper.selectByUserId(sr.getStaffId());
                        if (cpUser == null) {
                            List<StaffIdentifyRelation> identifyRelations = staffIdentifyRelationMapper.getByStaffId(sr.getStaffId());
                            List<CpUser> cpUsers = new ArrayList<>();
                            if (CollUtil.isNotEmpty(identifyRelations)) {
                                List<String> accounts = identifyRelations.stream()
                                        .map(StaffIdentifyRelation::getStaffNo)
                                        .filter(Objects::nonNull)
                                        .distinct()
                                        .collect(Collectors.toList());
                                log.info("accounts:{}", accounts);
                                cpUsers = cpUserMapper.selectByAccount(accounts);
                            }

                            if (CollUtil.isNotEmpty(cpUsers)) {
                                log.info("企业微信账号已存在：{}", cpUsers);
                                return;
                            }
                        }

                        //创建企业微信账号
                        Long departmentId = getDepartmentId(sr.getFClassDept(), sr.getSClassDept());
                        List<CpTag> tags = cpTagMapper.selectTagsByIdentify(sr.getFClassIdentify(), sr.getSClassIdentify());
                        List<Long> tagIds = tags.stream()
                                .map(CpTag::getTagid)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        StaffEntity staff = staffMapper.selectByStaffId(sr.getStaffId());
                        if(staff==null){
                            log.info("staff不存在？？？？？？");
                        }
                        String userId = sr.getStaffNo();
                        if(StringUtils.isEmpty(userId)){
                            userId = sr.getStaffId();
                        }
                        try {
                            UserSaveWithTagQo utq = new UserSaveWithTagQo();
                            utq.setOperateType(1);
                            utq.setUserid(userId);
                            utq.setName(staff == null ? "" : staff.getStaffName());
                            utq.setDepartment(Arrays.asList(departmentId));
                            utq.setTagidList(tagIds);
                            utq.setGender(getSex(staff));
                            utq.setMobile(staff == null ? "" : staff.getPhone());
                            utq.setMainDepartment(departmentId);
                            utq.setIsLeaderInDept(new ArrayList<>() {{
                                add(0);
                            }});
                            log.info("staff phone:{}", staff.getPhone());
                            userService.saveUserWithTag(utq);
                        }catch (Exception ce){
                            log.error("新增人员失败：",ce);
                        }finally {
                            String url = urlPrefix+"/app-base/identityCard/index?scope=snsapi_base&corpId="+corpId+"&agentId="+agentId+"&userId="+userId+"&username="+staff.getStaffName()+"&staffNoType="+sr.getStaffNoType();
                            log.info("workbench url::::::::::::::::::::::::::::::::::::::{}",url);
                            WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
                            WxCpAgentWorkBench dto = new WxCpAgentWorkBench();
                            dto.setUrl(url);
                            dto.setJumpUrl(url);
                            dto.setHeight("single_row");
                            dto.setEnableWebviewClick(false);
                            dto.setHideTitle(true);
                            dto.setType("webview");
                            dto.setAgentId(Long.parseLong(agentId));
                            dto.setUserId(userId);
                            try {
                                wxCpService.getWorkBenchService().setWorkBenchData(dto);
                            }catch (Exception wxe){
                                log.error("工作台数据异常",wxe);
                            }
                            log.info("workbench result::::::::::::::::::::::::::::::::::::::success");
                        }
                    }
                } else if (routingKey.equals(MqConstant.IDENTIFY_CHANGE_ROUTER_KEY)) {
                    //身份中体推送毕业离职退休数据
                    List<StaffIdentifyRelation> list = new Gson().fromJson(content,
                            new TypeToken<List<StaffIdentifyRelation>>() {
                            }.getType());
                    List<UserRemoveQueue> queue = userRemoveQueueMapper.selectAllQueue();
                    List<String> noNeedDealUserIds = queue.stream().map(UserRemoveQueue::getUserId).collect(Collectors.toList());
                    log.info("noNeedDealUserIds --- {}",noNeedDealUserIds);
                    String userId = "";
                    for (StaffIdentifyRelation sir : list) {
                        if (StringUtils.isEmpty(sir.getStaffId()) || sir.getIdentifyId() == null) {
                            continue;
                        }
                        CpUser cpUser = cpUserMapper.selectByUserId(sir.getStaffId());
                        if (cpUser == null) {
                            List<StaffIdentifyRelation> identifyRelations = staffIdentifyRelationMapper.getByStaffId(sir.getStaffId());
                            List<String> accounts = identifyRelations.stream()
                                    .map(StaffIdentifyRelation::getStaffNo)
                                    .filter(Objects::nonNull)
                                    .distinct()
                                    .collect(Collectors.toList());
                            if(CollUtil.isEmpty(accounts)){
                                continue;
                            }
                            log.info("accounts:{}", accounts);
                            List<CpUser> cpUsers = cpUserMapper.selectByAccount(accounts);
                            if (CollUtil.isEmpty(cpUsers)) {
                                continue;
                            }
                            List<String> userIds = cpUsers.stream()
                                    .map(CpUser::getUserid)
                                    .filter(Objects::nonNull)
                                    .distinct()
                                    .collect(Collectors.toList());
                            if (CollUtil.isEmpty(userIds)) {
                                continue;
                            }
                            userId = userIds.get(0);
                        }
                        if (noNeedDealUserIds.contains(userId)) {
                            continue;
                        }
                        if(StringUtils.isEmpty(userId)){
                            continue;
                        }
                        // 放入成员离任自动化队列
                        List<UserRemoveRuleEntity> rules = userRemoveRuleMapper.selectAll();
                            UserRemoveQueue urq = new UserRemoveQueue();
                            urq.setUserId(userId);
                            Integer autoType = dealAutoType(sir.getIdentifyStatus());
                            Integer delayDays = dealDelayTime(autoType, rules);
                            Integer operateType = dealoperateType(autoType, rules);
                            urq.setOperateType(operateType);
                            urq.setAutoType(autoType);
                            // 获取日历实例
                            Calendar calendar = Calendar.getInstance();
                            // 增加30天
                            calendar.add(Calendar.DATE, delayDays);
                            // 获取结果日期
                            Date futureDate = calendar.getTime();
                            urq.setDealTime(futureDate);
                            urq.setDealStatus(0);
                            urq.setCreateTime(new Date());
                            userRemoveQueueMapper.insertByMe(urq);
                            noNeedDealUserIds.add(userId);

                    }
                }
                log.info("消费成功");
            } catch (Exception e) {
                log.info("消费fail : {} ", content, e);
                throw e;
            }
        }

    }

    private String getSex(StaffEntity staff) {
        if (staff == null || StringUtils.isEmpty(staff.getSex())) {
            return "0";
        }
        if (staff.getSex().equals("男")) {
            return "1";
        }
        return "0";
    }

    private Integer dealDelayTime(Integer autoType, List<UserRemoveRuleEntity> rules) {
        for (UserRemoveRuleEntity entity : rules) {
            if (autoType == entity.getAutoType() && entity.getAutoStatus() == 1) {
                return entity.getDelayDays();
            }
            continue;
        }
        return null;
    }

    private Integer dealoperateType(Integer autoType, List<UserRemoveRuleEntity> rules) {
        for (UserRemoveRuleEntity entity : rules) {
            if (autoType == entity.getAutoType() && entity.getAutoStatus() == 1) {
                return entity.getOperateType();
            }
            continue;
        }
        return null;
    }

    private Integer dealAutoType(String identifyStatus) {
        if ("毕业".equals(identifyStatus)) {
            return 1;
        } else if ("离职".equals(identifyStatus)) {
            return 2;
        }
        return 3;
    }

    private Long getDepartmentId(String fClassDept, String sClassDept) {
        Long departmentId = 1L;
        if (StringUtils.isEmpty(sClassDept) && StringUtils.isEmpty(fClassDept)) {
            return departmentId;
        }
        if (StringUtils.isEmpty(sClassDept)) {
            List<CpDepartment> cpDepts = cpDepartmentMapper.selectByName(fClassDept);
            if (CollUtil.isEmpty(cpDepts)) {
                return departmentId;
            } else {
                return cpDepts.get(0).getId();
            }
        } else {
            List<CpDepartment> cpDepts = cpDepartmentMapper.selectByName(sClassDept);
            if (CollUtil.isEmpty(cpDepts)) {
                return departmentId;
            } else {
                List<CpDepartment> cps = cpDepartmentMapper.selectByPath(fClassDept + "/" + sClassDept);
                if (CollUtil.isEmpty(cps)) {
                    return departmentId;
                } else {
                    return cps.get(0).getId();
                }
            }
        }
    }

}
