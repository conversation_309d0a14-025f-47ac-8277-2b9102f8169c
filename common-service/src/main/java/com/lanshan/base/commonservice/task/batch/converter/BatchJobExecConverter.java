package com.lanshan.base.commonservice.task.batch.converter;


import java.util.List;

import com.lanshan.base.commonservice.task.batch.entity.BatchJobExec;
import com.lanshan.base.commonservice.task.batch.vo.BatchJobExecVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 批量任务执行信息(BatchJobExec)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchJobExecConverter {

    BatchJobExecConverter INSTANCE = Mappers.getMapper(BatchJobExecConverter.class);

    BatchJobExecVO toVO(BatchJobExec entity);

    BatchJobExec toEntity(BatchJobExecVO vo);

    List<BatchJobExecVO> toVO(List<BatchJobExec> entityList);

    List<BatchJobExec> toEntity(List<BatchJobExecVO> voList);
}


