package com.lanshan.base.commonservice.group.converter;


import com.lanshan.base.commonservice.group.entity.MsgDeptGroupChat;
import com.lanshan.base.commonservice.group.vo.MsgDeptGroupChatVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 部门群聊(MsgDeptGroupChat)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface MsgDeptGroupChatConverter {

    MsgDeptGroupChatConverter INSTANCE = Mappers.getMapper(MsgDeptGroupChatConverter.class);

    @Mapping(target = "totalGroupMember", ignore = true)
    MsgDeptGroupChatVO toVO(MsgDeptGroupChat entity);

    @Mapping(target = "joinUserList", ignore = true)
    MsgDeptGroupChat toEntity(MsgDeptGroupChatVO vo);
    
    List<MsgDeptGroupChatVO> toVO(List<MsgDeptGroupChat> entityList);

    List<MsgDeptGroupChat> toEntity(List<MsgDeptGroupChatVO> voList);
}


