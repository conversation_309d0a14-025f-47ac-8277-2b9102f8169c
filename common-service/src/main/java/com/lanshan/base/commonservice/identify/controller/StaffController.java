package com.lanshan.base.commonservice.identify.controller;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.identify.dto.AddStaffDto;
import com.lanshan.base.commonservice.identify.service.StaffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = ServiceConstant.COMMON_IDENTIFY)
@Validated
@Api(tags = "员工导入接口", hidden = true)
@Slf4j
public class StaffController {
    @Autowired
    private StaffService staffService;

    @ApiOperation(value = "导入员工")
    @PostMapping(value = "importStaff")
    public Result<Object> importStaff(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.build().setCode(500).setMsg("请选择要上传的Excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            return Result.build().setCode(500).setMsg("请上传Excel格式的文件(.xls或.xlsx)");
        }
        try {
            return Result.build(staffService.importUserData(file));
        }catch (Exception e){
            return Result.build().setCode(500).setMsg(e.getMessage());
        }
    }
    @ApiOperation(value = "新增人员")
    @PostMapping(value = "addStaff")
    public  Result<Object> addStaff( AddStaffDto addStaffDto) {
        try {
            addStaffDto.checkParam(addStaffDto);
            staffService.add(addStaffDto);
            return Result.build(true);
        }catch (ServiceException se){
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        }catch (Exception e){
            return Result.build().setCode(500).setMsg(e.getMessage());
        }
    }
}
