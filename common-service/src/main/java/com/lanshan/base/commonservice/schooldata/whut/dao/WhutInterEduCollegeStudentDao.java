package com.lanshan.base.commonservice.schooldata.whut.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.group.dto.MsgClassGroupChatCountDTO;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutInterEduCollegeStudent;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 国教院计划外学生信息(WhutInterEduCollegeStudent)表数据库访问层
 *
 * <AUTHOR>
 */
public interface WhutInterEduCollegeStudentDao extends BaseMapper<WhutInterEduCollegeStudent> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutInterEduCollegeStudent> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WhutInterEduCollegeStudent> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutInterEduCollegeStudent> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<WhutInterEduCollegeStudent> entities);

    /**
     * 根据班级编码查询班级人数
     *
     * @param classCodeList 班级编码集合
     * @return 班级信息
     */
    List<MsgClassGroupChatCountDTO> listCountByBjmc(List<String> classCodeList);

    /**
     * 清空表
     */
    @Update("truncate table school_data.whut_inter_edu_college_student")
    void truncate();
}

