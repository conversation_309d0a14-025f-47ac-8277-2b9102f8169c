package com.lanshan.base.commonservice.standardapp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 退费用户
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class RefundUser extends Model<RefundUser> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属年份
     */
    private Integer year;
    /**
     * 学号
     */
    private String userId;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankNumber;

    /**
     * 持卡人姓名
     */
    private String bankUserName;

    /**
     * 是否捐赠
     */
    private Boolean donate;
    /**
     * 校园网余额
     */
    private String balance;

    /**
     * 一卡通余额
     */
    private String balanceCard;



    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
