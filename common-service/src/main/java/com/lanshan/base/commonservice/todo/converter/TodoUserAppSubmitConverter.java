package com.lanshan.base.commonservice.todo.converter;


import com.lanshan.base.commonservice.todo.entity.TodoUserAppSubmit;
import com.lanshan.base.commonservice.todo.vo.TodoUserAppSubmitVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户应用提交待办统计(TodoUserAppSubmit)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoUserAppSubmitConverter {

    TodoUserAppSubmitConverter INSTANCE = Mappers.getMapper(TodoUserAppSubmitConverter.class);

    TodoUserAppSubmitVO toVO(TodoUserAppSubmit entity);

    TodoUserAppSubmit toEntity(TodoUserAppSubmitVO vo);

    List<TodoUserAppSubmitVO> toVO(List<TodoUserAppSubmit> entityList);

    List<TodoUserAppSubmit> toEntity(List<TodoUserAppSubmitVO> voList);
}


