package com.lanshan.base.commonservice.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "用户创建时间dto")
public class TodoUserCreateDateDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;
}
