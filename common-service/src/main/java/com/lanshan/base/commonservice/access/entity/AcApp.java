package com.lanshan.base.commonservice.access.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 接入方应用(AcApp)表实体类
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AcApp extends Model<AcApp> {
    private static final long serialVersionUID = 1021034718090260959L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 操作类型 1：调用类型 2：调用、推送类型
     */
    private Integer operateType;
    /**
     * 应用类型 0：内部应用 1：外部应用
     */
    private Integer appType;
    /**
     * 应用所属接入方ID
     */
    private Long companyId;
    /**
     * 应用所属接入方名称
     */
    private String companyName;
    /**
     * 应用keyId
     */
    private String keyId;
    /**
     * 应用访问密钥
     */
    private String secret;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否需要用户授权 0:否 1:是
     */
    private String needUserAuth;
    /**
     * 申请原因
     */
    private String applicationReason;
    /**
     * 状态。0：禁用；1：正常
     */
    private Integer status;
    /**
     * 有效开始时间
     */
    private Date startTime;
    /**
     * 有效结束时间
     */
    private Date endTime;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 开放文档地址
     */
    private String openDocUrl;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

