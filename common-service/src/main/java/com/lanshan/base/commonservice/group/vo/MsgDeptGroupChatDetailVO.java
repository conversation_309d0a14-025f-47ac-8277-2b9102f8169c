package com.lanshan.base.commonservice.group.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "部门群聊详情VO")
@Data
public class MsgDeptGroupChatDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "是否加入企业微信 true 加入 false 未加入")
    private boolean hasJoinQywx;

    @ApiModelProperty(value = "是否加入群聊 true 加入 false 未加入")
    private boolean hasJoinGroup;

    @ApiModelProperty(value = "是否绑定 true 绑定 false 未绑定")
    private boolean hasBind;
}

