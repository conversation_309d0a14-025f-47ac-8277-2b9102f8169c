package com.lanshan.base.commonservice.addressbook.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 用户手机号绑定表(CpUserPhone)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户手机号绑定表VO")
@Data
@ToString
public class CpUserPhoneVO implements Serializable {

    private static final long serialVersionUID = -2197303005078377532L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户ID.(学校主要使用学工号)")
    private String userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "手机号。当前绑定手机号")
    private String phoneNo;

    @ApiModelProperty(value = "首次绑定时间")
    private Date createTime;

    @ApiModelProperty(value = "最近一次绑定时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注。记录之前的绑定信息")
    private String remark;

    @ApiModelProperty(value = "0：未同步；1：已同步到企业微信")
    private Integer syncStatus;

    @ApiModelProperty(value = "true删除。手机号绑定了已经不用的学工号的情况下")
    private Boolean isDeleted;
}

