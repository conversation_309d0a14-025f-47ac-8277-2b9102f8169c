package com.lanshan.base.commonservice.schooldata.northwestpolitics.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lanshan.base.commonservice.common.utils.HttpsRequestUtil;
import com.lanshan.base.commonservice.common.utils.JsonUtil;
import com.lanshan.base.commonservice.common.utils.ThreadPoolUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;

import static com.lanshan.base.commonservice.schooldata.northwestpolitics.constant.NorthwestPoliticsApiConstant.*;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
public class NorthwestPoliticsUtil {

    /**
     * 博士研究生学籍信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<DoctorStatusInfoDTO> getDoctorStatusInfo(Integer skip){
        List<DoctorStatusInfoDTO> list = getInfoMultiThread(DOCTOR_INFO_STATUS, skip,"xh desc", DoctorStatusInfoDTO.class,false);
        return list;
    }

    /**
     * 硕士研究生学籍信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<MasterStatusInfoDTO> getMasterStatusInfo(Integer skip){
        List<MasterStatusInfoDTO> list = getInfoMultiThread(MASTER_INFO_STATUS, skip,"xh desc", MasterStatusInfoDTO.class,false);
        return list;
    }

    /**
     * 硕士研究生联系信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<MasterContactDTO> getMasterContactInfo(Integer skip){
        List<MasterContactDTO> list = getInfoMultiThread(MASTER_INFO_CONTACT, skip,"XH desc", MasterContactDTO.class,false);
        return list;
    }

    /**
     * 本专科生_学籍信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<StudentStatusInfoDTO> getStudentStatusInfo(Integer skip){
        List<StudentStatusInfoDTO> list = getInfoMultiThread(STATUS_INFO_STATUS, skip,"xh desc", StudentStatusInfoDTO.class,false);
        return list;
    }

    /**
     * 本专科生_联系信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<StudentContactDTO> getStudentContactInfo(Integer skip){
        List<StudentContactDTO> list = getInfoMultiThread(CONTACT_INFO_STATUS, skip, "XH desc",StudentContactDTO.class,false);
        return list;
    }

    /**
     * 学生基本信息
     * <AUTHOR> yang.
     * @since 2025/4/30 9:52
     */
    public static List<StudentBasicInfoDTO> getStudentBasicInfo(Integer skip){
        List<StudentBasicInfoDTO> list = getInfoMultiThread(BASIC_INFO_STUDENT, skip,"xh desc", StudentBasicInfoDTO.class,false);
        return list;
    }

    /**
     * 教职工基本信息_综合
     * <AUTHOR> yang.
     * @since 2025/4/29 19:24
     */
    public static List<StaffInfoIntegratedDTO> getStaffInfoIntegrated(Integer skip){
        List<StaffInfoIntegratedDTO> list = getInfoMultiThread(BASIC_INFO_STAFF_INTEGRATED, skip,"gh desc", StaffInfoIntegratedDTO.class,false);
        return list;
    }

    /**
     * 教职工基本信息
     * <AUTHOR> yang.
     * @since 2025/4/29 19:24
     */
    public static List<StaffInfoDTO> getStaffInfo(Integer skip){
        List<StaffInfoDTO> list = getInfoMultiThread(BASIC_INFO_STAFF, skip,"gh desc", StaffInfoDTO.class,false);
        return list;
    }

    /**
     * 教职工联系信息
     * <AUTHOR> yang.
     * @since 2025/4/29 19:24
     */
    public static List<StaffContactDTO> getStaffContactInfo(Integer skip){
        List<StaffContactDTO> list = getInfoMultiThread(CONTACT_INFO_STAFF, skip,"GH desc", StaffContactDTO.class,false);
        return list;
    }


    /**
     * 学校机构基本信息
     * <AUTHOR> yang.
     * @since 2025/4/29 19:24
     */
    public static List<SchoolInfoDTO> getSchoolInfo(Integer skip){
        List<SchoolInfoDTO> list = getInfoMultiThread(BASIC_INFO_SCHOOL, skip,"JGBM desc", SchoolInfoDTO.class,false);
        return list;
    }

    /**
     * 获取信息通用方法
     * <AUTHOR> yang.
     * @since 2025/4/30 9:45
     * @param url 基本信息的请求 URL
     * @param skip 当前偏移量
     * @param <T> 返回的数据类型
     */
    private static <T> List<T> getInfoMultiThread(String url,  Integer skip,String orderBy, Class<T> clazz,boolean isPaging) {
        Map<String, String> headers = getAuthHeaders();
        Map<String, Object> params = getParams(skip,orderBy);
        String rest = HttpsRequestUtil.doPostJson(url, JsonUtil.toJson(params), headers);
        log.info("{} 响应成功", url);
        JSONObject resp = JSONUtil.parseObj(rest);
        String error = resp.getStr("error");
        if (error != null) {
            log.error("{} 调用失败：{}", url, error);
            return new ArrayList<>();
        }

        Integer count = resp.getInt("@odata.count");
        String value = resp.getStr("value");
        List<T> list = JsonUtil.getListType(value, clazz);
        if (CollUtil.isEmpty(list)) {
            log.info("{} 返回数据为空：{}", url, rest);
            list = new ArrayList<>();
        }

        // 如果返回数据数量达到1000且有更多数据，用线程池去获取剩余数据
        if (!isPaging && count > 1000 && list.size() == 1000) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            Queue<T> updates = new ConcurrentLinkedQueue<>();
            int totalPages = (count / 1000) + (count % 1000 == 0 ? 0 : 1);
            for (int page = 1; page < totalPages; page++){
                final int pageSkip = 1000 * page + 1;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    List<T> records = getInfoMultiThread(url, pageSkip,orderBy, clazz,true);
                    updates.addAll(records);
                }, ThreadPoolUtil.getInstance());
                futures.add(future);
            }
            // 阻塞主线程
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            list.addAll(updates);
        }
        return list;
    }

    /**
     * 鉴权信息请求头
     * <AUTHOR> yang.
     * @since 2025/4/30 9:49
     */
    private static Map<String,String> getAuthHeaders(){
        String timestamp = String.valueOf(DateUtil.current());
        String sign = generateSign(APP_ID, APP_SECRET, timestamp);
        Map<String,String> headers = new HashMap<>();
        headers.put("appId",APP_ID);
        headers.put("timestamp",timestamp);
        headers.put("sign",sign);
        return headers;
    }

    /**
     * 请求参数
     * <AUTHOR> yang.
     * @since 2025/4/30 9:49
     */
    private static Map<String,Object> getParams(Integer skip,String orderBy){
        Map<String,Object> params = new HashMap<>();
        params.put("$count",true);
        params.put("$beauty",true);
        params.put("$top",1000);
        if (StrUtil.isNotEmpty(orderBy)) {
            params.put("$orderby",orderBy);
        }
        if (skip != null){
            params.put("$skip",skip);
        }
        return params;
    }

    /**
     * 生成sign方法
     * <AUTHOR> yang.
     * @since 2025/4/29 19:32
     * @param appId 第三方应用的 appId
     * @param appSecret 第三方应用的 appSecret
     * @param timestamp 请求的时间戳
     */
    public static String generateSign(String appId, String appSecret, String timestamp) {
        // 拼接字符串：appId + appSecret + timestamp
        String rawString = appId + appSecret + timestamp;
        // MD5 加密
        String md5Result = MD5.create().digestHex(rawString);
        // Base64 编码
        String base64Result = Base64.encode(md5Result.getBytes());
        return base64Result;
    }

    public static void main(String[] args)  {
        List<MasterContactDTO> list = getMasterContactInfo(null);
        System.out.println(list.size());
        ThreadPoolUtil.shutdown();
    }

}
