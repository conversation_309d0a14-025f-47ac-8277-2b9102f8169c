package com.lanshan.base.commonservice.addressbook.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 常用标签表(CpCommonUseTag)实体
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CpCommonUseTag implements Serializable {
    private static final long serialVersionUID = -50001788288081854L;

    @ApiModelProperty("标签id，非负整型")
    @TableId
    private Long tagid;
}

