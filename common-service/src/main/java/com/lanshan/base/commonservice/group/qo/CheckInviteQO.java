package com.lanshan.base.commonservice.group.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "检查邀请群聊成员QO")
public class CheckInviteQO {

    @ApiModelProperty(value = "班级时为班级编码，课程时为课程码，部门时为部门码")
    private String code;

    @ApiModelProperty(value = "群聊类型 1 班级 2 课程 3 部门", required = true)
    private String type;

    @ApiModelProperty(value = "课程、班级-学生类型 1：本科生 2：研究生")
    private String studentType;

}
