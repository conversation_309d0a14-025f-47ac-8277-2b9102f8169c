package com.lanshan.base.commonservice.system.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.lanshan.base.api.dto.system.SysLogininforVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统访问记录(SysLogininfor)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:26:35
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@ToString
@ApiModel(value = "系统登录记录")
public class SysLogininfor extends Model<SysLogininfor> {
    private static final long serialVersionUID = 7210752862051377587L;
    /**
     * 访问ID
     */
    private Long infoId;
    /**
     * 用户账号
     */
    private String userName;
    /**
     * 登录IP地址
     */
    private String ipaddr;
    /**
     * 登录状态（0成功 1失败）
     */
    private String status;
    /**
     * 提示信息
     */
    private String msg;
    /**
     * 访问时间
     */
    private Date accessTime;

    public SysLogininfor(SysLogininforVo vo) {
        BeanUtils.copyProperties(vo, this);
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.infoId;
    }
}

