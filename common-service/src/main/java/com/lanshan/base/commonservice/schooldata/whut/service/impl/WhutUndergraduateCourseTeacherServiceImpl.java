package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.schooldata.whut.converter.WhutUndergraduateCourseTeacherConverter;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateCourseTeacherDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutMasterCourseTeacher;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateCourseTeacher;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutMasterCourseTeacherService;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutUndergraduateCourseTeacherService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 本科生课表信息-教师表(WhutUndergraduateCourseTeacher)表服务实现类
 */
@Service
public class WhutUndergraduateCourseTeacherServiceImpl extends ServiceImpl<WhutUndergraduateCourseTeacherDao, WhutUndergraduateCourseTeacher> implements WhutUndergraduateCourseTeacherService {

    @Resource
    private WhutUndergraduateCourseTeacherDao whutUndergraduateCourseTeacherDao;

    @Resource
    private UserService userService;

    @Resource
    private WhutMasterCourseTeacherService whutMasterCourseTeacherService;

    @Resource
    private RedisService redisService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void whutUndergraduateCourseTeacherSpilt() {
        //查询所有数据
        List<WhutUndergraduateCourseTeacher> list = super.list();
        //过滤掉工号为空的数据
        list.removeIf(teacher -> CharSequenceUtil.isBlank(teacher.getSkjsgh()));

        List<WhutUndergraduateCourseTeacher> entityList = new ArrayList<>();
        for (WhutUndergraduateCourseTeacher teacher : list) {
            //讲教师工号含逗号的分割
            String userid = teacher.getSkjsgh();
            if (CharSequenceUtil.isNotBlank(userid) && userid.contains(",")) {
                //工号列表
                List<String> useridList = CharSequenceUtil.split(userid, ",");
                for (String s : useridList) {
                    WhutUndergraduateCourseTeacher entity = WhutUndergraduateCourseTeacherConverter.INSTANCE.toEntity(teacher);
                    entity.setSkjsgh(s);
                    entityList.add(entity);
                }
            } else {
                entityList.add(teacher);
            }
        }

        //根据课号与工号去重
        entityList = entityList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(o -> o.getXkkh() + "-" + o.getSkjsgh()))), ArrayList::new));
        //去除工号前面所有的0
        for (WhutUndergraduateCourseTeacher entity : entityList) {
            entity.setSkjsgh(entity.getSkjsgh().replaceAll("^0+(?!$)", ""));
        }

        //填充用户名
        for (WhutUndergraduateCourseTeacher entity : entityList) {
            CpUser user = userService.getCacheUserByUserid(entity.getSkjsgh());
            if (user != null) {
                entity.setSkjsxm(user.getName());
            }
        }

        //清空表
        whutUndergraduateCourseTeacherDao.truncate();

        //插入数据
        super.saveBatch(entityList);
    }

    @Override
    public void cacheCourseTeacher() {
        // 查询本科生课程教师
        QueryWrapper<WhutUndergraduateCourseTeacher> uccQw = Wrappers.query(WhutUndergraduateCourseTeacher.class);
        uccQw.select("distinct skjsgh");
        List<String> teacherList = new ArrayList<>(super.listObjs(uccQw));

        // 查询研究生课程教师
        QueryWrapper<WhutMasterCourseTeacher> mccQw = Wrappers.query(WhutMasterCourseTeacher.class);
        mccQw.select("distinct gh");
        teacherList.addAll(whutMasterCourseTeacherService.listObjs(mccQw));

        //去除空字符串
        teacherList.removeIf(StrUtil::isBlank);
        //去重
        teacherList = teacherList.stream().distinct().collect(Collectors.toList());

        // 缓存辅导员信息
        redisService.deleteObject(CommonServiceRedisKeys.GROUP_CHAT_COURSE_TEACHER);
        redisService.setCacheList(CommonServiceRedisKeys.GROUP_CHAT_COURSE_TEACHER, teacherList);
    }
}

