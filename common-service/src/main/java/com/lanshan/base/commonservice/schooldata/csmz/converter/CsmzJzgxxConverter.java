package com.lanshan.base.commonservice.schooldata.csmz.converter;


import com.lanshan.base.commonservice.schooldata.csmz.entity.CsmzJzgxx;
import com.lanshan.base.commonservice.schooldata.csmz.vo.CsmzJzgxxVO;
import com.lanshan.base.commonservice.schooldata.csmz.vo.DataJzgxx;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教职工信息(CsmzJzgxx)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface CsmzJzgxxConverter {

    CsmzJzgxxConverter INSTANCE = Mappers.getMapper(CsmzJzgxxConverter.class);

    CsmzJzgxxVO toVO(CsmzJzgxx entity);

    CsmzJzgxx toEntity(CsmzJzgxxVO vo);

    List<CsmzJzgxxVO> toVO(List<CsmzJzgxx> entityList);

    List<CsmzJzgxx> toEntity(List<CsmzJzgxxVO> voList);

    CsmzJzgxx dataToCsmz(DataJzgxx dataJzgxx);

    List<CsmzJzgxx> dataToCsmz(List<DataJzgxx> dataJzgxxList);
}


