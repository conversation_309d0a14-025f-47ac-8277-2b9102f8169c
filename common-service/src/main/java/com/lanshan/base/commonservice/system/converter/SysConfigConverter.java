package com.lanshan.base.commonservice.system.converter;

import com.lanshan.base.api.dto.system.SysConfigVo;
import com.lanshan.base.commonservice.system.entity.SysConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 系统配置bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface SysConfigConverter {

    SysConfigConverter INSTANCE = Mappers.getMapper(SysConfigConverter.class);

    SysConfigVo toVo(SysConfig sysConfig);

    List<SysConfigVo> toVo(List<SysConfig> sysConfig);

    @Mapping(target = "params", ignore = true)
    SysConfig toEntity(SysConfigVo sysConfigVo);

    List<SysConfig> toEntity(List<SysConfigVo> sysConfigVo);

}
