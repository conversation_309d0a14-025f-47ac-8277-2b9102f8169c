package com.lanshan.base.commonservice.system.converter;

import com.lanshan.base.api.dto.system.SysRoleVo;
import com.lanshan.base.commonservice.system.entity.SysRole;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色Bean转换器
 */
@Mapper
public interface SysRoleConverter {
    SysRoleConverter INSTANCE = Mappers.getMapper(SysRoleConverter.class);

    SysRoleVo toSysRoleVo(SysRole sysRole);

    List<SysRoleVo> toSysRoleVo(List<SysRole> sysRoleList);
}
