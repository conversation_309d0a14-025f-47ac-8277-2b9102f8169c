package com.lanshan.base.commonservice.schooldata.hue.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 图书欠费信息(TQywxtxlzlTsqfxx)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "图书欠费信息VO")
@Data
@ToString
public class TQywxtxlzlTsqfxxVO implements Serializable {

    @ApiModelProperty(value = "记录经手人编码")
    private String jljsrbmdmmc;

    @ApiModelProperty(value = "处理状态码")
    private String clztmdmmc;

    @ApiModelProperty(value = "证件号")
    private String zjh;

    @ApiModelProperty(value = "财产号")
    private String cch;

    @ApiModelProperty(value = "书名")
    private String sm;

    @ApiModelProperty(value = "记录经手人编码")
    private String jljsrbm;

    @ApiModelProperty(value = "记录日期")
    private String jilrq;

    @ApiModelProperty(value = "应罚金额")
    private String yfje;

    @ApiModelProperty(value = "实罚金额")
    private String sfje;

    @ApiModelProperty(value = "处理日期")
    private String clrq;

    @ApiModelProperty(value = "处理状态码")
    private String clztm;

    @ApiModelProperty(value = "")
    private String tstamp;
}

