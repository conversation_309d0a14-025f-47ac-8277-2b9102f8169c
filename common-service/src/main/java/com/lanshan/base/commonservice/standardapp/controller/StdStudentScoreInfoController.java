package com.lanshan.base.commonservice.standardapp.controller;


import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.dto.ScoreSearchDTO;
import com.lanshan.base.commonservice.standardapp.service.StdStudentScoreInfoService;
import com.lanshan.base.commonservice.standardapp.vo.CreditInfoVO;
import com.lanshan.base.commonservice.standardapp.vo.SchoolYearTermInfoVO;
import com.lanshan.base.commonservice.standardapp.vo.StdStudentScoreInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学生成绩信息表(StudentScoreInfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("studentScoreInfo")
@Api(tags = "学生成绩信息表(StudentScoreInfo)控制层", hidden = true)
public class StdStudentScoreInfoController {
    /**
     * 服务对象
     */
    @Resource
    private StdStudentScoreInfoService stdStudentScoreInfoService;

    /**
     * 查询学分信息
     *
     * @return 学分信息
     */
    @ApiOperation("查询学生学年学期集合")
    @GetMapping("/searchCredit")
    public Result<CreditInfoVO> searchCredit() {
        return Result.build(this.stdStudentScoreInfoService.searchCredit(SecurityContextHolder.getUserId()));
    }

    /**
     * 查询成绩
     *
     * @param scoreSearchDTO 查询对象
     * @return 成绩单对象
     */
    @ApiOperation("查询学生成绩")
    @PostMapping("/searchScore")
    public Result<Map<String, List<StdStudentScoreInfoVO>>> searchScore(@RequestBody ScoreSearchDTO scoreSearchDTO) {
        return Result.build(this.stdStudentScoreInfoService.getStudentScoreList(scoreSearchDTO).stream().collect(Collectors.groupingBy(StdStudentScoreInfoVO::getCourseType, Collectors.toList())));
    }

    /**
     * 查询学生学年学期集合
     *
     * @return 学生学年学期集合
     */
    @ApiOperation("查询学生学年学期集合")
    @GetMapping("/listSchoolYearTerm")
    public Result<List<SchoolYearTermInfoVO>> listSchoolYearTerm() {
        return Result.build(this.stdStudentScoreInfoService.listSchoolYearTerm(SecurityContextHolder.getUserId()));
    }

}

