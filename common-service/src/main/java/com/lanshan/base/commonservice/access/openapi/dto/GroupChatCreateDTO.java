package com.lanshan.base.commonservice.access.openapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("创建群聊请求对象")
public class GroupChatCreateDTO {

    @ApiModelProperty(value = "群聊名，最多50个utf8字符，超过将截断", required = false)
    private String name;

    @ApiModelProperty(value = "指定群主的id。如果不指定，系统会随机从userlist中选一人作为群主", required = false)
    private String owner;

    @NotEmpty(message = "群成员列表不能为空")
    @Size(min = 2, max = 2000, message = "群成员至少2人，至多2000人")
    @ApiModelProperty(value = "群成员id列表。至少2人，至多2000人", required = true)
    private List<String> userlist;

    @ApiModelProperty(value = "群聊的唯一标志，不能与已有的群重复；字符串类型，最长32个字符。只允许字符0-9及字母a-zA-Z。如果不填，系统会随机生成群id", required = false)
    private String chatid;
}