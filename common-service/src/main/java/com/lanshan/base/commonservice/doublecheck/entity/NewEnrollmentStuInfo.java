package com.lanshan.base.commonservice.doublecheck.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


/**
 * 新生信息表(NewEnrollmentStuInfo)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class NewEnrollmentStuInfo extends Model<NewEnrollmentStuInfo> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 手机号
     */
    private String sjh;
    /**
     * 身份证号
     */
    private String sfzh;
    /**
     * 准考证号
     */
    private String zkzh;
    /**
     * 学号
     */
    private String xh;
    /**
     * 用户id
     */
    private String userid;
    /**
     * 是否将用户的 userid 修改为 用户的学号
     */
    private String changed;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

