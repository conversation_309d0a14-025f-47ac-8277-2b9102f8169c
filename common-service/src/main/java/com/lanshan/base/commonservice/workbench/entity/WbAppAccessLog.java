package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户访问应用记录表(WbAppAccessLog)表实体类
 *
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class WbAppAccessLog extends Model<WbAppAccessLog> {
    private static final long serialVersionUID = 7335633283186198150L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 访问时间
     */
    private Date createTime;
    /**
     * 用户访问地址
     */
    private String ipAddr;
    /**
     * 访问目标应用地址
     */
    private String accessUrl;

    @TableField(value = "count(*)",insertStrategy = FieldStrategy.NEVER,updateStrategy = FieldStrategy.NEVER)
    private  Long accessCount;

    /**
     * 用户点击时用户所有的标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> userTags;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

