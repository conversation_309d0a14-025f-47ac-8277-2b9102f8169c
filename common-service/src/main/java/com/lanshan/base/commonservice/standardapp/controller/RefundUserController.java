package com.lanshan.base.commonservice.standardapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.qo.RefundUserQO;
import com.lanshan.base.commonservice.standardapp.service.RefundUserService;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserDetailVO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * 退费用户控制器
 */
@RestController
@RequestMapping("/refund/user")
@Api(tags = "退费用户管理")
public class RefundUserController {

    @Autowired
    private RefundUserService refundUserService;

    @PostMapping("/list")
    @ApiOperation("获取退费用户列表")
    public Result<IPage<RefundUserVO>> getRefundUserList(@RequestBody RefundUserQO qo) {
        return Result.build(refundUserService.getRefundUserList(qo));
    }

    @PostMapping("/export")
    @ApiOperation("导出退费用户列表")
    public void export(HttpServletResponse response, @RequestBody RefundUserQO qo) {
        refundUserService.export(response, qo);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("获取退费用户详情")
    public Result<RefundUserDetailVO> getRefundUserDetail(@PathVariable Long id) {
        return Result.build(refundUserService.getRefundUserDetail(id));
    }

    @GetMapping("/myDetail")
    @ApiOperation("获取我的退费用户详情")
    public Result<RefundUserDetailVO> getMyRefundUserDetail() {
        return Result.build(refundUserService.getMyRefundUserDetail());
    }


    @PostMapping("/save")
    @ApiOperation("保存退费用户")
    public Result<Boolean> saveRefundUser(@RequestBody RefundUserVO vo) {
        return Result.build(refundUserService.saveRefundUser(vo));
    }

    @PostMapping("/update")
    @ApiOperation("更新退费用户")
    public Result<Boolean> updateRefundUser(@RequestBody RefundUserVO vo) {
        return Result.build(refundUserService.updateRefundUser(vo));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除退费用户")
    public Result<Boolean> deleteRefundUser(@PathVariable Long id) {
        return Result.build(refundUserService.deleteRefundUser(id));
    }

    @GetMapping("/template")
    @ApiOperation("下载导入模板")
    public void downloadTemplate(HttpServletResponse response) {
        refundUserService.generateImportTemplate(response);
    }

    @PostMapping("/import")
    @ApiOperation("导入退费用户数据")
    public Result<Void> importRefundUser(@RequestParam("file") MultipartFile file) {
        refundUserService.importRefundUser(file);
        return Result.build();
    }

    @GetMapping("/college/list")
    @ApiOperation("获取学院列表")
    public Result<List<String>> getCollegeList() {
        return Result.build(refundUserService.getCollegeList());
    }


}