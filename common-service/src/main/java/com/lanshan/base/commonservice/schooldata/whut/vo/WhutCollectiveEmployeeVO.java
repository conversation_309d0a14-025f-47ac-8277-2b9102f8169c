package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 *  集体所有制教职工基本信息(WhutCollectiveEmployee)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = " 集体所有制教职工基本信息VO")
@Data
@ToString
public class WhutCollectiveEmployeeVO implements Serializable{

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "企业微信用户id")
    private String qywxUserId;

    @ApiModelProperty(value = "工号")
    private String gh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "性别码")
    private String xbm;

    @ApiModelProperty(value = "性别名称")
    private String xbmc;

    @ApiModelProperty(value = "手机号")
    private String sjh;

    @ApiModelProperty(value = "身份证件类型码")
    private String sfzjlxm;

    @ApiModelProperty(value = "身份证件类型名称")
    private String sfzjlxmc;

    @ApiModelProperty(value = "身份证件号")
    private String sfzjh;

    @ApiModelProperty(value = "单位编码")
    private String dwbm;

    @ApiModelProperty(value = "单位名称")
    private String dwmc;

    @ApiModelProperty(value = "时间戳")
    private String tstamp;

    @ApiModelProperty(value = "人员状态")
    private String ryzt;

    @ApiModelProperty(value = "来校时间")
    private String lxsj;

    @ApiModelProperty(value = "退休时间")
    private String txsj;
}

