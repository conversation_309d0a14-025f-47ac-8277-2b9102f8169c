package com.lanshan.base.commonservice.identify.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.commonservice.identify.dto.ChangeCurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.CurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.IDentifyNumDto;
import com.lanshan.base.commonservice.identify.dto.IdentifyListDto;
import com.lanshan.base.commonservice.identify.entity.AppAuthEntity;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import com.lanshan.base.commonservice.identify.mapper.AppAuthMapper;
import com.lanshan.base.commonservice.identify.mapper.StaffIdentifyRelationMapper;
import com.lanshan.base.commonservice.identify.mapper.StaffMapper;
import com.lanshan.base.commonservice.identify.service.IdentifyApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: capability-platform-base
 */
@Service("identifyApiService")
public class IdentifyApiServiceImpl implements IdentifyApiService {
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private StaffIdentifyRelationMapper staffIdentifyRelationMapper;

    @Resource
    private AppAuthMapper appAuthMapper;

    @Override
    public String getLsUserId(IDentifyNumDto iDentifyNumDto) {
        checkAppId(iDentifyNumDto.getAppId());
        List<String> staffIdList = staffMapper.selectByParam(iDentifyNumDto.getIdCard(), iDentifyNumDto.getStaffNo(), iDentifyNumDto.getYktNum());
        if (CollUtil.isNotEmpty(staffIdList) && staffIdList.size() > 1) {
            throw new ServiceException("匹配到多条人员记录", 11);
        }
        if (CollUtil.isNotEmpty(staffIdList) && staffIdList.size() == 1) {
            return staffIdList.get(0);
        }
        throw new ServiceException("没有找到匹配人员",13);
    }

    private void checkAppId(String appId) {
        if(StringUtils.isEmpty(appId)){
            throw new ServiceException("请输入appid",2);
        }
        AppAuthEntity entity = appAuthMapper.selectAppByAppId(appId);
        if(entity == null || entity.getAppStatus()!=1){
            throw new ServiceException("此appId没有访问接口权限",3);
        }
    }

    @Override
    public List<StaffIdentifyRelation> getIdentifyList(IdentifyListDto identifyListDto) {
        checkAppId(identifyListDto.getAppId());
        List<StaffIdentifyRelation> result =  staffIdentifyRelationMapper.selectListByStaffId(identifyListDto.getLanshanUserId());
        if(CollUtil.isEmpty(result)){
         throw new ServiceException("没有找到此用户的身份信息",22);
        }
        return result;
    }

    @Override
    public StaffIdentifyRelation getCurrentIdentify(CurrentIdentifyDto currentIdentifyDto) {
        checkAppId(currentIdentifyDto.getAppId());
        StaffIdentifyRelation entity =  staffIdentifyRelationMapper.selectCurrentIdentify(currentIdentifyDto.getLanshanUserId());
        if(entity != null){
            return entity;
        }
        StaffIdentifyRelation srEntity =   staffIdentifyRelationMapper.selectIdentifyByActive(currentIdentifyDto.getLanshanUserId());
        if(srEntity == null ){
            throw new ServiceException("当前用户无有效身份",32);
        }
        return srEntity;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class,ServiceException.class})
    public void changeCurrentIdentify(ChangeCurrentIdentifyDto changeCurrentIdentifyDto) {
        checkAppId(changeCurrentIdentifyDto.getAppId());
        StaffIdentifyRelation entity =  staffIdentifyRelationMapper.selectCurrentIdentify(changeCurrentIdentifyDto.getLanshanUserId());
        if(entity == null){
            throw new ServiceException("当前用户无有效身份",43);
        }
        if(entity.getId() == changeCurrentIdentifyDto.getIdentifyId()){
            return;
        }
        StaffIdentifyRelation newEntity =  staffIdentifyRelationMapper.selectIdentifyByStaffIdAndIdentifyId(changeCurrentIdentifyDto.getLanshanUserId(),changeCurrentIdentifyDto.getIdentifyId());
        if(newEntity == null){
            throw new ServiceException("所选身份"+changeCurrentIdentifyDto.getIdentifyId()+"不存在，请选择其他身份",44);
        }
        entity.setDefaultIdentify(0);
        staffIdentifyRelationMapper.updateByStatus(entity);

        newEntity.setDefaultIdentify(1);
        staffIdentifyRelationMapper.updateByStatus(newEntity);
    }
}
