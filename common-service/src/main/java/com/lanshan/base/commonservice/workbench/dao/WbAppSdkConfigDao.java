package com.lanshan.base.commonservice.workbench.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.workbench.entity.WbAppSdkConfig;

/**
 * 应用悬浮球SDK配置表(WbAppSdkConfig)表数据库访问层
 *
 * <AUTHOR>
 */
public interface WbAppSdkConfigDao extends BaseMapper<WbAppSdkConfig> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WbAppSdkConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WbAppSdkConfig> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<WbAppSdkConfig> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<WbAppSdkConfig> entities);

    WbAppSdkConfig selectByAppId(@Param("appId") Long appId);
}

