package com.lanshan.base.commonservice.standardapp.converter;


import java.util.List;

import com.lanshan.base.commonservice.standardapp.entity.StdCampusInfo;
import com.lanshan.base.commonservice.standardapp.vo.StdCampusInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 校区信息(StdCampusInfo)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface StdCampusInfoConverter {

    StdCampusInfoConverter INSTANCE = Mappers.getMapper(StdCampusInfoConverter.class);

    StdCampusInfoVO toVO(StdCampusInfo entity);

    StdCampusInfo toEntity(StdCampusInfoVO vo);
    
    List<StdCampusInfoVO> toVO(List<StdCampusInfo> entityList);

    List<StdCampusInfo> toEntity(List<StdCampusInfoVO> voList);
}


