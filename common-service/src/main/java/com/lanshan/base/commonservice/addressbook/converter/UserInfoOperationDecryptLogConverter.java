package com.lanshan.base.commonservice.addressbook.converter;


import java.util.List;

import com.lanshan.base.commonservice.addressbook.entity.UserInfoOperationDecryptLog;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoOperationDecryptLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * (UserInfoOperationDecryptLog)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface UserInfoOperationDecryptLogConverter {

    UserInfoOperationDecryptLogConverter INSTANCE = Mappers.getMapper(UserInfoOperationDecryptLogConverter.class);

    UserInfoOperationDecryptLogVO toVO(UserInfoOperationDecryptLog entity);

    UserInfoOperationDecryptLog toEntity(UserInfoOperationDecryptLogVO vo);

    List<UserInfoOperationDecryptLogVO> toVO(List<UserInfoOperationDecryptLog> entityList);

    List<UserInfoOperationDecryptLog> toEntity(List<UserInfoOperationDecryptLogVO> voList);
}


