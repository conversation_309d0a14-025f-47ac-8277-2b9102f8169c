package com.lanshan.base.commonservice.standardapp.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "RefundUserQO", description = "退费用户查询条件对象")
public class RefundUserQO extends PageQo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "所属年份")
    private Integer year;

    @ApiModelProperty(value = "学号")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "学院")
    private String college;
    
    @ApiModelProperty(value = "学历层次 2本科生 3研究生")
    private String userType;
    
    @ApiModelProperty(value = "银行卡状态 true已绑定 false未绑定")
    private Boolean bankBind;

    @ApiModelProperty(value = "是否捐赠")
    private Boolean donate;
}