package com.lanshan.base.commonservice.system.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.dto.AjaxResult;
import com.lanshan.base.api.dto.system.SysRoleVo;
import com.lanshan.base.api.dto.system.SysUserRoleVo;
import com.lanshan.base.api.dto.system.SysUserVo;
import com.lanshan.base.api.page.TableDataInfo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityUtils;
import com.lanshan.base.commonservice.system.dto.SysRoleMenuDTO;
import com.lanshan.base.commonservice.system.dto.SysRoleSearchDTO;
import com.lanshan.base.commonservice.system.entity.SysDept;
import com.lanshan.base.commonservice.system.entity.SysRole;
import com.lanshan.base.commonservice.system.entity.SysUser;
import com.lanshan.base.commonservice.system.entity.SysUserRole;
import com.lanshan.base.commonservice.system.service.ISysDeptService;
import com.lanshan.base.commonservice.system.service.ISysRoleService;
import com.lanshan.base.commonservice.system.service.ISysUserService;
import com.lanshan.base.starter.log.annotation.Log;
import com.lanshan.base.starter.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")
@Api(tags = "系统角色信息控制器", hidden = true)
public class SysRoleController extends BaseController {
    @Resource
    private ISysRoleService sysRoleService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ISysDeptService sysDeptService;

    @ApiOperation("设置通讯录数据权限")
    @RequiresPermissions("system:role:edit")
    @PostMapping("/setWxDataScope/put")
    public Result<Boolean> setWxDataScope(Long roleId, Integer wxDataScope) {
        return Result.build(sysRoleService.addAddressDataScope(roleId, wxDataScope));
    }

    @ApiOperation("分页查询角色信息列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/list")
    public Result<IPage<SysRoleVo>> list(SysRoleSearchDTO searchDTO) {
        return Result.build(sysRoleService.pageRole(searchDTO));
    }

    @ApiOperation("根据角色ID获取详细信息")
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId) {
        sysRoleService.checkRoleDataScope(roleId);
        return success(sysRoleService.selectRoleById(roleId));
    }

    @ApiOperation("新增角色")
    @RequiresPermissions("system:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRoleVo roleVo) {
        SysRole role = new SysRole(roleVo);
        if (!sysRoleService.checkRoleNameUnique(role)) {
            return error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!sysRoleService.checkRoleKeyUnique(role)) {
            return error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        role.setCreateBy(SecurityUtils.getUsername());
        return toAjax(sysRoleService.insertRole(role));

    }

    @ApiOperation("修改保存角色")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/put")
    public AjaxResult edit(@Validated @RequestBody SysRoleVo roleVo) {
        SysRole role = new SysRole(roleVo);
        sysRoleService.checkRoleAllowed(role);
        sysRoleService.checkRoleDataScope(role.getRoleId());
        if (!sysRoleService.checkRoleNameUnique(role)) {
            return error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (!sysRoleService.checkRoleKeyUnique(role)) {
            return error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        role.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(sysRoleService.updateRole(role));
    }

    @ApiOperation("修改保存角色可见菜单")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("menu/put")
    public Result<Boolean> editRoleMenu(@Validated @RequestBody SysRoleMenuDTO roleMenuDTO) {
        return Result.build(sysRoleService.updateRoleMenu(roleMenuDTO));
    }


    @ApiOperation("修改保存数据权限")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/dataScope/put")
    public AjaxResult dataScope(@RequestBody SysRoleVo roleVo) {
        SysRole role = new SysRole(roleVo);
        sysRoleService.checkRoleAllowed(role);
        sysRoleService.checkRoleDataScope(role.getRoleId());
        return toAjax(sysRoleService.authDataScope(role));
    }

    @ApiOperation("角色状态修改")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus/put")
    public AjaxResult changeStatus(@RequestBody SysRoleVo roleVo) {
        SysRole role = new SysRole(roleVo);
        sysRoleService.checkRoleAllowed(role);
        sysRoleService.checkRoleDataScope(role.getRoleId());
        role.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(sysRoleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @RequiresPermissions("system:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @PostMapping("/{roleIds}/delete")
    public AjaxResult remove(@PathVariable Long[] roleIds) {
        return toAjax(sysRoleService.deleteRoleByIds(roleIds));
    }

    @ApiOperation("获取角色选择框列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/optionselect")
    public AjaxResult optionselect() {
        return success(sysRoleService.selectRoleAll());
    }

    @ApiOperation("查询已分配用户角色列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo allocatedList(SysUserVo user) {
        startPage();
        List<SysUserVo> list = sysUserService.selectAllocatedList(new SysUser(user));
        return getDataTable(list);
    }

    @ApiOperation("查询未分配用户角色列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(SysUserVo user) {
        startPage();
        List<SysUser> list = sysUserService.selectUnallocatedList(new SysUser(user));
        return getDataTable(list);
    }

    @ApiOperation("取消授权用户")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PostMapping("/authUser/cancel/put")
    public AjaxResult cancelAuthUser(@RequestBody SysUserRoleVo userRoleVo) {
        return toAjax(sysRoleService.deleteAuthUser(new SysUserRole(userRoleVo)));
    }

    @ApiOperation("批量取消授权用户")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PostMapping("/authUser/cancelAll/put")
    public AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds) {
        return toAjax(sysRoleService.deleteAuthUsers(roleId, userIds));
    }

    @ApiOperation("批量选择用户授权")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PostMapping("/authUser/selectAll/put")
    public AjaxResult selectAuthUserAll(Long roleId, Long[] userIds) {
        sysRoleService.checkRoleDataScope(roleId);
        return toAjax(sysRoleService.insertAuthUsers(roleId, userIds));
    }

    @ApiOperation("获取对应角色部门树列表")
    @RequiresPermissions("system:role:list")
    @GetMapping(value = "/deptTree/{roleId}")
    public AjaxResult deptTree(@PathVariable("roleId") Long roleId) {
        AjaxResult ajax = AjaxResult.success();

        Map<String, Object> map = new HashMap<>(4);
        map.put("checkedKeys", sysDeptService.selectDeptListByRoleId(roleId));
        map.put("deptList", sysDeptService.selectDeptTreeList(new SysDept()));
        return ajax.success(map);
    }
}
