package com.lanshan.base.commonservice.group.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

/**
 * Description: new java files header
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 18:42
 */
@ApiModel(value = "群聊成员数量Vo")
@Data
@ToString
public class GroupChatMemberNumVo {

    /**
     * 群聊自增ID
     */
    private Long id;

    /**
     * 对应群聊成员的总数（经过通讯录筛选）
     */
    private Integer num;

}
