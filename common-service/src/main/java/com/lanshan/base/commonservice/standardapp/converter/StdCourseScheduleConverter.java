package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdCourseSchedule;
import com.lanshan.base.commonservice.standardapp.vo.StdCourseScheduleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课程安排信息表(StdCourseSchedule)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdCourseScheduleConverter {

    StdCourseScheduleConverter INSTANCE = Mappers.getMapper(StdCourseScheduleConverter.class);

    StdCourseScheduleVO toVO(StdCourseSchedule entity);

    StdCourseSchedule toEntity(StdCourseScheduleVO vo);

    List<StdCourseScheduleVO> toVO(List<StdCourseSchedule> entityList);

    List<StdCourseSchedule> toEntity(List<StdCourseScheduleVO> voList);
}


