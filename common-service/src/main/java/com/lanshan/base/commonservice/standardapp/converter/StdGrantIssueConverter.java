package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdGrantIssue;
import com.lanshan.base.commonservice.standardapp.vo.StdGrantIssueVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生奖助金发放查询表(StdGrantIssue)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdGrantIssueConverter {

    StdGrantIssueConverter INSTANCE = Mappers.getMapper(StdGrantIssueConverter.class);

    StdGrantIssueVO toVO(StdGrantIssue entity);

    StdGrantIssue toEntity(StdGrantIssueVO vo);

    List<StdGrantIssueVO> toVO(List<StdGrantIssue> entityList);

    List<StdGrantIssue> toEntity(List<StdGrantIssueVO> voList);
}


