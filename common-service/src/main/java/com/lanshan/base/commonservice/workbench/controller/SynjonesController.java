package com.lanshan.base.commonservice.workbench.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.common.utils.AesUtil;
import com.lanshan.base.commonservice.task.WorkbenchTask;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import com.synjones.cop.sdk.common.multiton.GatewayMultitonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("synjones")
@Api(tags = "武汉理工新中新API", hidden = true)
@RequiredArgsConstructor
@Slf4j
public class SynjonesController {
    final private WxCpServiceFactory wxCpServiceFactory;

    final private WorkbenchTask workbenchTask;

    final private UserService userService;

    final private RedissonClient redissonClient;

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${common.agent.config.corp-id}")
    private String defaultCorpId;

    @ApiOperation("获取二维码数据")
    @GetMapping("qrdata/offline")
    public Result<JSONObject> getQrDateOffline() throws UnsupportedEncodingException, InvocationTargetException, IllegalAccessException {

        String request = String.format("{\"sno\":\"%s\"}", SecurityContextHolder.getUserId());
        String response = GatewayMultitonUtils.request(
                "2.0",
                "synjones.cop.medium.qrcode.createoffline.sno",
                "synjones.cop.medium.qrcode.createoffline.sno",
                "des",
                request,
                null,
                "targetScene");
        return Result.build(JSONObject.parseObject(response).getJSONObject("obj"));
    }

    @ApiOperation("二维码在线检查")
    @GetMapping("qrdata/check")
    public Result<JSONObject> checkQrDate(String barcode) throws UnsupportedEncodingException, InvocationTargetException, IllegalAccessException {
        String request = String.format("{\"barcode\":\"%s\"}", barcode);
        String response = GatewayMultitonUtils.request(
                "2.0",
                "synjones.cop.medium.qrcode.check",
                "synjones.cop.medium.qrcode.check",
                "des",
                request,
                null,
                "targetScene");
        return Result.build(JSONObject.parseObject(response).getJSONObject("obj"));
    }

    @ApiOperation("获取用户信息")
    @GetMapping("userinfo/v2")
    public Result<JSONObject> getUserinfoFromAddressBook() {
        return Result.build(getUserInfoFromAddressBook(SecurityContextHolder.getUserId()));
    }

    @ApiOperation("获取用户信息（从通讯录获取）")
    @GetMapping("userinfo")
    public Result<JSONObject> getUserinfo() throws UnsupportedEncodingException, InvocationTargetException, IllegalAccessException {
        return Result.build(getUserInfo(SecurityContextHolder.getUserId()));
    }

    @ApiOperation("获取用户信息by code")
    @GetMapping("userinfo/code")
    public Result<JSONObject> getUserinfo(String code) throws UnsupportedEncodingException, InvocationTargetException, IllegalAccessException {
        String userId = AesUtil.AESDecryptURL(code);
        return Result.build(getUserInfo(userId));
    }

    @ApiOperation("获取用户信息by code（从通讯录获取）")
    @GetMapping("userinfo/code/v2")
    public Result<JSONObject> getUserinfoFormAddressBook(String code) {
        String userId = AesUtil.AESDecryptURL(code);
        return Result.build(getUserInfoFromAddressBook(userId));
    }

    public JSONObject getUserInfo(String userId) throws UnsupportedEncodingException, InvocationTargetException, IllegalAccessException {
        RMapCache<String, String> map = redissonClient.getMapCache("synjones:userinfo");
        String resultStr = map.get(userId);
        if (resultStr == null || "{}".equals(resultStr)) {
            String request = String.format("{\"query_idinfo\":{\"sno\":\"%s\"}}", userId);
            String response = GatewayMultitonUtils.request(
                    "2.0",
                    "synjones.cop.combination.base",
                    "synjones.onecard.query.idinfo",
                    "des",
                    request,
                    null,
                    "targetScene");
            resultStr = JSONObject.parseObject(response).getJSONObject("obj").toString();
            if (resultStr != null) {
                map.put(userId, resultStr, 1, TimeUnit.HOURS);
            }
        }
        return JSONObject.parseObject(resultStr);
    }

    public JSONObject getUserInfoFromAddressBook(String userId) {
        RMapCache<String, String> map = redissonClient.getMapCache("synjones:userinfo");
        String resultStr = map.get(userId);
        if (resultStr == null || "{}".equals(resultStr)) {
            CpUser cpUser = userService.getCacheUserByUserid(userId);
            JSONObject jsonObject = new JSONObject();
            if (cpUser != null) {
                Pair<String, String> pair = getUserType(cpUser.getUserType());
                JSONArray array = new JSONArray();
                JSONObject user = new JSONObject();
                array.add(user);
                jsonObject.put("idinfo", array);
                user.put("sno", cpUser.getUserid());
                user.put("name", cpUser.getName());
                user.put("areaname", "武汉理工大学");
                user.put("pidclass", pair.getKey());
                user.put("pidname", pair.getValue());
            }
            resultStr = jsonObject.toString();
            if (resultStr != null) {
                map.put(userId, resultStr, 1, TimeUnit.HOURS);
            }
        }
        return JSONObject.parseObject(resultStr);
    }

    private Pair<String, String> getUserType(Integer userType) {
        if (userType == null) {
            return Pair.of("0", "外来人员");
        } else if (userType == 1) {
            return Pair.of("2", "教工");
        } else if (userType == 2) {
            return Pair.of("1", "本科生");
        } else if (userType == 3) {
            return Pair.of("1", "研究生");
        } else {
            return Pair.of("3", "外来人员");
        }
    }

    @ApiOperation("获取用户照片")
    @GetMapping("user/photo")
    public Result<JSONObject> getUserPhoto() {
        String userNo = AesUtil.AESEncrypt(SecurityContextHolder.getUserId());
        JSONObject param = new JSONObject();
        param.put("userNo", userNo);
        JSONObject jsonObject = restTemplate.postForObject("https://face.whut.edu.cn/photo/yd/getCertificatePhotoByNoBody", param, JSONObject.class);
        if (jsonObject == null) {
            return Result.build();
        }
        return Result.build(jsonObject.getJSONObject("data"));
    }

    @ApiOperation("获取用户照片by code")
    @GetMapping("user/photo/code")
    public Result<JSONObject> getUserPhoto(String code) {
        JSONObject param = new JSONObject();
        String userNo = AesUtil.AESEncrypt(AesUtil.AESDecryptURL(code));
        param.put("userNo", userNo);
        JSONObject jsonObject = restTemplate.postForObject("https://face.whut.edu.cn/photo/yd/getCertificatePhotoByNoBody", param, JSONObject.class);
        if (jsonObject == null) {
            return Result.build();
        }
        return Result.build(jsonObject.getJSONObject("data"));
    }

    @ApiOperation("更新学校新闻展示")
    @GetMapping("update/school/news")
    public Result<String> updateSchoolNews(String agentId) throws WxErrorException {
        workbenchTask.updateSchoolNews(agentId);
        return Result.build("OK");
    }

    @ApiOperation("设置用户身份码展示数据")
    @GetMapping("update/user/webview")
    public Result<String> updateUserWebView(String userId, String agentId) {
        workbenchTask.updateUserWebViewData(userId, agentId);
        return Result.build("OK");
    }

    @ApiOperation("设置用户身份码展示数据模拟用户")
    @GetMapping("update/user/webview/mock")
    public Result<String> updateUserWebView(String userId, String mockUserId, String agentId) {
        workbenchTask.updateUserWebViewData(userId, mockUserId, agentId);
        return Result.build("OK");
    }

    @ApiOperation("批量为每个用户设置身份码应用的webview展示数据")
    @PostMapping("set/user/webview/data")
    public Result<String> setUserWebView(String agentId) {
        int page = 1;
        // 分批次获取所有用户的id
        List<CpUser> userIdList;
        boolean hasNext = true;

        while (hasNext) {
            userIdList = userService.list(Page.of(page++, 1000), new LambdaQueryWrapper<CpUser>().select(CpUser::getUserid).orderByAsc(CpUser::getUserid));
            for (CpUser user : userIdList) {
                workbenchTask.updateUserWebViewData(user.getUserid(), agentId);
            }
            if (userIdList.size() < 1000) {
                hasNext = false;
            }
        }
        return Result.build("OK");
    }

    @ApiOperation("设置与应用的webview展示模板")
    @PostMapping("set/user/webview/template")
    public Result<String> setAppWebViewTemplate(@RequestBody WxCpAgentWorkBench dto) {
        WxCpService wxCpService = wxCpServiceFactory.get(defaultCorpId, String.valueOf(dto.getAgentId()));
        log.info("设置用户身份码展示数据：{}", dto);
        try {
            wxCpService.getWorkBenchService().setWorkBenchTemplate(dto);
        } catch (WxErrorException e) {
            log.error("设置用户身份码展示数据失败：{}", e.getMessage());
        }
        return Result.build("OK");
    }
}
