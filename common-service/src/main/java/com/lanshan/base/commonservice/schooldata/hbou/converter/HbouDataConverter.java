package com.lanshan.base.commonservice.schooldata.hbou.converter;

import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxBzks;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxJzg;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxXs;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxZzjg;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataBzks;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataJzgxx;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataXsxx;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataZzjgxx;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 湖北开放大学数据转换工具
 */
@Mapper
public interface HbouDataConverter {

    HbouDataConverter INSTANCE = Mappers.getMapper(HbouDataConverter.class);

    /**
     * 教职工数据转换
     */
    List<HbouQywxJzg> dataToHbouJzg(List<DataJzgxx> list);

    /**
     * 学生数据转换
     */
    List<HbouQywxXs> dataToHbouXs(List<DataXsxx> dataXsxxList);

    /**
     * 组织机构数据转换
     */
    List<HbouQywxZzjg> dataToHbouZzjg(List<DataZzjgxx> list);

    /**
     * 本专科生数据转换
     */
    List<HbouQywxBzks> dataToHbouBzks(List<DataBzks> list);
}