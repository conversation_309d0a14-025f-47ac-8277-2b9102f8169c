package com.lanshan.base.commonservice.todo.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lanshan.base.commonservice.todo.dto.TodoFlowNodeDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("待办新增流程入参")
public class TodoSaveFlowInfoQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("流水号")
    private String serialNo;

    @ApiModelProperty("提交人id")
    private String submitterId;

    @ApiModelProperty("提交人名称")
    private String submitterName;

    @ApiModelProperty("流程状态（流程待办） 0：在办中 1：已办结 2：已驳回 3：已撤销")
    private Integer flowStatus;

    @ApiModelProperty("待办名称")
    private String name;

    @ApiModelProperty("待办描述")
    private String description;

    @ApiModelProperty("提交信息")
    private ObjectNode submitInfo;

    @ApiModelProperty("节点信息")
    private List<TodoFlowNodeDto> flowNodeList;

    @ApiModelProperty("待办类型")
    private String type;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}

