package com.lanshan.base.commonservice.identify.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.identify.entity.IdentifyChangeRuleEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (IdentifyChangeRule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-28 13:52:53
 */
public interface IdentifyChangeRuleMapper extends BaseMapper<IdentifyChangeRuleEntity> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<IdentifyChangeRuleEntity> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<IdentifyChangeRuleEntity> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<IdentifyChangeRuleEntity> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<IdentifyChangeRuleEntity> entities);

    List<IdentifyChangeRuleEntity> selectByStatus(@Param("status") int status);
}

