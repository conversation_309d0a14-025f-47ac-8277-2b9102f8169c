package com.lanshan.base.commonservice.standardapp.converter;


import java.util.List;

import com.lanshan.base.commonservice.standardapp.entity.StdEmptyClassroomInfo;
import com.lanshan.base.commonservice.standardapp.vo.StdEmptyClassroomInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 空教室信息(StdEmptyClassroomInfo)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface StdEmptyClassroomInfoConverter {

    StdEmptyClassroomInfoConverter INSTANCE = Mappers.getMapper(StdEmptyClassroomInfoConverter.class);

    StdEmptyClassroomInfoVO toVO(StdEmptyClassroomInfo entity);

    StdEmptyClassroomInfo toEntity(StdEmptyClassroomInfoVO vo);
    
    List<StdEmptyClassroomInfoVO> toVO(List<StdEmptyClassroomInfo> entityList);

    List<StdEmptyClassroomInfo> toEntity(List<StdEmptyClassroomInfoVO> voList);
}


