package com.lanshan.base.commonservice.workbench.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 应用统计查询类
 */
@Data
@ApiModel(value = "应用统计查询类")
public class WbAppStatSearchDTO {

    @ApiModelProperty(value = "日期 为天时：2021-01-01，日期为月：2021-01")
    private String date;

    @ApiModelProperty(value = "类型 1：日统计 2：月统计")
    private String type;

    @ApiModelProperty(value = "应用ID")
    private Long tagId;
}
