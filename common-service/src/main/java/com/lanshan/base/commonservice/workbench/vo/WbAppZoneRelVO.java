package com.lanshan.base.commonservice.workbench.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 应用专区关联应用表(WbAppZoneRel)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "应用专区关联应用表VO")
@Data
@ToString
public class WbAppZoneRelVO implements Serializable {

    private static final long serialVersionUID = -2184470124332777605L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用专区ID. 即类型为专区的应用ID")
    private Long zoneId;

    @ApiModelProperty(value = "专区关联的应用ID")
    private Long appId;

    @ApiModelProperty(value = "排序号。从小到大排序")
    private Integer sort;
}

