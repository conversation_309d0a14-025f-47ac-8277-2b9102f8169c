package com.lanshan.base.commonservice.group.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.constant.CommonConstant;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.config.properties.GroupChatProperties;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.enums.GroupStudentTypeEnum;
import com.lanshan.base.commonservice.group.dao.MsgClassGroupChatDao;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.entity.MsgClassGroupChat;
import com.lanshan.base.commonservice.group.handler.GroupChatHandler;
import com.lanshan.base.commonservice.group.handler.GroupChatHandlerFactory;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatDetailQo;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatPageQo;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatQO;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatSaveQo;
import com.lanshan.base.commonservice.group.service.MsgClassGroupChatService;
import com.lanshan.base.commonservice.group.service.MsgGroupExportHistoryService;
import com.lanshan.base.commonservice.group.vo.CheckGroupCreateVO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatDetailVO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 班级群聊(MsgClassGroupChat)表服务实现类
 */
@Slf4j
@Service
public class MsgClassGroupChatServiceImpl extends ServiceImpl<MsgClassGroupChatDao, MsgClassGroupChat> implements MsgClassGroupChatService {

    @Resource
    private GroupChatHandlerFactory groupChatHandlerFactory;

    @Resource
    private GroupChatProperties groupChatProperties;

    @Resource
    private UserService userService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisService redisService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor poolTaskExecutor;

    @Resource
    private MsgGroupExportHistoryService msgGroupExportHistoryService;

    @Override
    public IPage<MsgClassGroupChatVO> pageClassGroupChat(MsgClassGroupChatPageQo pageQo) {
        IPage<MsgClassGroupChatVO> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        pageQo.setUserid(userId);

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //分页查询班级群聊信息
        handler.pageClassGroupChat(page, pageQo);
        return page;
    }

    @Override
    public List<MsgClassGroupChatDetailVO> classGroupChatDetail(MsgClassGroupChatDetailQo qo) {

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //获取班级群聊详情
        return handler.getMsgClassGroupChatDetailVO(qo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createClassGroupChat(MsgClassGroupChatSaveQo saveQo) throws WxErrorException {
        //获取lockKey
        String lockKey = getLockKey(saveQo);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            //获取锁
            boolean lockResult = lock.tryLock(CommonConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                throw ExceptionCodeEnum.GROUP_CHAT_CLASS_CREATE_ERROR.toServiceException();
            }

            //查询班级群是否已经建立
            LambdaQueryWrapper<MsgClassGroupChat> cgcQw = Wrappers.lambdaQuery(MsgClassGroupChat.class);
            cgcQw.eq(MsgClassGroupChat::getStudentType, saveQo.getStudentType());
            if (StringUtils.isNotBlank(saveQo.getGrade())) {
                cgcQw.eq(MsgClassGroupChat::getGrade, saveQo.getGrade());
            }
            if (StringUtils.isNotBlank(saveQo.getMajorCode())) {
                cgcQw.eq(MsgClassGroupChat::getMajorCode, saveQo.getMajorCode());
            }
            if (StringUtils.isNotBlank(saveQo.getClassCode())) {
                cgcQw.eq(MsgClassGroupChat::getClassCode, saveQo.getClassCode());
            }
            if (StringUtils.isNotBlank(saveQo.getInstituteCode())) {
                cgcQw.eq(MsgClassGroupChat::getInstituteCode, saveQo.getInstituteCode());
            }
            boolean exists = super.exists(cgcQw);
            if (exists) {
                throw ExceptionCodeEnum.GROUP_CHAT_CLASS_EXISTS.toServiceException();
            }

            //获取处理器
            GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

            //获取班级群聊成员
            List<GroupChatScope> userList = handler.listClassGroupChatUser(saveQo);

            //创建班级群聊
            handler.creatClassGroupChat(saveQo, userList);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public CheckGroupCreateVO isCounselor() {
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //检查是否可以建群（是否是辅导员）
        return handler.checkClassGroupCanCreate(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateClassGroupChatUser() {
        //查询班级群聊
        List<MsgClassGroupChat> list = super.list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新班级群聊用户
        handler.updateClassGroupChatUser(list);
    }

    @Override
    public IPage<MsgClassGroupChatVO> pageByParam(MsgClassGroupChatQO qo) {

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新班级群聊用户
        return handler.classGroupPageParam(qo);
    }

    @Override
    public void export(MsgClassGroupChatQO qo, HttpServletResponse response) {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新班级群聊用户
        handler.classGroupExport(qo, response);
    }


    private String getLockKey(MsgClassGroupChatSaveQo saveQo) {
        String lockKey = CommonServiceRedisKeys.GROUP_CHAT_CLASS_CREATE;
        //本科生建群key
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == saveQo.getStudentType()) {
            lockKey = lockKey + saveQo.getStudentType() + "-" + saveQo.getClassCode();

        } else {
            //研究生建群key
            lockKey = lockKey + saveQo.getStudentType() + "-" + saveQo.getGrade() + "-" + saveQo.getMajorCode();
        }
        return lockKey;
    }
}

