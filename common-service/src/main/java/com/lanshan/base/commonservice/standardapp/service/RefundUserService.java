package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.entity.RefundUser;
import com.lanshan.base.commonservice.standardapp.qo.RefundUserQO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserDetailVO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserVO;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 退费用户服务接口
 */
public interface RefundUserService extends IService<RefundUser> {
    
    /**
     * 根据查询条件获取退费用户列表
     *
     * @param qo 查询条件
     * @return 退费用户列表
     */
    IPage<RefundUserVO> getRefundUserList(RefundUserQO qo);

    /**
     * 导出退费用户列表
     */
    void export(HttpServletResponse response, RefundUserQO qo);

    /**
     * 保存退费用户
     *
     * @param vo 退费用户信息
     * @return 保存结果
     */
    boolean saveRefundUser(RefundUserVO vo);

    /**
     * 更新退费用户
     *
     * @param vo 退费用户信息
     * @return 更新结果
     */
    boolean updateRefundUser(RefundUserVO vo);

    /**
     * 删除退费用户
     *
     * @param id 退费用户ID
     * @return 删除结果
     */
    boolean deleteRefundUser(Long id);

    /**
     * 导入退费用户数据
     *
     * @param file 导入文件
     */
    void importRefundUser(MultipartFile file);
    
    /**
     * 生成导入模板
     *
     */
    void generateImportTemplate(HttpServletResponse response);
    
    /**
     * 获取学院列表（用于下拉框）
     * @return 学院名称列表
     */
    List<String> getCollegeList();
    
    /**
     * 获取退费用户详情，包含用户信息和相关日志
     * @param id 退费用户ID
     * @return 退费用户详情
     */
    RefundUserDetailVO getRefundUserDetail(Long id);

    /**
     * 获取当前登录用户的退费用户详情，包含用户信息和相关日志
     */
    RefundUserDetailVO getMyRefundUserDetail();
}