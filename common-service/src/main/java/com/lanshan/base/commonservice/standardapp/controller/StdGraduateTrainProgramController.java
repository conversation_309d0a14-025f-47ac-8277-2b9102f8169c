package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.converter.StdGraduateTrainProgramConverter;
import com.lanshan.base.commonservice.standardapp.entity.StdGraduateTrainProgram;
import com.lanshan.base.commonservice.standardapp.service.StdGraduateTrainProgramService;
import com.lanshan.base.commonservice.standardapp.vo.StdGraduateTrainProgramVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 研究生培养方案表(StdGraduateTrainProgram)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("stdGraduateTrainProgram")
@Api(tags = "研究生培养方案表(StdGraduateTrainProgram)控制层", hidden = true)
public class StdGraduateTrainProgramController {
    /**
     * 服务对象
     */
    @Resource
    private StdGraduateTrainProgramService stdGraduateTrainProgramService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<StdGraduateTrainProgramVO>> selectAll(Page<StdGraduateTrainProgramVO> page, StdGraduateTrainProgramVO vo) {
        QueryWrapper<StdGraduateTrainProgram> queryWrapper = new QueryWrapper<>(StdGraduateTrainProgramConverter.INSTANCE.toEntity(vo));
        IPage<StdGraduateTrainProgram> pageData = this.stdGraduateTrainProgramService.page(page.convert(StdGraduateTrainProgramConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(StdGraduateTrainProgramConverter.INSTANCE::toVO));
    }
}

