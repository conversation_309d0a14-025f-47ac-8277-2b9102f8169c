package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 研究生课表信息-教师表(WhutMasterCourseTeacher)表实体VO类
 */
@ApiModel(value = "研究生课表信息-教师表VO")
@Data
public class WhutMasterCourseTeacherVO implements Serializable{

    @ApiModelProperty(value = "课表编号")
    private String kbbh;

    @ApiModelProperty(value = "开课学年")
    private String kkxn;

    @ApiModelProperty(value = "开课学期")
    private String kkxq;

    @ApiModelProperty(value = "课程编码")
    private String kcbm;

    @ApiModelProperty(value = "课程名称")
    private String kcmc;

    @ApiModelProperty(value = "工号")
    private String gh;

    @ApiModelProperty(value = "任课教师名称")
    private String rkjsmc;
}

