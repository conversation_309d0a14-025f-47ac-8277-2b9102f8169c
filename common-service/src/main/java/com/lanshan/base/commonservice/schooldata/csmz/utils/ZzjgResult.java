package com.lanshan.base.commonservice.schooldata.csmz.utils;

import com.alibaba.fastjson2.annotation.JSONField;
import com.lanshan.base.commonservice.schooldata.csmz.vo.DataZzjg;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 组织机构返回值信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZzjgResult extends BasePageResult {

    @JSONField(name = "data")
    List<DataZzjg> dataZzjgList;
}
