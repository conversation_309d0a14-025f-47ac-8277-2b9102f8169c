package com.lanshan.base.commonservice.group.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 群聊应用设置操作VO
 */
@Data
@ApiModel(value = "群聊应用设置操作VO")
public class GroupAppSettingOperateVO implements Serializable {

    @ApiModelProperty(value = "配置key")
    private String configKey;

    @ApiModelProperty(value = "配置值（用户 id 集合）")
    private List<String> useridList;
}
