package com.lanshan.base.commonservice.schooldata.whut.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.group.vo.MsgDeptGroupChatDetailVO;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutTeacher;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 教职工基本信息(WhutTeacher)表数据库访问层
 *
 * <AUTHOR>
 */
public interface WhutTeacherDao extends BaseMapper<WhutTeacher> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutTeacher> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WhutTeacher> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutTeacher> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<WhutTeacher> entities);

    /**
     * 查询部门下的人员
     *
     * @param deptCode 部门编码
     * @return 部门下的人员
     */
    List<MsgDeptGroupChatDetailVO> listMemberByDwbm(String deptCode);

    /**
     * 根据工号查询
     *
     * @param gh 工号
     * @return WhutTeacher
     */
    WhutTeacher getByGh(String gh);

    @Update("truncate table school_data.whut_teacher")
    void truncate();
}

