package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdGroupChat;
import com.lanshan.base.commonservice.standardapp.vo.StdGroupChatVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 群聊表(StdGroupChat)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdGroupChatConverter {

    StdGroupChatConverter INSTANCE = Mappers.getMapper(StdGroupChatConverter.class);

    StdGroupChatVO toVO(StdGroupChat entity);

    StdGroupChat toEntity(StdGroupChatVO vo);

    List<StdGroupChatVO> toVO(List<StdGroupChat> entityList);

    List<StdGroupChat> toEntity(List<StdGroupChatVO> voList);
}


