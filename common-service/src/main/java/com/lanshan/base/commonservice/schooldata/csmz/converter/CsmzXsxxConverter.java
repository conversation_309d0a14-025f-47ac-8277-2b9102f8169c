package com.lanshan.base.commonservice.schooldata.csmz.converter;


import com.lanshan.base.commonservice.schooldata.csmz.entity.CsmzXsxx;
import com.lanshan.base.commonservice.schooldata.csmz.vo.CsmzXsxxVO;
import com.lanshan.base.commonservice.schooldata.csmz.vo.DataXsxx;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生信息(CsmzXsxx)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface CsmzXsxxConverter {

    CsmzXsxxConverter INSTANCE = Mappers.getMapper(CsmzXsxxConverter.class);

    CsmzXsxxVO toVO(CsmzXsxx entity);

    CsmzXsxx toEntity(CsmzXsxxVO vo);

    List<CsmzXsxxVO> toVO(List<CsmzXsxx> entityList);

    List<CsmzXsxx> toEntity(List<CsmzXsxxVO> voList);

    CsmzXsxx dataToCsmz(DataXsxx dataXsxx);

    List<CsmzXsxx> dataToCsmz(List<DataXsxx> dataXsxxList);
}


