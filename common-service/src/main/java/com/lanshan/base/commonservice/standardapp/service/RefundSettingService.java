package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.entity.RefundSetting;
import com.lanshan.base.commonservice.standardapp.qo.RefundSettingQO;
import com.lanshan.base.commonservice.standardapp.vo.RefundSettingVO;

import java.util.List;

/**
 * 退费设置服务接口
 */
public interface RefundSettingService extends IService<RefundSetting> {

    /**
     * 根据查询条件获取退费设置列表
     *
     * @param qo 查询条件
     * @return 退费设置列表
     */
    List<RefundSettingVO> getRefundSettingList(RefundSettingQO qo);

    /**
     * 保存退费设置
     *
     * @param vo 退费设置信息
     * @return 保存结果
     */
    boolean saveRefundSetting(RefundSettingVO vo);

    /**
     * 更新退费设置
     *
     * @param vo 退费设置信息
     * @return 更新结果
     */
    boolean updateRefundSetting(RefundSettingVO vo);

    /**
     * 删除退费设置
     *
     * @param id id
     * @return 删除结果
     */
    boolean deleteRefundSetting(Integer id);

    /**
     * 根据年份获取退费设置
     *
     * @param year 年份
     */
    RefundSettingVO getRefundSettingByYear(Integer year);
}