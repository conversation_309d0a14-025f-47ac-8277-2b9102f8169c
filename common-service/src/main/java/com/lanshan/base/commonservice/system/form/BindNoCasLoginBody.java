package com.lanshan.base.commonservice.system.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 绑定非CAS登录用户请求体
 */
@Data
@ApiModel(value = "绑定非CAS登录用户请求体")
public class BindNoCasLoginBody {

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String idCardNumber;
}
