package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 网络套餐信息
 * @date 2024/2/27 17:19
 */
@Data
@ApiModel(value = "网络套餐信息VO")
public class NetworkPackageInfoVO implements Serializable {

    private static final long serialVersionUID = 5343520288139264385L;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "套餐使用情况")
    private String used;

    @ApiModelProperty(value = "套餐总流量或总时长")
    private String total;

    @ApiModelProperty(value = "剩余流量或总时长")
    private String remaining;
}
