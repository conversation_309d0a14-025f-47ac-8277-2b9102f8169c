package com.lanshan.base.commonservice.access.openapi;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.qo.WxCpAgentDTO;
import com.lanshan.base.commonservice.workbench.dto.AppShowStyleDTO;
import com.lanshan.base.commonservice.workbench.service.impl.WbAppServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/open/access/workbench")
@Api(tags = "工作台开放API", hidden = true)
public class WorkbenchOpenApi extends OpenApi {

    @Resource
    private WbAppServiceImpl wbAppService;

    @ApiOperation("获取应用详情")
    @PostMapping("/app/detail/v1")
    public Result<WxCpAgent> getAppDetail() throws WxErrorException {
        WxCpAgent wxCpAgent = getAgentService().get(Integer.valueOf(getAgentId()));
        return Result.build(wxCpAgent);
    }

    @ApiOperation("设置应用详情")
    @PostMapping("/app/set-detail/v1")
    public Result<Boolean> setAppDetail(@RequestBody WxCpAgentDTO dto) throws WxErrorException {
        WxCpAgent wxCpAgent = WxCpAgent.builder()
                .agentId(Integer.valueOf(getAgentId()))
                .name(dto.getName())
                .description(dto.getDescription())
                .redirectDomain(dto.getRedirectDomain())
                .reportLocationFlag(dto.getReportLocationFlag())
                .logoMediaId(dto.getLogoMediaId())
                .homeUrl(dto.getHomeUrl())
                .isReportEnter(dto.getIsReportEnter())
                .build();
        getAgentService().set(wxCpAgent);
        return Result.build(true);
    }

    @ApiOperation("获取应用展示模板")
    @PostMapping("/app/template/get/v1")
    public Result<String> getAppTemplate() throws WxErrorException {
        String workBenchTemplate = getWorkbenchService().getWorkBenchTemplate(Long.valueOf(getAgentId()));
        return Result.build(workBenchTemplate);
    }

    @ApiOperation("设置应用展示模板")
    @PostMapping("/app/template/set/v1")
    public Result<Boolean> setAppTemplate(@RequestBody AppShowStyleDTO dto) throws WxErrorException {
        dto.setAppId(getAgentId());
        wbAppService.setAgentTemplate(dto);
        return Result.build(true);
    }

    @ApiOperation("设置应用的用户展示数据")
    @PostMapping("/app/user/data/set/v1")
    public Result<Boolean> setUserData(@RequestBody WxCpAgentWorkBench dto) throws WxErrorException {
        dto.setAgentId(Long.valueOf(getAgentId()));
        getWorkbenchService().setWorkBenchData(dto);
        return Result.build(true);
    }
}
