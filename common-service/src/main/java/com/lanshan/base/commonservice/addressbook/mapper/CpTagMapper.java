package com.lanshan.base.commonservice.addressbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.addressbook.entity.CpTag;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 标签表(Tag)数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Mapper
public interface CpTagMapper extends BaseMapper<CpTag> {

    //清空表
    @Update("TRUNCATE TABLE cp_tag")
    void truncate();

    //复制备份表的数据到标准表
    @Insert("INSERT INTO cp_tag SELECT * FROM cp_tag_bak")
    void copyBakToStandard();

    @Select("SELECT unnest(string_to_array(tag_id, ',')) AS tag_id FROM tag_auto_rule WHERE is_deleted = false")
    List<CpTag> getRuleTagIds();

    List<CpTag> selectTagsByIdentify(@Param("fClassIdentify") String fClassIdentify, @Param("sClassIdentify") String sClassIdentify);
}

