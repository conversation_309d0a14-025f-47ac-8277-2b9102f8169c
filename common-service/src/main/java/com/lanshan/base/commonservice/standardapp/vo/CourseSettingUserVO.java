package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class CourseSettingUserVO implements Serializable {

    private static final long serialVersionUID = 1554766845377894208L;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "部门名称集合")
    private List<String> deptName;
}
