package com.lanshan.base.commonservice.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.workbench.entity.WbAppTagRel;

import java.util.List;

/**
 * 应用标签关联表(WbAppTagRel)表服务接口
 *
 * <AUTHOR>
 */
public interface WbAppTagRelService extends IService<WbAppTagRel> {

    /**
     * 根据标签id查询关联的应用
     *
     * @param tagId 标签id
     * @return 应用标签关联的应用列表
     */
    List<WbAppTagRel> listByTagId(Long tagId);

    /**
     * 根据标签id列表查询关联的应用id列表
     *
     * @param tagIds 标签id列表
     * @return 应用id列表
     */
    List<Long> listAppIdsByTagIds(List<Long> tagIds);

    /**
     * 关联应用和标签
     *
     * @param appId  应用id
     * @param tagIds 标签id列表
     * @param isAll  是否全量关联
     * @return 成功返回true，失败返回false
     */
    Boolean relateAppTagList(Long appId, List<Long> tagIds, Boolean isAll);

    /**
     * 关联标签和应用
     *
     * @param tagId  标签id
     * @param appIds 应用id列表
     * @param isAll  是否全量关联
     * @return 成功返回true，失败返回false
     */
    Boolean relateTagAppList(Long tagId, List<Long> appIds, Boolean isAll);

    /**
     * 关联应用和标签
     *
     * @param appId 应用id
     * @param tagId 标签id
     * @return 成功返回true，失败返回false
     */
    Boolean relateAppTag(Long appId, Long tagId);

    /**
     * 根据应用ID查询应用标签关联
     *
     * @param appIds 应用ID列表
     * @return 应用标签关联列表
     */
    List<WbAppTagRel> selectAppTagName(List<Long> appIds);

    /**
     * 根据标签名称查询应用标签关联
     *
     * @param tagName 标签名称
     * @return 应用标签关联列表
     */
    List<WbAppTagRel> selectByTagName(String tagName);
}

