package com.lanshan.base.commonservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 待办同步任务线程池配置
 */
@Configuration
@EnableAsync
public class TodoSyncConfig {

    /**
     * 待办API调用执行器
     * 用于限制对外部系统的API调用频率
     */
    @Bean("todoApiExecutor")
    public TaskExecutor todoApiExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：较小的线程数以限制并发API调用
        executor.setCorePoolSize(3);
        // 最大线程数：较小的最大线程数防止API限流
        executor.setMaxPoolSize(5);
        // 队列容量：较大的队列容量允许任务排队而不是被拒绝
        executor.setQueueCapacity(200);
        // 线程名称前缀
        executor.setThreadNamePrefix("TodoApi-");
        // 等待任务完成再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程空闲时间：超过核心线程数的线程在空闲时的存活时间（秒）
        executor.setKeepAliveSeconds(300);
        // 拒绝策略：队列满且达到最大线程数时，使用调用者所在的线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        executor.initialize();
        return executor;
    }
} 