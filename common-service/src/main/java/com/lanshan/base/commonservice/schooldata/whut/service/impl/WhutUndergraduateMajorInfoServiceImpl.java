package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateMajorInfoDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateMajorInfo;
import com.lanshan.base.commonservice.schooldata.whut.qo.TgxjxbkszyjbxxQo;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutUndergraduateMajorInfoService;
import com.lanshan.base.commonservice.schooldata.whut.vo.ResponseSet;
import com.lanshan.base.commonservice.schooldata.whut.vo.TgxjxbkszyjbxxResponseSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 本科生专业基本信息表(WhutUndergraduateMajorInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("whutUndergraduateMajorInfoService")
public class WhutUndergraduateMajorInfoServiceImpl extends ServiceImpl<WhutUndergraduateMajorInfoDao, WhutUndergraduateMajorInfo> implements WhutUndergraduateMajorInfoService {

    @Resource
    private DrpServiceImpl drpService;

    @Resource
    private TransactionTemplate transactionTemplate;

    // 批量大小
    private static final int BATCH_SIZE = 5000;
    // 每页数据量
    private static final long PAGE_SIZE = 1500L;
    // 并发获取数据的线程数
    private static final int FETCH_THREADS = 5;

    @Override
    public void syncData() {
        long startTime = System.currentTimeMillis();
        log.info("开始同步本科生专业基本信息");

        // 获取第一页数据，主要是为了获取总页数
        TgxjxbkszyjbxxQo firstPageQo = new TgxjxbkszyjbxxQo();
        firstPageQo.setPer_page(PAGE_SIZE);
        firstPageQo.setPage(1L);
        ResponseSet<TgxjxbkszyjbxxResponseSet> firstPageResponse = drpService.tgxjxbkszyjbxx(firstPageQo);
        if (ObjectUtil.isNull(firstPageResponse)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步本科生专业基本信息失败");
        }

        TgxjxbkszyjbxxResponseSet firstPageResult = firstPageResponse.getResult();
        long maxPage = firstPageResult.getMax_page();
        log.info("本科生专业基本信息总页数: {}", maxPage);

        // 创建线程池用于并行获取数据
        ExecutorService fetchExecutor = Executors.newFixedThreadPool(FETCH_THREADS);
        List<Future<List<WhutUndergraduateMajorInfo>>> futures = new ArrayList<>();

        // 处理第一页数据
        List<WhutUndergraduateMajorInfo> firstPageData = processPageData(firstPageResult.getData());

        // 并行获取其他页数据
        for (long pageIndex = 2; pageIndex <= maxPage; pageIndex++) {
            final long currentPage = pageIndex;
            futures.add(fetchExecutor.submit(() -> fetchPageData(currentPage)));
        }

        // 收集所有页的数据
        List<WhutUndergraduateMajorInfo> allData = new ArrayList<>(firstPageData);
        for (Future<List<WhutUndergraduateMajorInfo>> future : futures) {
            try {
                List<WhutUndergraduateMajorInfo> pageData = future.get();
                if (CollUtil.isNotEmpty(pageData)) {
                    allData.addAll(pageData);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取本科生专业数据异常", e);
                Thread.currentThread().interrupt();
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("获取本科生专业数据异常");
            }
        }

        // 关闭线程池
        fetchExecutor.shutdown();
        try {
            if (!fetchExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                fetchExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            fetchExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("成功获取本科生专业数据，总数量: {}, 耗时: {}ms", allData.size(), System.currentTimeMillis() - startTime);

        // 清空表并批量保存数据
        long saveStartTime = System.currentTimeMillis();

        // 设置事务超时时间为30分钟
        transactionTemplate.setTimeout(1800); // 30分钟 = 1800秒

        // 先清空表（单独事务）
        transactionTemplate.execute(status -> {
            try {
                this.baseMapper.truncate();
                log.info("清空本科生专业表完成");
                return null;
            } catch (Exception e) {
                log.error("清空本科生专业表异常", e);
                status.setRollbackOnly();
                throw e;
            }
        });

        // 分批保存数据（每批一个事务）
        if (CollUtil.isNotEmpty(allData)) {
            int total = allData.size();
            int batchCount = (total + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

            for (int i = 0; i < batchCount; i++) {
                final int batchIndex = i;
                int finalI = i;
                transactionTemplate.execute(status -> {
                    try {
                        int fromIndex = batchIndex * BATCH_SIZE;
                        int toIndex = Math.min((batchIndex + 1) * BATCH_SIZE, total);
                        List<WhutUndergraduateMajorInfo> batch = allData.subList(fromIndex, toIndex);

                        this.saveBatch(batch, BATCH_SIZE);
                        log.info("保存本科生专业数据进度: {}/{}, 当前批次: {}", toIndex, total, finalI + 1);
                        return null;
                    } catch (Exception e) {
                        log.error("保存本科生专业数据异常，批次: {}", batchIndex + 1, e);
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            }
        }


        transactionTemplate.execute(status -> {
            try {
                // 清空表
                this.baseMapper.truncate();
                log.info("清空本科生专业表完成");

                // 分批保存数据
                if (CollUtil.isNotEmpty(allData)) {
                    int total = allData.size();
                    int batchCount = (total + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

                    for (int i = 0; i < batchCount; i++) {
                        int fromIndex = i * BATCH_SIZE;
                        int toIndex = Math.min((i + 1) * BATCH_SIZE, total);
                        List<WhutUndergraduateMajorInfo> batch = allData.subList(fromIndex, toIndex);

                        this.saveBatch(batch, BATCH_SIZE);
                    }
                }
                return null;
            } catch (Exception e) {
                log.error("保存本科生专业数据异常", e);
                status.setRollbackOnly();
                throw e;
            }
        });

        log.info("同步本科生专业基本信息完成，总耗时: {}ms, 数据保存耗时: {}ms",
                System.currentTimeMillis() - startTime, System.currentTimeMillis() - saveStartTime);
    }

    /**
     * 获取指定页的数据
     */
    private List<WhutUndergraduateMajorInfo> fetchPageData(long pageIndex) {
        try {
            TgxjxbkszyjbxxQo tgxjxbkszyjbxxQo = new TgxjxbkszyjbxxQo();
            tgxjxbkszyjbxxQo.setPer_page(PAGE_SIZE);
            tgxjxbkszyjbxxQo.setPage(pageIndex);

            ResponseSet<TgxjxbkszyjbxxResponseSet> response = drpService.tgxjxbkszyjbxx(tgxjxbkszyjbxxQo);
            if (ObjectUtil.isNull(response)) {
                log.error("获取本科生专业数据失败，页码: {}", pageIndex);
                return Collections.emptyList();
            }

            TgxjxbkszyjbxxResponseSet result = response.getResult();
            List<TgxjxbkszyjbxxResponseSet.Info> data = result.getData();
            log.info("成功获取本科生专业数据，页码: {}, 数量: {}", pageIndex, data.size());

            return processPageData(data);
        } catch (Exception e) {
            log.error("获取本科生专业数据异常，页码: {}", pageIndex, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理页面数据，转换为实体对象
     */
    private List<WhutUndergraduateMajorInfo> processPageData(List<TgxjxbkszyjbxxResponseSet.Info> data) {
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .map(info -> BeanUtil.copyProperties(info, WhutUndergraduateMajorInfo.class))
                .collect(Collectors.toList());
    }
}

