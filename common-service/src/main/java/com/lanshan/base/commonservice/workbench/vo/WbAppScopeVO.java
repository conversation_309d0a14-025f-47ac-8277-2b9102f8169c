package com.lanshan.base.commonservice.workbench.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 工作台应用可见范围信息表(WbAppScope)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "工作台应用可见范围信息表VO")
@Data
@ToString
public class WbAppScopeVO implements Serializable {

    private static final long serialVersionUID = 8521925725679572449L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "范围类型。1：用户；2：部门；3：标签")
    private Integer scopeType;

    @ApiModelProperty(value = "根据范围类型，限定不同目标ID")
    private String scopeTargetId;
}

