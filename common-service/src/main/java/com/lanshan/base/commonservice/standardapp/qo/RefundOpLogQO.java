package com.lanshan.base.commonservice.standardapp.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 退费操作日志查询对象
 */
@Data
@ApiModel(value = "RefundOpLogQO", description = "退费操作日志查询条件对象")
public class RefundOpLogQO extends PageQo implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "操作开始时间")
    private Date opTimeStart;

    @ApiModelProperty(value = "操作结束时间")
    private Date opTimeEnd;

    @ApiModelProperty(value = "操作人")
    private String opUser;

    @ApiModelProperty(value = "附件名称")
    private String fileName;

    @ApiModelProperty(value = "附件URL")
    private String fileUrl;

    @ApiModelProperty(value = "操作类型（添加银行卡、更换银行卡、捐赠支出、导入数据表等）")
    private String opType;

    @ApiModelProperty(value = "操作内容（添加中国建设银行卡 (尾号5678)、确认捐赠全部余额）")
    private String opContent;
}