package com.lanshan.base.commonservice.minio.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.minio.entity.ComMinioFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对象文件信息(ComMinioFile)表数据库访问层
 *
 * <AUTHOR>
 */
public interface ComMinioFileDao extends BaseMapper<ComMinioFile> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComMinioFile> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComMinioFile> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComMinioFile> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ComMinioFile> entities);

}

