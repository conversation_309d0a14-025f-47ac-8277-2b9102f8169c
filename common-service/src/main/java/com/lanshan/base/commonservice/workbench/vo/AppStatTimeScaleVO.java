package com.lanshan.base.commonservice.workbench.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 应用统计时间范围的数据VO
 */
@Data
@ApiModel(value = "应用统计时间范围的数据VO")
public class AppStatTimeScaleVO {

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "应用图标")
    private String logoUrl;

    @ApiModelProperty(value = "时间范围")
    private String timeScale;

    @ApiModelProperty(value = "总点击次数")
    private Integer totalUserClick;

    @ApiModelProperty(value = "学生点击次数")
    private Integer totalStuClick;

    @ApiModelProperty(value = "教师点击次数")
    private Integer totalTchClick;

    @ApiModelProperty(value = "总使用次数")
    private Integer totalUserUsed;

    @ApiModelProperty(value = "学生使用次数")
    private Integer totalStuUsed;

    @ApiModelProperty(value = "教师使用次数")
    private Integer totalTchUsed;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "本科生使用人数")
    private Integer totalStuUndergraduateUsed;

    @ApiModelProperty(value = "研究生使用人数")
    private Integer totalStuMasterUsed;

    @ApiModelProperty(value = "本科生点击次数")
    private Integer totalStuUndergraduateClick;

    @ApiModelProperty(value = "研究生点击次数")
    private Integer totalStuMasterClick;

    @ApiModelProperty(value = "其他点击次数")
    private Integer totalOtherClick;

    @ApiModelProperty(value = "其他使用人数")
    private Integer totalOtherUsed;

}
