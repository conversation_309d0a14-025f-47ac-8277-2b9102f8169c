package com.lanshan.base.commonservice.standardapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatQO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatVO;
import com.lanshan.base.commonservice.standardapp.entity.RefundUser;
import com.lanshan.base.commonservice.standardapp.qo.RefundUserQO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退费用户数据访问层
 */
@Mapper
public interface RefundUserDao extends BaseMapper<RefundUser> {
    /**
     * 获取退费用户列表（联表查询）
     * @param qo 查询条件
     * @return 退费用户列表
     */
    List<RefundUserVO> getRefundUserList(@Param("page") IPage<RefundUserVO> page, @Param("qo") RefundUserQO qo );
    
    /**
     * 获取学院列表
     * @return 学院列表
     */
    List<String> getCollegeList();
}