package com.lanshan.base.commonservice.access.openapi;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.openapi.dto.GroupChatCreateDTO;
import com.lanshan.base.commonservice.access.openapi.dto.GroupChatUpdateDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpChat;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/open/access/groupChat")
@Api(tags = "群聊开放 API", hidden = true)
public class GroupChatOpenApi extends OpenApi {

    @ApiOperation("创建群聊会话")
    @PostMapping("/create/v1")
    public Result<String> create(@RequestBody @Valid GroupChatCreateDTO dto) throws WxErrorException {
        return Result
                .build(getGroupChatService().create(dto.getName(), dto.getOwner(), dto.getUserlist(), dto.getChatid()));
    }

    @ApiOperation("修改群聊会话")
    @PostMapping("/update/v1")
    public Result<Void> update(@RequestBody @Valid GroupChatUpdateDTO dto) throws WxErrorException {
        getGroupChatService().update(dto.getChatid(), dto.getName(), dto.getOwner(), dto.getAdd_user_list(),
                dto.getDel_user_list());
        return Result.build();
    }

    @ApiOperation("获取群聊会话")
    @GetMapping("/get/v1")
    public Result<WxCpChat> create(@RequestParam String chatid) throws WxErrorException {
        return Result.build(getGroupChatService().get(chatid));
    }
}
