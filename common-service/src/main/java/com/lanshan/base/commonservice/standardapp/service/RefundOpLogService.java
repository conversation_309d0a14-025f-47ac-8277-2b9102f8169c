package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.entity.RefundOpLog;
import com.lanshan.base.commonservice.standardapp.qo.RefundOpLogQO;
import com.lanshan.base.commonservice.standardapp.vo.RefundOpLogVO;
import java.util.List;

/**
 * 退费操作日志服务接口
 */
public interface RefundOpLogService extends IService<RefundOpLog> {
    /**
     * 根据查询条件获取退费操作日志列表
     *
     * @param qo 查询条件
     * @return 退费操作日志列表
     */
    IPage<RefundOpLogVO> getRefundOpLogList(RefundOpLogQO qo);

    /**
     * 保存退费操作日志
     *
     * @param vo 退费操作日志信息
     * @return 保存结果
     */
    boolean saveRefundOpLog(RefundOpLogVO vo);

    /**
     * 更新退费操作日志
     *
     * @param vo 退费操作日志信息
     * @return 更新结果
     */
    boolean updateRefundOpLog(RefundOpLogVO vo);

    /**
     * 删除退费操作日志
     *
     * @param id 退费操作日志ID
     * @return 删除结果
     */
    boolean deleteRefundOpLog(Long id);
}