package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.converter.StdSalarInfoConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdSalarInfoDao;
import com.lanshan.base.commonservice.standardapp.entity.StdSalarInfo;
import com.lanshan.base.commonservice.standardapp.service.StdSalarInfoService;
import com.lanshan.base.commonservice.standardapp.vo.StdSalarInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 工资信息表(SalarInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdSalarInfoService")
public class StdSalarInfoServiceImpl extends ServiceImpl<StdSalarInfoDao, StdSalarInfo> implements StdSalarInfoService {

    @Override
    public StdSalarInfoVO getSalaryInfoByUserid(String month) {
        String userId = SecurityContextHolder.getUserId();
        LambdaQueryWrapper<StdSalarInfo> qw = new LambdaQueryWrapper<>();
        qw.eq(StdSalarInfo::getUserId, userId);
        if (StringUtils.isNotEmpty(month)) {
            qw.eq(StdSalarInfo::getMonth, month);
        }
        StdSalarInfo salarInfo = this.getOne(qw);
        if (ObjectUtil.isNull(salarInfo)) {
            return null;
        }
        return StdSalarInfoConverter.INSTANCE.toVO(salarInfo);
    }
}

