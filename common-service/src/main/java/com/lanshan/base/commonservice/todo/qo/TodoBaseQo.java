package com.lanshan.base.commonservice.todo.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("待办基础入参")
public class TodoBaseQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("完成待办方式 1：到时自动完成 2：点击链接完成（必须要有链接地址） 3：调用接口完成 4：手动完成")
    private List<Integer> completeMode;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("连接地址")
    private String linkUrl;

    @ApiModelProperty("连接地址-PC端")
    private String linkUrlPc;

    @ApiModelProperty("是否重复")
    private Integer isRepeat;

    @ApiModelProperty("重复结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repeatUntil;

    @ApiModelProperty("重复类型 0：每日 1：每周 2：每月")
    private Integer repeatType;

    @ApiModelProperty("重复间隔 例如：repeat_interval指定为3，repeat_type指定为每周重复，那么每3周重复一次；")
    private Integer repeatInterval;

    @ApiModelProperty("每周周几重复 取值范围：1~7，分别表示周一至周日")
    private List<Integer> repeatDayOfWeek;

    @ApiModelProperty("每月哪几天重复 取值范围：1~31，分别表示1~31号")
    private List<Integer> repeatDayOfMonth;

    @ApiModelProperty("是否提醒")
    private Integer isRemind;

    @ApiModelProperty("是否流程应用")
    private Integer isFlow;

    @ApiModelProperty("流程id")
    private Long flowId;

    @ApiModelProperty("节点id")
    private Long nodeId;

    @ApiModelProperty("日程开始前多少秒提醒 0:事件开始时, 300:事件开始前5分钟, 900:事件开始前15分钟, 3600:事件开始前1小时, 86400:事件开始前1天")
    private List<Integer> remindTimeDiffs;

    @ApiModelProperty("用户id列表")
    private List<String> useridList;
}

