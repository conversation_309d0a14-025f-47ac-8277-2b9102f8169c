package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 网络设备信息VO
 * @date 2024/2/27 17:37
 */
@Data
@ApiModel(value = "网络设备信息VO")
public class NetworkEquipmentVO implements Serializable {

    private static final long serialVersionUID = -5280063630684321729L;

    @ApiModelProperty("设备名称")
    private String equipmentName;

    @ApiModelProperty("mac 地址")
    private String macAddress;

    @ApiModelProperty("在线时间")
    private String onlineDate;

    @ApiModelProperty("是否在线")
    private String isOnline;
}
