package com.lanshan.base.commonservice.addressbook.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * (UserInfoQrcode)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "VO")
@Data
@ToString
public class UserInfoQrcodeVO implements Serializable {

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "客服名称")
    private String title;

    @ApiModelProperty(value = "图片地址")
    private String img;

    @ApiModelProperty(value = "用户类型(0：其他人员 1：教职工 2：本科生 3：研究生)")
    private String userType;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "启用")
    private Boolean enable;
}

