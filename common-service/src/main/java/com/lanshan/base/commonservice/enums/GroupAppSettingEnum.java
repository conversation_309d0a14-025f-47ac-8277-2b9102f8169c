package com.lanshan.base.commonservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 建群应用授权配置
 */
@Getter
@AllArgsConstructor
public enum GroupAppSettingEnum {

    //班级建群
    GROUP_APP_SETTING_CLASS("group.app.setting.class"),
    //课程建群
    GROUP_APP_SETTING_COURSE("group.app.setting.course"),
    //部门建群
    GROUP_APP_SETTING_DEPT("group.app.setting.dept"),
    ;

    private final String configKey;
}
