package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutInterEduCollegeStudentDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutInterEduCollegeStudent;
import com.lanshan.base.commonservice.schooldata.whut.qo.ViewEchoSierraQo;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutInterEduCollegeStudentService;
import com.lanshan.base.commonservice.schooldata.whut.vo.ResponseSet;
import com.lanshan.base.commonservice.schooldata.whut.vo.ViewEchoSierraResponseSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 国教院计划外学生信息(WhutInterEduCollegeStudent)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("whutInterEduCollegeStudentService")
public class WhutInterEduCollegeStudentServiceImpl extends ServiceImpl<WhutInterEduCollegeStudentDao, WhutInterEduCollegeStudent> implements WhutInterEduCollegeStudentService {

    @Resource
    private DrpServiceImpl drpService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public List<WhutInterEduCollegeStudent> listByCode(String classCode) {
        return this.list(Wrappers.lambdaQuery(WhutInterEduCollegeStudent.class)
                .eq(WhutInterEduCollegeStudent::getBjmc, classCode)
                .ne(WhutInterEduCollegeStudent::getXjydzkm, "6")
                .ne(WhutInterEduCollegeStudent::getXjydzkm, "8")
        );
    }

    // 批量大小
    private static final int BATCH_SIZE = 5000;
    // 每页数据量
    private static final long PAGE_SIZE = 1500L;
    // 并发获取数据的线程数
    private static final int FETCH_THREADS = 5;

    @Override
    public void syncData() {
        long startTime = System.currentTimeMillis();
        log.info("开始同步国教院计划外学生信息");

        // 获取第一页数据，主要是为了获取总页数
        ViewEchoSierraQo firstPageQo = new ViewEchoSierraQo();
        firstPageQo.setPer_page(PAGE_SIZE);
        firstPageQo.setPage(1L);
        ResponseSet<ViewEchoSierraResponseSet> firstPageResponse = drpService.viewEchoSierra(firstPageQo);
        if (ObjectUtil.isNull(firstPageResponse)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步国教院计划外学生信息失败");
        }

        ViewEchoSierraResponseSet firstPageResult = firstPageResponse.getResult();
        long maxPage = firstPageResult.getMax_page();
        log.info("国教院计划外学生信息总页数: {}", maxPage);

        // 创建线程池用于并行获取数据
        ExecutorService fetchExecutor = Executors.newFixedThreadPool(FETCH_THREADS);
        List<Future<List<WhutInterEduCollegeStudent>>> futures = new ArrayList<>();

        // 处理第一页数据
        List<WhutInterEduCollegeStudent> firstPageData = processPageData(firstPageResult.getData());

        // 并行获取其他页数据
        for (long pageIndex = 2; pageIndex <= maxPage; pageIndex++) {
            final long currentPage = pageIndex;
            futures.add(fetchExecutor.submit(() -> fetchPageData(currentPage)));
        }

        // 收集所有页的数据
        List<WhutInterEduCollegeStudent> allData = new ArrayList<>(firstPageData);
        for (Future<List<WhutInterEduCollegeStudent>> future : futures) {
            try {
                List<WhutInterEduCollegeStudent> pageData = future.get();
                if (CollUtil.isNotEmpty(pageData)) {
                    allData.addAll(pageData);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取国教院计划外学生数据异常", e);
                Thread.currentThread().interrupt();
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("获取国教院计划外学生数据异常");
            }
        }

        // 关闭线程池
        fetchExecutor.shutdown();
        try {
            if (!fetchExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                fetchExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            fetchExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("成功获取国教院计划外学生数据，总数量: {}, 耗时: {}ms", allData.size(), System.currentTimeMillis() - startTime);

        // 清空表并批量保存数据
        long saveStartTime = System.currentTimeMillis();

        // 设置事务超时时间为30分钟
        transactionTemplate.setTimeout(1800); // 30分钟 = 1800秒

        // 先清空表（单独事务）
        transactionTemplate.execute(status -> {
            try {
                this.baseMapper.truncate();
                log.info("清空国教院计划外学生表完成");
                return null;
            } catch (Exception e) {
                log.error("清空国教院计划外学生表异常", e);
                status.setRollbackOnly();
                throw e;
            }
        });

        // 分批保存数据（每批一个事务）
        if (CollUtil.isNotEmpty(allData)) {
            int total = allData.size();
            int batchCount = (total + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

            for (int i = 0; i < batchCount; i++) {
                final int batchIndex = i;
                transactionTemplate.execute(status -> {
                    try {
                        int fromIndex = batchIndex * BATCH_SIZE;
                        int toIndex = Math.min((batchIndex + 1) * BATCH_SIZE, total);
                        List<WhutInterEduCollegeStudent> batch = allData.subList(fromIndex, toIndex);

                        this.saveBatch(batch, BATCH_SIZE);
                        log.info("保存国教院计划外学生数据进度: {}/{}, 当前批次: {}", toIndex, total, batchIndex + 1);
                        return null;
                    } catch (Exception e) {
                        log.error("保存国教院计划外学生数据异常，批次: {}", batchIndex + 1, e);
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            }
        }

        log.info("同步国教院计划外学生信息完成，总耗时: {}ms, 数据保存耗时: {}ms",
                System.currentTimeMillis() - startTime, System.currentTimeMillis() - saveStartTime);
    }

    /**
     * 获取指定页的数据
     */
    private List<WhutInterEduCollegeStudent> fetchPageData(long pageIndex) {
        try {
            ViewEchoSierraQo viewEchoSierraQo = new ViewEchoSierraQo();
            viewEchoSierraQo.setPer_page(PAGE_SIZE);
            viewEchoSierraQo.setPage(pageIndex);

            ResponseSet<ViewEchoSierraResponseSet> response = drpService.viewEchoSierra(viewEchoSierraQo);
            if (ObjectUtil.isNull(response)) {
                log.error("获取国教院计划外学生数据失败，页码: {}", pageIndex);
                return Collections.emptyList();
            }

            ViewEchoSierraResponseSet result = response.getResult();
            List<ViewEchoSierraResponseSet.Info> data = result.getData();
            log.info("成功获取国教院计划外学生数据，页码: {}, 数量: {}", pageIndex, data.size());

            return processPageData(data);
        } catch (Exception e) {
            log.error("获取国教院计划外学生数据异常，页码: {}", pageIndex, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理页面数据，转换为实体对象
     */
    private List<WhutInterEduCollegeStudent> processPageData(List<ViewEchoSierraResponseSet.Info> data) {
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .map(info -> BeanUtil.copyProperties(info, WhutInterEduCollegeStudent.class))
                .collect(Collectors.toList());
    }
}

