package com.lanshan.base.commonservice.group.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 班级群聊(MsgClassGroupChat)表实体类
 */
@Data
@TableName(autoResultMap = true)
public class MsgClassGroupChat implements Serializable{

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "学院编码")
    private String instituteCode;

    @ApiModelProperty(value = "专业编码")
    private String majorCode;

    @ApiModelProperty(value = "班级编码")
    private String classCode;

    @ApiModelProperty(value = "学生类型  1：本科生 2：研究生")
    private Integer studentType;

    @ApiModelProperty(value = "群聊ID")
    private String chatId;

    @ApiModelProperty(value = "群聊名称")
    private String chatName;

    @ApiModelProperty(value = "管理员用户ID")
    private String managerUserid;

    @ApiModelProperty(value = "管理员名称")
    private String managerName;

    @ApiModelProperty(value = "加入人数")
    private Integer joinCount;

    @ApiModelProperty(value = "加入用户列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> joinUserList;

    @ApiModelProperty(value = "辅导员列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> counselorList;
}

