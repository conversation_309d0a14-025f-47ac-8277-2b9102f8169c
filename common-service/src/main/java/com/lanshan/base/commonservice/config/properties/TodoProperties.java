package com.lanshan.base.commonservice.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 待办配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "common.todo")
public class TodoProperties {

    /**
     * 处理器
     */
    private String handler;

}
