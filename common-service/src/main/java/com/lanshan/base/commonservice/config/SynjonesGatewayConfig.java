package com.lanshan.base.commonservice.config;

import com.lanshan.base.api.utils.system.SpringUtils;
import com.synjones.cop.sdk.common.annotation.RefreshGatewayAccessToken;
import com.synjones.cop.sdk.common.model.AccessTokenInfo;
import com.synjones.cop.sdk.common.model.GatewaySettings;
import com.synjones.cop.sdk.common.multiton.GatewayMultitonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;

@Data
@Configuration
@ConfigurationProperties(prefix = "synjones.gateway")
@Slf4j
public class SynjonesGatewayConfig {
    private String gatewayUrl;

    private String appId;

    private String appSecret;

    private String severPublicKey;

    private String clientPrivateKey;

    @Resource
    private RedissonClient redissonClient;

    @Bean
    @Order(1)
    protected SynjonesGatewayConfig GatewayConfigInit() {
        // 是否打印中间过程日志，包含各请求体，响应体信息
        GatewaySettings.isDebug = true;
        try {
            GatewayMultitonUtils.init(
                    gatewayUrl,
                    appId,
                    appSecret,
                    severPublicKey,
                    clientPrivateKey,
                    SynjonesGatewayConfig.class,
                    null
            );
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>初始化异常<<<<<<<<<<<<<<<<<<<", e);
        }
        return null;
    }

    /**
     * 集群部署情况 项目可自行维护Token
     * 外部更新Token时使用 调用更新
     *
     * @param accessTokenInfo token
     */
    @RefreshGatewayAccessToken
    public static void refreshAccessToken(AccessTokenInfo accessTokenInfo) {
        log.info("反射⽅=>{}", accessTokenInfo);
        if (accessTokenInfo == null || StringUtils.isEmpty(accessTokenInfo.getAccessToken())) {
            return;
        }

        RMap<String, AccessTokenInfo> map = SpringUtils.getBean(RedissonClient.class).getMap("synjones:access");
        map.put("token", accessTokenInfo);
    }

    /**
     * 获取当前 AccessToken
     *
     * @return token
     */
    public static AccessTokenInfo getAccessToken() {
        RMap<String, AccessTokenInfo> map = SpringUtils.getBean(RedissonClient.class).getMap("synjones:access");
        return map.get("token");
    }
}
