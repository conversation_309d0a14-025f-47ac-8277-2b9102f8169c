package com.lanshan.base.commonservice.task.batch.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 批量任务执行信息(BatchJobExec)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class BatchJobExec extends Model<BatchJobExec> {
    private static final long serialVersionUID = -2174821577386897485L;
    /**
     * id
     */
    private Long id;
    /**
     * 批量任务ID
     */
    private Long batchId;
    /**
     * 执行bean名称
     */
    private String execBeanName;
    /**
     * 执行参数。json格式字符串
     */
    private String execParam;
    /**
     * 计划执行时间
     */
    private Date execPlanTime;
    /**
     * 执行结束时间
     */
    private Date execEndTime;
    /**
     * 执行状态；0: 等待执行；1：执行中；2：执行结束
     */
    private Integer execStatus;
    /**
     * 执行结果
     */
    private String execResult;

    /**
     * 业务状态。0：未处理；1：处理完成
     */
    private Integer businessStatus;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

