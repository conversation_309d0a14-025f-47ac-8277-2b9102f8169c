package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程安排信息表(StdCourseSchedule)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class StdCourseSchedule extends Model<StdCourseSchedule> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 课程主键。课程编号
     */
    private String courseId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 学分
     */
    private String credit;
    /**
     * 教师ID
     */
    private String teacherId;
    /**
     * 教师名称
     */
    private String teacherName;
    /**
     * 学年. 如：2023-2024
     */
    private String teachYear;
    /**
     * 学期。1:上学期，2:下学期
     */
    private Integer term;
    /**
     * 开课日期
     */
    private Date startDate;
    /**
     * 校历周号。1代表开课第一周，以此类推;多个逗号分隔
     */
    private String weekNo;
    /**
     * 星期几或周几
     */
    private String dayOfWeek;
    /**
     * 节次。多个节次，逗号分隔，如: 1,2
     */
    private String lessonNo;
    /**
     * 授课教室名称
     */
    private String classroomName;
    /**
     * 教室ID
     */
    private String classroomId;
    /**
     * 教学地点所在校区标识
     */
    private String campusAreaId;

    /**
     * 课时
     */
    private String courseHours;

    /**
     * 考核方式
     */
    private String assessmentMethod;

    /**
     * 班级名称用英文","逗号分割
     */
    private String teachClasses;

    /**
     * 课程类型 2：本科生 3：研究生
     */
    private String courseType;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

