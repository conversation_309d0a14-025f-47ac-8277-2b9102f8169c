package com.lanshan.base.commonservice.standardapp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.standardapp.converter.StdBorrowingInfoConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdBorrowingInfoDao;
import com.lanshan.base.commonservice.standardapp.dto.BorrowingSearchDTO;
import com.lanshan.base.commonservice.standardapp.entity.StdBorrowingInfo;
import com.lanshan.base.commonservice.standardapp.service.StdBorrowingInfoService;
import com.lanshan.base.commonservice.standardapp.vo.StdBorrowingInfoVO;
import org.springframework.stereotype.Service;

/**
 * 借款信息表(StdBorrowingInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdBorrowingInfoService")
public class StdBorrowingInfoServiceImpl extends ServiceImpl<StdBorrowingInfoDao, StdBorrowingInfo> implements StdBorrowingInfoService {

    @Override
    public IPage<StdBorrowingInfoVO> pageByParam(BorrowingSearchDTO searchDTO) {
        Page<StdBorrowingInfo> page = this.page(Page.of(searchDTO.getPage(), searchDTO.getSize()), Wrappers.lambdaQuery(StdBorrowingInfo.class)
                .eq(StdBorrowingInfo::getUserId, searchDTO.getUserId())
                .orderByDesc(StdBorrowingInfo::getCreateTime)
        );
        return page.convert(StdBorrowingInfoConverter.INSTANCE::toVO);
    }
}

