package com.lanshan.base.commonservice.todo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.todo.mapper.TodoWhitelistConfigMapper;
import com.lanshan.base.commonservice.todo.entity.TodoWhitelistConfig;
import com.lanshan.base.commonservice.todo.service.TodoWhitelistConfigService;
import org.springframework.stereotype.Service;

/**
 * 白名单配置表(TodoWhitelistConfig)表服务实现类
 *
 * <AUTHOR>
 */
@Service("todoWhitelistConfigService")
public class TodoWhitelistConfigServiceImpl extends ServiceImpl<TodoWhitelistConfigMapper, TodoWhitelistConfig> implements TodoWhitelistConfigService {

}

