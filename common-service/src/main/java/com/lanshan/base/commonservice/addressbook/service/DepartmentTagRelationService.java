package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.dto.DeptTagsRelationDto;
import com.lanshan.base.commonservice.addressbook.entity.CpDepartmentTagRelation;

import java.util.List;

/**
 * 部门标签-关联表(TagDepartmentRelation)服务接口
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface DepartmentTagRelationService extends IService<CpDepartmentTagRelation> {

    /**
     * 更新部门标签列表关联关系
     * @param dto
     */
    void updateDeptTagsRelation(DeptTagsRelationDto dto);

    /**
     * 根据部门id列表查询部门标签列表关联
     * @param deptIds
     * @return
     */
    List<CpDepartmentTagRelation> listByDeptIds(List<Long> deptIds);

    /**
     * 根据标签id列表删除部门标签列表关联
     * @param idList 标签id列表
     */
    void delByTagidList(List<Long> idList);

    /**
     * 根据部门d列表删除部门标签列表关联
     * @param deptIdList 部门id列表
     */
    void delByDeptIdList(List<Long> deptIdList);

    /**
     * 根据部门id列表查询部门标签列表关联
     * @param deptIdList 部门id列表
     * @return 部门标签列表关联
     */
    List<CpDepartmentTagRelation> listTagRelationByDeptidList(List<Long> deptIdList);

    /**
     * 同步标签关联部门关系到本地标准库
     * @param tagId 标签id
     * @param partyIdlist 部门id列表（从企业微信接口获取的数据）
     */
    void syncTagDeptFromWx(Long tagId, List<Long> partyIdlist);
}
