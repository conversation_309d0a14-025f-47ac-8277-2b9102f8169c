package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbSearchHotKeyword;
import com.lanshan.base.commonservice.workbench.vo.WbSearchHotKeywordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 热门搜索关键词(WbSearchHotKeyword)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbSearchHotKeywordConverter {

    WbSearchHotKeywordConverter INSTANCE = Mappers.getMapper(WbSearchHotKeywordConverter.class);

    WbSearchHotKeywordVO toVO(WbSearchHotKeyword entity);

    WbSearchHotKeyword toEntity(WbSearchHotKeywordVO vo);

    List<WbSearchHotKeywordVO> toVO(List<WbSearchHotKeyword> entityList);

    List<WbSearchHotKeyword> toEntity(List<WbSearchHotKeywordVO> voList);
}


