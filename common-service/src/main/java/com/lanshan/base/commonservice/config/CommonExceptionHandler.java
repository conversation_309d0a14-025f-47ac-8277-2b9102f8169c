package com.lanshan.base.commonservice.config;

import cn.hutool.core.text.StrPool;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.IpUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxCpErrorMsgEnum;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class CommonExceptionHandler {

    @ExceptionHandler(SecurityException.class)
    public Result<Object> handleSecurityException(SecurityException exception) {
        log.error(exception.getMessage(), exception);
        return Result.build().setCode(ExceptionCodeEnum.UNAUTHORIZED.getCode()).setMsg(exception.getMessage());
    }

    /**
     * 参数校验异常
     * @param exception 异常
     * @return 通用返回类
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Object> handleArgumentValidException(MethodArgumentNotValidException exception) {
        log.error(exception.getMessage(), exception);
        String errMsg = exception.getBindingResult().getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(StrPool.COMMA));
        return Result.build().setCode(ExceptionCodeEnum.BASE_VALID_PARAM.getCode()).setMsg(errMsg);
    }

    @ExceptionHandler(value = MultipartException.class)
    public Result<Object> fileUploadExceptionHandler(MultipartException exception){
        Throwable rootCause = exception.getRootCause();
        if(rootCause instanceof MaxUploadSizeExceededException){
            return Result.build().setCode(ExceptionCodeEnum.FILE_SIZE_EXCEED.getCode())
                    .setMsg(ExceptionCodeEnum.FILE_SIZE_EXCEED.getMsg());
        }
        return Result.build().setCode(ExceptionCodeEnum.FILE_UPLOAD_ERROR.getCode())
                .setMsg(ExceptionCodeEnum.FILE_UPLOAD_ERROR.getMsg());
    }

    @ExceptionHandler(WxErrorException.class)
    public Result<Object> handleWxErrorException(WxErrorException exception) {
        log.error(exception.getMessage(), exception);
        if (WxCpErrorMsgEnum.CODE_60020.getCode() == exception.getError().getErrorCode()) {
            String errMsg = String.format("访问IP(%s)不在白名单中，可前往企业微信管理端-应用详情-[选择所需应用]-企业可信IP中进行配置", IpUtils.getIpInfo(exception.getMessage()));
            return Result.build().setCode(exception.getError().getErrorCode()).setMsg(errMsg);
        } else if (WxCpErrorMsgEnum.CODE_40091.getCode() == exception.getError().getErrorCode()) {
            return Result.build().setCode(exception.getError().getErrorCode()).setMsg("应用secret有误，secret在企业微信管理端-应用详情-[选择所需应用]中进行查看");
        } else if (WxCpErrorMsgEnum.CODE_40013.getCode() == exception.getError().getErrorCode()) {
            return Result.build().setCode(exception.getError().getErrorCode()).setMsg("企业ID不合法，可前往企业管理端-设置中进行查看");
        } else {
            return Result.build().setCode(exception.getError().getErrorCode()).setMsg(exception.getError().getErrorMsg());
        }
    }

    /**
     * 扩展相关的异常处理
     *
     * @param exception 异常
     * @return 通用返回类
     */
    @ExceptionHandler(ServiceException.class)
    public Result<Object> handleServiceException(ServiceException exception) {
        log.error(exception.getMessage(), exception);
        return Result.build().setCode(exception.getCode()).setMsg(exception.getMsg());
    }

    @ExceptionHandler(Exception.class)
    public Result<Object> handleException(Exception exception) {
        log.error(exception.getMessage(), exception);
        return Result.build().setCode(ExceptionCodeEnum.SYSTEM_TIMEOUT.getCode()).
                setMsg(ExceptionCodeEnum.EXECPTION.getMsg())
                .setData(exception.getMessage());
    }

}
