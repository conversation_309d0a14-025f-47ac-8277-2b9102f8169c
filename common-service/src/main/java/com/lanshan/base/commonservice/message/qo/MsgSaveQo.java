package com.lanshan.base.commonservice.message.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("消息新增入参")
public class MsgSaveQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("类型 text：纯文本 textcard：文本卡片 news：图文")
    private String type;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("链接地址")
    private String linkUrl;

    @ApiModelProperty("图片地址")
    private String imgUrl;

    @ApiModelProperty("消息来源 1：应用 2：管理员")
    private Integer source;

    @ApiModelProperty("应用id")
    private Long appId;

    @ApiModelProperty("链接应用id")
    private Long linkAppId;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("发布者")
    private String publisher;

    @ApiModelProperty("发布类型 1：立即发送 2：定时发送")
    private Integer publishType;

    @ApiModelProperty("发布渠道  WEIXIN_CORP_CHANNEL：企业微信 SMS_CHANNEL：短信 EMAIL_CHANNEL：邮箱")
    private List<String> publishChannel;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    @ApiModelProperty("用户列表")
    @Size(max = 1000, message = "用户列表不能超过1000个")
    private List<String> userList;

    @ApiModelProperty("部门列表")
    @Size(max = 100, message = "用户列表不能超过100个")
    private List<Long> departmentList;

    @ApiModelProperty("标签列表")
    @Size(max = 100, message = "用户列表不能超过100个")
    private List<Long> tagList;

    @ApiModelProperty("发送用户数量")
    private Integer sendUserCount;

    @ApiModelProperty("已读用户数量")
    private Integer readUserCount;

    @ApiModelProperty("状态 1：待发送 2：已发送 3：已撤回")
    private Integer status;

    @ApiModelProperty("是否必读 0：否 1：是")
    private Integer isMustRead;
}

