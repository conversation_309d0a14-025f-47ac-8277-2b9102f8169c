package com.lanshan.base.commonservice.group.converter;


import java.util.List;

import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.vo.GroupChatScopeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 群聊范围表(GroupChatScope)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupChatScopeConverter {

    GroupChatScopeConverter INSTANCE = Mappers.getMapper(GroupChatScopeConverter.class);

    GroupChatScopeVO toVO(GroupChatScope entity);

    GroupChatScope toEntity(GroupChatScopeVO vo);

    List<GroupChatScopeVO> toVO(List<GroupChatScope> entityList);

    List<GroupChatScope> toEntity(List<GroupChatScopeVO> voList);
}


