package com.lanshan.base.commonservice.access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.access.entity.AcDataScope;
import com.lanshan.base.commonservice.access.qo.AcDataScopeQO;

import java.util.List;

/**
 * 数据集的权限控制(AcDataScope)表服务接口
 *
 * <AUTHOR>
 */
public interface AcDataScopeService extends IService<AcDataScope> {

    /**
     * 获取数据集的权限控制
     *
     * @param qo 查询条件
     * @return 数据集的权限控制
     */
    AcDataScope getDataScope(AcDataScopeQO qo);

    /**
     * 根据应用id查询权限控制
     * @param appId 应用id
     * @return 权限控制列表
     */
    List<AcDataScope> listByAppId(Long appId);
}

