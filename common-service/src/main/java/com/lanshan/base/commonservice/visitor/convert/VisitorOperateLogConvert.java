package com.lanshan.base.commonservice.visitor.convert;

import com.lanshan.base.commonservice.visitor.entity.VisitorOperateLog;
import com.lanshan.base.commonservice.visitor.vo.VisitorOperateLogVo;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(builder = @Builder(disableBuilder = true))
public interface VisitorOperateLogConvert {

    VisitorOperateLogConvert INSTANCE = Mappers.getMapper(VisitorOperateLogConvert.class);

    VisitorOperateLogVo toVo(VisitorOperateLog entity);

    List<VisitorOperateLogVo> toVo(List<VisitorOperateLog> entityList);

}
