package com.lanshan.base.commonservice.config.rabbitmq;

/**
 * @program: capability-platform-base
 * @Description:
 * @createTime: 2025-04-23 10:25
 */
public interface MqConstant {
//    String IDENTIFY_QUEUE = "identify.queue";
//    String IDENTIFY_EXCHANGE = "identify.exchange";
    String IDENTIFY_ROUTER_KEY = "identify.routing.key";
    String IDENTIFY_CHANGE_ROUTER_KEY = "identify.change.routing.key";
}
