package com.lanshan.base.commonservice.standardapp.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.qo.RefundSettingQO;
import com.lanshan.base.commonservice.standardapp.service.RefundSettingService;
import com.lanshan.base.commonservice.standardapp.vo.RefundSettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 退费设置控制器
 */
@RestController
@RequestMapping("/refund/setting")
@Api(tags = "退费设置管理")
public class RefundSettingController {

    @Autowired
    private RefundSettingService refundSettingService;

    @PostMapping("/list")
    @ApiOperation("获取退费设置列表")
    public Result<List<RefundSettingVO>> getRefundSettingList(@RequestBody RefundSettingQO qo) {
        return Result.build(refundSettingService.getRefundSettingList(qo));
    }

    @PostMapping("/save")
    @ApiOperation("保存退费设置")
    public Result<Boolean> saveRefundSetting(@RequestBody RefundSettingVO vo) {
        return Result.build(refundSettingService.saveRefundSetting(vo));
    }

    @PostMapping("/update")
    @ApiOperation("更新退费设置")
    public Result<Boolean> updateRefundSetting(@RequestBody RefundSettingVO vo) {
        return Result.build(refundSettingService.updateRefundSetting(vo));
    }

    @DeleteMapping("/{year}")
    @ApiOperation("删除退费设置")
    public Result<Boolean> deleteRefundSetting(@PathVariable Integer year) {
        return Result.build(refundSettingService.deleteRefundSetting(year));
    }
}