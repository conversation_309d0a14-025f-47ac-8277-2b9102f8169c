package com.lanshan.base.commonservice.monitor.converter;


import com.lanshan.base.commonservice.monitor.entity.AcService;
import com.lanshan.base.commonservice.monitor.model.vo.AcServiceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用服务信息表(AcService)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper(imports = {Arrays.class, Collectors.class, Collections.class })
public interface AcServiceConverter {

    AcServiceConverter INSTANCE = Mappers.getMapper(AcServiceConverter.class);

    @Mapping(target = "contactUserList", expression = "java(!entity.getContactUser().isBlank() ? Arrays.asList(entity.getContactUser().split(\",\")) : Collections.emptyList())")
    @Mapping(target = "contactDeptList", expression = "java(!entity.getContactDept().isBlank() ? Arrays.stream(entity.getContactDept().split(\",\")).map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList())")
    @Mapping(target = "contactTagList", expression = "java(!entity.getContactTag().isBlank() ? Arrays.stream(entity.getContactTag().split(\",\")).map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList())")
    @Mapping(target = "supportTypeList", expression = "java(Arrays.asList(entity.getSupportType().split(\",\")))")
    AcServiceVO toVO(AcService entity);

    @Mapping(target = "contactUser", expression = "java(String.join(\",\",vo.getContactUserList()))")
    @Mapping(target = "contactDept", expression = "java(vo.getContactDeptList().stream().map(String::valueOf).collect(Collectors.joining(\",\")))")
    @Mapping(target = "contactTag", expression = "java(vo.getContactTagList().stream().map(String::valueOf).collect(Collectors.joining(\",\")))")
    @Mapping(target = "supportType", expression = "java(String.join(\",\",vo.getSupportTypeList()))")
    AcService toEntity(AcServiceVO vo);

    List<AcServiceVO> toVO(List<AcService> entityList);

    List<AcService> toEntity(List<AcServiceVO> voList);
}


