package com.lanshan.base.commonservice.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.file.FileInfo;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.MediaTypeEnum;
import com.lanshan.base.api.utils.file.ImageUtils;
import com.lanshan.base.api.utils.system.SpringUtils;
import com.lanshan.base.commonservice.config.manager.AsyncManager;
import com.lanshan.base.commonservice.config.manager.factory.AsyncFactory;
import com.lanshan.base.commonservice.workbench.converter.WbAppThemeConverter;
import com.lanshan.base.commonservice.workbench.converter.WbAppThemeFileConverter;
import com.lanshan.base.commonservice.workbench.dao.WbAppThemeDao;
import com.lanshan.base.commonservice.workbench.dao.WbAppThemeFileDao;
import com.lanshan.base.commonservice.workbench.dto.AppThemeDTO;
import com.lanshan.base.commonservice.workbench.dto.AppThemeSearchDTO;
import com.lanshan.base.commonservice.workbench.dto.WorkbenchThemeDTO;
import com.lanshan.base.commonservice.workbench.entity.WbApp;
import com.lanshan.base.commonservice.workbench.entity.WbAppTheme;
import com.lanshan.base.commonservice.workbench.entity.WbAppThemeFile;
import com.lanshan.base.commonservice.workbench.enums.AppTypeEnum;
import com.lanshan.base.commonservice.workbench.enums.SwitchStatusEnum;
import com.lanshan.base.commonservice.workbench.event.AppChangeEvent;
import com.lanshan.base.commonservice.workbench.service.WbAppService;
import com.lanshan.base.commonservice.workbench.service.WbAppThemeService;
import com.lanshan.base.commonservice.workbench.vo.ThemeNameListVO;
import com.lanshan.base.commonservice.workbench.vo.WbAppThemeVO;
import com.lanshan.base.starter.db.IdGenerator;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作台应用主题信息主表(WbAppTheme)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("wbAppThemeService")
public class WbAppThemeServiceImpl extends ServiceImpl<WbAppThemeDao, WbAppTheme> implements WbAppThemeService {

    @Resource
    private WbAppThemeFileDao wbAppThemeFileDao;

    @Resource
    private WbAppService wbAppService;

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @Resource
    private AppSearchServiceImpl appSearchServiceImpl;

    /**
     * 创建工作台新主题
     *
     * @param dto 工作台主题信息
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createTheme(WorkbenchThemeDTO dto) {
        //由于可以重复上传，上传后应该进行覆盖（更新）操作
        String themeName = dto.getThemeName();
        List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class).eq(WbAppThemeFile::getThemeName, themeName));
        List<WbAppThemeFile> createOrUpdatelist = new ArrayList<>();
        List<WbAppTheme> allAppThemeList = this.list();
        Map<Long, WbAppTheme> appThemeMap = allAppThemeList.stream().collect(Collectors.toMap(WbAppTheme::getId, appTheme -> appTheme, (o, n) -> n));
        Date now = new Date();
        if (CollUtil.isEmpty(wbAppThemeFiles)) {
            //上传的主题不存在，新增主题信息
            dto.getThemeImages().forEach(t -> {
                Long themeSeq = Long.valueOf(t.getOriginalName().split("\\.")[0].split("[_-]")[0].trim());
                //可能有不在应用列表的主题，不做处理
                if (Objects.nonNull(appThemeMap.get(themeSeq))) {
                    createOrUpdatelist.add(WbAppThemeFile.builder()
                            .themeName(themeName)
                            .themeSeq(themeSeq)
                            .logoFileId(t.getId())
                            .logoFilePath(t.getUrl())
                            .createTime(now)
                            .build());
                }
            });
            //导入的主题可能并非全量应用，所以需要初始化好其他应用的主题信息
            if (allAppThemeList.size() > createOrUpdatelist.size()) {
                Set<Long> themeSeqSet = createOrUpdatelist.stream().map(WbAppThemeFile::getThemeSeq).collect(Collectors.toSet());
                allAppThemeList.stream().filter(appTheme -> !themeSeqSet.contains(appTheme.getId())).forEach(appTheme -> createOrUpdatelist.add(
                        WbAppThemeFile.builder()
                                .themeName(themeName)
                                .themeSeq(appTheme.getId())
                                .createTime(now)
                                .build()
                ));
            }
            return wbAppThemeFileDao.insertBatch(createOrUpdatelist) > 0;
        } else {
            //上传的主题存在，做更新操作
            Map<Long, FileInfo> fileInfoMap = dto.getThemeImages().stream().collect(Collectors.toMap(fileInfo -> Long.valueOf(fileInfo.getOriginalName().split("[_-]")[0].trim()),
                    fileInfo -> fileInfo, (o, n) -> n));
            wbAppThemeFiles.forEach(wbAppThemeFile -> {
                FileInfo fileInfo = fileInfoMap.get(wbAppThemeFile.getThemeSeq());
                if (Objects.isNull(fileInfo)) {
                    return;
                }
                wbAppThemeFile.setLogoFilePath(fileInfo.getUrl());
                wbAppThemeFile.setLogoFileId(fileInfo.getId());
                wbAppThemeFile.setLogoMediaId("");
                wbAppThemeFile.setLogoMediaAt(null);
                wbAppThemeFile.setCreateTime(now);
                createOrUpdatelist.add(wbAppThemeFile);
            });
            List<WbAppTheme> appThemeList = this.listByIds(fileInfoMap.keySet());
            if (CollUtil.isEmpty(createOrUpdatelist) || CollUtil.isEmpty(appThemeList)) {
                throw ExceptionCodeEnum.THEME_IMPORT_ERROR.toServiceException();
            }
            boolean isUpdateThemeFile = wbAppThemeFileDao.insertOrUpdateBatch(createOrUpdatelist) > 0;
            //如果上传的主题就是当前选中的主题，同步更新企微主题
            if (CharSequenceUtil.equals(appThemeList.get(0).getThemeCurrentName(), themeName)) {
                appThemeList.forEach(appTheme -> {
                    appTheme.setSwitchStatus(SwitchStatusEnum.SWITCHING.getCode());
                    appTheme.setSwitchTime(DateUtil.date());
                });
                super.updateBatchById(appThemeList);
                asyncSubmitChangeAppTheme(createOrUpdatelist);
            }
            return isUpdateThemeFile;
        }
    }

    /**
     * 初始化应用主题
     *
     * @param wbApp 应用
     * @return Boolean
     */
    @Override
    public Boolean initAppTheme(WbApp wbApp) {

        WbAppTheme theme = this.getOne(new LambdaQueryWrapper<WbAppTheme>().eq(WbAppTheme::getAppId, wbApp.getId()));

        if (theme != null && theme.getId() != null) {
            return Boolean.FALSE;
        }
        return this.save(WbAppTheme.builder()
                .appId(wbApp.getId())
                .appName(wbApp.getAppName())
                .agentId(wbApp.getAgentId())
                .themeDefault("默认主题")
                .build());
    }

    @Override
    public void initAppTheme() {
        List<WbApp> list = wbAppService.list(Wrappers.lambdaQuery(WbApp.class).eq(WbApp::getIsDeleted, false));
        if(list.isEmpty()){
            return;
        }
        Map<Long, WbApp> appMap = list.stream().collect(Collectors.toMap(WbApp::getId, wbApp -> wbApp, (o, n) -> n));
        List<WbAppTheme> appThemeList = new ArrayList<>(list.size());
        List<WbAppThemeFile> themeFileList = new ArrayList<>(list.size());
        list.forEach(app -> appThemeList.add(WbAppTheme.builder()
                .appId(app.getId())
                .appName(app.getAppName())
                .agentId(app.getAgentId())
                .themeDefault("默认主题")
                .themeCurrentName("默认主题")
                .build()));
        super.saveBatch(appThemeList);
        appThemeList.forEach(wbAppTheme -> themeFileList.add(WbAppThemeFile.builder()
                .id(IdGenerator.generateId())
                .themeName(wbAppTheme.getThemeDefault())
                .themeSeq(wbAppTheme.getId())
                .createTime(new Date())
                .logoFilePath(appMap.get(wbAppTheme.getAppId()).getLogoUrl())
                .build()));
        wbAppThemeFileDao.insertBatch(themeFileList);
    }

    @Override
    public IPage<WbAppThemeVO> pageTheme(AppThemeSearchDTO appThemeSearchDTO) {
        Page<WbAppTheme> page = new Page<>(appThemeSearchDTO.getPage(), appThemeSearchDTO.getSize());
        IPage<WbAppTheme> pageData = this.page(page, Wrappers.lambdaQuery(WbAppTheme.class)
                .like(CharSequenceUtil.isNotBlank(appThemeSearchDTO.getAppName()), WbAppTheme::getAppName, appThemeSearchDTO.getAppName().trim())
                .orderByDesc(WbAppTheme::getId)
        );
        if (pageData.getRecords().isEmpty()) {
            return new Page<>(appThemeSearchDTO.getPage(), appThemeSearchDTO.getSize());
        }
        Set<Long> seqSet = pageData.getRecords().stream().map(WbAppTheme::getId).collect(Collectors.toSet());

        List<WbAppThemeFile> themeFileList = wbAppThemeFileDao.selectList(
                new LambdaQueryWrapper<WbAppThemeFile>()
                        .in(WbAppThemeFile::getThemeSeq, seqSet)
                        .orderByAsc(WbAppThemeFile::getThemeSeq, WbAppThemeFile::getThemeName));
        Map<Long, List<WbAppThemeFile>> themeMap = themeFileList.stream().sorted(Comparator.comparing(WbAppThemeFile::getId))
                .collect(Collectors.groupingBy(WbAppThemeFile::getThemeSeq));
        IPage<WbAppThemeVO> pageResult = pageData.convert(WbAppThemeConverter.INSTANCE::toVO);

        pageResult.getRecords().forEach(t -> {
            List<WbAppThemeFile> wbAppThemeFiles = themeMap.get(t.getId());
            if (CollectionUtils.isNotEmpty(wbAppThemeFiles)) {
                t.setThemeLogos(wbAppThemeFiles.stream().map(WbAppThemeFileConverter.INSTANCE::wbAppThemeFileToLogoInfo).collect(Collectors.toList()));
            }
        });
        return pageResult;
    }

    /**
     * 切换应用主题
     *
     * @param themeName 主题名称
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchTheme(String themeName) {

        //将指定主题的应用LOGO图片批量设置到企业微信应用
        List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.selectList(new LambdaQueryWrapper<WbAppThemeFile>()
                .eq(WbAppThemeFile::getThemeName, themeName));

        boolean isUpdate = this.update(WbAppTheme.builder()
                        .switchStatus(SwitchStatusEnum.SWITCHING.getCode())
                        .themeCurrentName(themeName)
                        .switchTime(new Date())
                        .build(),
                new LambdaQueryWrapper<>());
        //异步执行提交每个应用的LOGO图片设置
        asyncSubmitChangeAppTheme(wbAppThemeFiles);
        return isUpdate;
    }

    /**
     * 异步提交应用主题图片LOGO设置到企业微信应用
     *
     * @param wbAppThemeFiles 应用主题图片信息
     */
    private void asyncSubmitChangeAppTheme(List<WbAppThemeFile> wbAppThemeFiles) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                if (CollectionUtils.isNotEmpty(wbAppThemeFiles)) {
                    // 异步处理每一个应用的LOGO图片设置
                    wbAppThemeFiles.forEach(t -> AsyncManager.me().execute(AsyncFactory.changeAppTheme(t)));
                }
            }
        });

    }

    /**
     * 应用主题图片LOGO设置到企业微信应用
     *
     * @param appThemeFile 应用主题图片信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeAppTheme(WbAppThemeFile appThemeFile) {
        WbAppTheme theme = this.getOne(new LambdaQueryWrapper<WbAppTheme>().eq(WbAppTheme::getId, appThemeFile.getThemeSeq()));
        try {
            WbApp wbApp = wbAppService.getById(theme.getAppId());
            //对于没有设置主题图片的，跳过设置
            if (CharSequenceUtil.isNotEmpty(appThemeFile.getLogoFilePath())) {
                // 如果是企业微信应用，需要将LOGO图片设置到企业微信应用
                if (AppTypeEnum.WECHAT_CP.getCode().equals(wbApp.getAppType())) {
                    WxCpService wxCpService = wxCpServiceFactory.get(wbApp.getCorpId(), String.valueOf(wbApp.getAgentId()));
                    DateTime logoMediaAt = DateUtil.date(appThemeFile.getLogoMediaAt());
                    //没有上传过素材，或者上传过素材但超过3天，重新上传
                    if (CharSequenceUtil.isEmpty(appThemeFile.getLogoMediaId()) ||
                            (CharSequenceUtil.isNotEmpty(appThemeFile.getLogoMediaId()) && !DateUtil.isIn(DateUtil.date(), logoMediaAt, DateUtil.offsetDay(logoMediaAt, 3)))) {
                        // 上传临时素材
                        File logoFile = ImageUtils.getUrlFile(appThemeFile.getLogoFilePath());
                        WxMediaUploadResult result = wxCpService.getMediaService()
                                .upload(MediaTypeEnum.IMAGE.getValue(), logoFile);
                        FileUtil.del(logoFile);
                        appThemeFile.setLogoMediaId(result.getMediaId());
                        appThemeFile.setLogoMediaAt(DateUtil.date(result.getCreatedAt() * 1000));
                        wbAppThemeFileDao.updateById(appThemeFile);
                    }
                    // 应用LOGO图片设置到企业微信应用
                    WxCpAgent agentResult = wxCpService.getAgentService().get(wbApp.getAgentId());
                    WxCpAgent setAgent = new WxCpAgent();
                    setAgent.setAgentId(agentResult.getAgentId());
                    setAgent.setLogoMediaId(appThemeFile.getLogoMediaId());
                    setAgent.setName(agentResult.getName());
                    wxCpService.getAgentService().set(setAgent);

                    // 再次获取应用信息并保存到应用库
                    wbAppService.syncAppInfoFromWxCp(wbApp);
                } else {
                    // 其他应用只需更新应用的logoUrl即可
                    wbApp.setLogoUrl(appThemeFile.getLogoFilePath());
                    wbApp.setUpdateTime(new Date());
                    wbAppService.saveOrUpdate(wbApp);
                }
            }
            //更新完成设置更新状态
            theme.setSwitchStatus(SwitchStatusEnum.SWITCHED.getCode());
            theme.setSwitchTime(new Date());
            this.updateById(theme);
            //手动维护应用索引相关
            AsyncManager.me().execute(new TimerTask() {
                @Override
                public void run() {
                    appSearchServiceImpl.updateAppDocIndex(new AppChangeEvent(wbApp, wbApp.getId(), AppChangeEvent.ChangeType.UPDATE));
                }
            });
        } catch (Exception e) {
            log.error("应用主题图片LOGO设置到企业微信应用失败", e);
            //失败后将状态修改为替换失败
            theme.setSwitchStatus(SwitchStatusEnum.FAIL.getCode());
            theme.setSwitchTime(new Date());
            this.updateById(theme);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTheme(String themeName) {
        List<WbAppTheme> list = this.list();
        if (CollUtil.isEmpty(list)) {
            throw ExceptionCodeEnum.WX_CP_APP_THEME_EXIST.toServiceException();
        }
        List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class).eq(WbAppThemeFile::getThemeName, themeName));
        if (!wbAppThemeFiles.isEmpty()) {
            throw ExceptionCodeEnum.THEME_EXIST.toServiceException();
        }
        List<WbAppThemeFile> themeFileList = new ArrayList<>(list.size());
        list.forEach(wbAppTheme -> themeFileList.add(WbAppThemeFile.builder()
                .id(IdGenerator.generateId())
                .themeName(themeName)
                .themeSeq(wbAppTheme.getId())
                .createTime(new Date())
                .build())
        );
        return wbAppThemeFileDao.insertBatch(themeFileList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTheme(String newThemeName, String oldThemeName) {
        List<WbAppThemeFile> newThemeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class).eq(WbAppThemeFile::getThemeName, newThemeName));
        if (!newThemeFiles.isEmpty()) {
            throw ExceptionCodeEnum.THEME_EXIST.toServiceException();
        }
        List<WbAppThemeFile> oldThemeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class).eq(WbAppThemeFile::getThemeName, oldThemeName));
        oldThemeFiles.forEach(oldThemeFile -> oldThemeFile.setThemeName(newThemeName));
        return wbAppThemeFileDao.insertOrUpdateBatch(oldThemeFiles) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTheme(String themeName) {
        //不能删除默认主题
        if (CharSequenceUtil.equals("默认主题", themeName)) {
            throw ExceptionCodeEnum.CAN_NOT_DEL_DEFAULT_THEME.toServiceException();
        }
        List<WbAppThemeFile> themeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class).eq(WbAppThemeFile::getThemeName, themeName));
        if (themeFiles.isEmpty()) {
            throw ExceptionCodeEnum.THEME_NOT_EXIST.toServiceException();
        }
        List<WbAppTheme> appThemeList = this.list();
        //删除前判断是否时当前主题，如果时当前主题，将主题切换到默认主题上
        if (CharSequenceUtil.equals(themeName, appThemeList.get(0).getThemeCurrentName())) {
            appThemeList.forEach(wbAppTheme -> wbAppTheme.setThemeCurrentName(wbAppTheme.getThemeDefault()));
            super.updateBatchById(appThemeList);
        }
        return wbAppThemeFileDao.deleteBatchIds(themeFiles.stream().map(WbAppThemeFile::getId).collect(Collectors.toList())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAppTheme(AppThemeDTO appThemeDTO) {

        WbAppThemeFile wbAppThemeFile = wbAppThemeFileDao.selectOne(Wrappers.lambdaQuery(WbAppThemeFile.class)
                .eq(WbAppThemeFile::getThemeSeq, appThemeDTO.getCurrentId())
                .eq(WbAppThemeFile::getThemeName, appThemeDTO.getThemeName())
        );
        wbAppThemeFile.setLogoFileId(appThemeDTO.getFileInfo().getId());
        wbAppThemeFile.setLogoFilePath(appThemeDTO.getFileInfo().getUrl());
        //清除之前上传的主题信息
        wbAppThemeFile.setLogoMediaId("");
        wbAppThemeFile.setLogoMediaAt(null);
        boolean isUpdateTheme = wbAppThemeFileDao.updateById(wbAppThemeFile) > 0;
        WbAppTheme wbAppTheme = this.getById(appThemeDTO.getCurrentId());
        //如果修改的是当前主题，更新企业微信后台
        if (CharSequenceUtil.equals(wbAppTheme.getThemeCurrentName(), appThemeDTO.getThemeName())) {
            wbAppTheme.setSwitchStatus(SwitchStatusEnum.SWITCHING.getCode());
            wbAppTheme.setSwitchTime(DateUtil.date());
            super.updateById(wbAppTheme);
            asyncSubmitChangeAppTheme(Collections.singletonList(wbAppThemeFile));
        }
        return isUpdateTheme;
    }

    @Override
    public ThemeNameListVO getThemeNameList() {
        List<WbAppTheme> wbAppThemeList = this.list();
        if(wbAppThemeList.isEmpty()){
            return ThemeNameListVO.builder().build();
        }
        WbAppTheme appTheme = wbAppThemeList.get(0);
        List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.listThemeName();
        List<String> themeNameList = wbAppThemeFiles.stream().sorted(Comparator.comparing(WbAppThemeFile::getId)).map(WbAppThemeFile::getThemeName).collect(Collectors.toList());
        return ThemeNameListVO.builder()
                .themeCurrentName(appTheme.getThemeCurrentName())
                .themeDefault(appTheme.getThemeDefault())
                .themeNameList(themeNameList)
                .build();
    }

    @EventListener(classes = {AppChangeEvent.class})
    @Transactional(rollbackFor = Exception.class)
    public void listenerAppChanged(AppChangeEvent appChangeEvent) {
        List<Long> appIds = appChangeEvent.getAppIds();
        List<WbApp> appList = wbAppService.listByIds(appIds);
        if (CollectionUtils.isEmpty(appList)) {
            return;
        }
        WbAppThemeServiceImpl appThemeServiceImpl = SpringUtils.getBean(WbAppThemeServiceImpl.class);
        if (AppChangeEvent.ChangeType.DELETE.equals(appChangeEvent.getChangeType())) {
            // 删除应用对应主题信息
            this.remove(Wrappers.lambdaQuery(WbAppTheme.class).in(WbAppTheme::getAppId, appIds));
        } else if (AppChangeEvent.ChangeType.UPDATE.equals(appChangeEvent.getChangeType())) {
            // 更新应用对应主题信息
            appThemeServiceImpl.updateAppTheme(appIds, appList);
        } else {
            //新增应用主题信息
            appThemeServiceImpl.createAppTheme(appList);
        }
    }

    /**
     * 新增应用应用主题
     *
     * @param appList 应用集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void createAppTheme(List<WbApp> appList) {
        List<WbAppTheme> themeList = new ArrayList<>(appList.size());
        List<WbAppThemeFile> themeFileList = new ArrayList<>(appList.size());
        Map<Long, WbApp> appMap = appList.stream().collect(Collectors.toMap(WbApp::getId, wbApp -> wbApp, (o, n) -> n));
        //同步新增 app 主题记录表记录
        appList.forEach(app -> themeList.add(WbAppTheme.builder()
                .appId(app.getId())
                .appName(app.getAppName())
                .agentId(app.getAgentId())
                .themeDefault("默认主题")
                .themeCurrentName("默认主题")
                .build()));
        super.saveBatch(themeList);
        List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.selectList(Wrappers.query(WbAppThemeFile.class)
                .select("theme_name", "MIN(id) AS id")
                .orderByAsc("id")
                .groupBy("theme_name"));
        //新增 app 主题文件记录表记录
        themeList.forEach(wbAppTheme -> wbAppThemeFiles.forEach(wbAppThemeFile -> themeFileList.add(
                        //新增的 app 主题需要初始化多个已存在的主题
                        WbAppThemeFile.builder()
                                .id(IdGenerator.generateId())
                                .themeName(wbAppThemeFile.getThemeName())
                                .themeSeq(wbAppTheme.getId())
                                .createTime(new Date())
                                .logoFilePath(CharSequenceUtil.equals("默认主题", wbAppThemeFile.getThemeName()) ? appMap.get(wbAppTheme.getAppId()).getLogoUrl() : "")
                                .build())
                )
        );
        if (CollectionUtils.isNotEmpty(themeFileList)) {
            wbAppThemeFileDao.insertBatch(themeFileList);
        }
    }

    /**
     * 更新应用主题信息
     *
     * @param appIds  应用ID
     * @param appList 应用集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAppTheme(List<Long> appIds, List<WbApp> appList) {
        List<WbAppTheme> wbAppThemeList = this.list(Wrappers.lambdaQuery(WbAppTheme.class).in(WbAppTheme::getAppId, appIds));
        Map<Integer, WbApp> appMap = appList.stream().filter(wbApp -> !wbApp.getIsDeleted())
                .collect(Collectors.toMap(WbApp::getAgentId, weApp -> weApp, (o, n) -> o));
        wbAppThemeList.forEach(wbAppTheme -> {
            WbApp app = appMap.get(wbAppTheme.getAgentId());
            if (Objects.isNull(app)) {
                return;
            }
            if (!CharSequenceUtil.equals(wbAppTheme.getAppName(), app.getAppName())) {
                //更新应用主题中的应用名称
                wbAppTheme.setAppName(app.getAppName());
                super.updateById(wbAppTheme);
            }
            List<WbAppThemeFile> wbAppThemeFiles = wbAppThemeFileDao.selectList(Wrappers.lambdaQuery(WbAppThemeFile.class)
                    .eq(WbAppThemeFile::getThemeSeq, wbAppTheme.getId())
                    .eq(WbAppThemeFile::getThemeName, wbAppTheme.getThemeCurrentName()));
            if (!wbAppThemeFiles.isEmpty()) {
                //更新默认主题的 logo 图片
                wbAppThemeFiles.forEach(themeFile -> themeFile.setLogoFilePath(app.getLogoUrl()));
                wbAppThemeFileDao.insertOrUpdateBatch(wbAppThemeFiles);
            }
        });
    }
}

