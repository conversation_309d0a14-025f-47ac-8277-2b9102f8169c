package com.lanshan.base.commonservice.message.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.whut.sms.SmsServicePortType;
import com.lanshan.base.commonservice.schooldata.whut.sms.WhutSmsServiceFactory;
import com.lanshan.base.commonservice.schooldata.whut.sms.model.SmsSendQo;
import com.lanshan.base.commonservice.schooldata.whut.sms.model.SmsSentResponseSet;
import com.lanshan.base.commonservice.schooldata.whut.sms.model.WhutSmsTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/4/22 20:08
 */
@RequiredArgsConstructor
@Component("whutPhoneCodeSend")
@Slf4j
public class WhutPhoneCodeSend implements SmsPhoneCodeSend {

    private final WhutSmsServiceFactory whiteSmsServiceFactory;

    @Override
    public void sendPhoneCode(String phone, String code) {
        SmsServicePortType smsServicePortType = whiteSmsServiceFactory.getSmsServicePortType();
        SmsSendQo smsSendQo = new SmsSendQo();
        smsSendQo.setPerson_info("||||" + phone);
        smsSendQo.setSms_info(StrUtil.format(WhutSmsTemplate.BIND_TEMPLATE, code));
        smsSendQo.setSend_time(DateUtil.now());
        String requestStr = JSON.toJSONString(smsSendQo);
        log.info("武汉理工发送短信验证码，请求参数：{}", requestStr);
        String respStr = smsServicePortType.saveSmsInfo(requestStr);
        log.info("武汉理工发送短信验证码，响应结果：{}", respStr);
        SmsSentResponseSet smsSentResponseSet = JSON.parseObject(respStr, SmsSentResponseSet.class);
        if (!smsSentResponseSet.isResult()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("短信发送失败" + smsSentResponseSet.getMsg());
        }
    }

    @Override
    public void sendPhoneCode(String phone, String code, String userId) {
        this.sendPhoneCode(phone, code);
    }
}
