
package com.lanshan.base.commonservice.schooldata.hbou.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class HbouQywxBzks extends Model<HbouQywxBzks> {

    /**
     * 学号
     */
    @TableId
    private String xh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xbmc;

    /**
     * 专业编码
     */
    private String zybm;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级编码
     */
    private String bjbm;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 年级
     */
    private String nj;

    /**
     * 学院编码
     */
    private String xybh;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 校区编码
     */
    private String xqbm;

    /**
     * 校区名称
     */
    private String xqmc;

    /**
     * 学生类别
     */
    private String xslb;

    /**
     * 学生当前状态
     */
    private String xsdqzt;

    /**
     * 入学年月
     */
    private String rxny;
}