package com.lanshan.base.commonservice.group.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;

/**
 * 群聊成员操作表(GroupChatOperation)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "群聊成员操作表VO")
@Data
@ToString
public class GroupChatOperationVO implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "群聊唯一ID")
    private String chatid;

    @ApiModelProperty(value = "操作类型(1:添加成员 2:移除成员)")
    private Integer type;

    @ApiModelProperty(value = "群聊操作内容")
    private String content;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;
}

