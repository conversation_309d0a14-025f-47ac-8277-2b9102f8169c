package com.lanshan.base.commonservice.group.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.constant.CommonConstant;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.config.properties.GroupChatProperties;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.group.dao.MsgDeptGroupChatDao;
import com.lanshan.base.commonservice.group.entity.MsgDeptGroupChat;
import com.lanshan.base.commonservice.group.handler.GroupChatHandler;
import com.lanshan.base.commonservice.group.handler.GroupChatHandlerFactory;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.service.MsgDeptGroupChatService;
import com.lanshan.base.commonservice.group.service.MsgGroupExportHistoryService;
import com.lanshan.base.commonservice.group.vo.CheckGroupCreateVO;
import com.lanshan.base.commonservice.group.vo.GroupCommonInfoVO;
import com.lanshan.base.commonservice.group.vo.MsgDeptGroupChatDetailVO;
import com.lanshan.base.commonservice.group.vo.MsgDeptGroupChatVO;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 部门群聊(MsgDeptGroupChat)表服务实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service("msgDeptGroupChatService")
public class MsgDeptGroupChatServiceImpl extends ServiceImpl<MsgDeptGroupChatDao, MsgDeptGroupChat> implements MsgDeptGroupChatService {

    private final GroupChatHandlerFactory groupChatHandlerFactory;

    private final GroupChatProperties groupChatProperties;

    private final RedisService redisService;

    private final RedissonClient redissonClient;

    private final ISysConfigService sysConfigService;

    private final MsgGroupExportHistoryService msgGroupExportHistoryService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor poolTaskExecutor;

    @Override
    public CheckGroupCreateVO checkCanCreate() {
        String userId = SecurityContextHolder.getUserId();

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());
        return handler.checkCanCreate(userId);
    }

    @Override
    public List<MsgDeptGroupChatVO> listGroupChat() {
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //查询部门群聊信息
        return handler.listDeptGroupChat(userId);
    }

    @Override
    public List<MsgDeptGroupChatDetailVO> deptGroupChatDetail(String deptCode) {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //获取部门群详情
        return handler.listMsgDeptGroupChatDetail(deptCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createDeptGroupChat(MsgDeptGroupChatSaveQo saveQo) throws WxErrorException {
        //获取lockKey
        String lockKey = CommonServiceRedisKeys.GROUP_CHAT_DEPT_CREATE + saveQo.getDeptCode();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            //获取锁
            boolean lockResult = lock.tryLock(CommonConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                throw ExceptionCodeEnum.GROUP_CHAT_CLASS_CREATE_ERROR.toServiceException();
            }

            //查询部门群是否已经建立
            boolean exists = this.exists(Wrappers.lambdaQuery(MsgDeptGroupChat.class).eq(MsgDeptGroupChat::getDeptCode, saveQo.getDeptCode()));
            if (exists) {
                throw ExceptionCodeEnum.GROUP_CHAT_CLASS_EXISTS.toServiceException();
            }

            //获取处理器
            GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

            //创建班级群聊
            handler.creatDeptGroupChat(saveQo);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void updateDeptGroupChatUser() {
        //查询课程群聊
        List<MsgDeptGroupChat> list = super.list();

        if (CollUtil.isEmpty(list)) {
            return;
        }

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新部门群聊用户
        handler.updateDeptGroupChatUser(list);
    }

    @Override
    public Boolean updateGroupChatName(UpdateMsgGroupChatNameQO qo) throws WxErrorException {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新部门群聊用户
        return handler.updateGroupChatName(qo);
    }

    @Override
    public void inviteGroupUser(InviteGroupUserQO qo) {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新邀请群聊用户
        handler.inviteGroupUser(qo);

        //缓存数据，默认一天只能点击一次
        DateTime current = DateUtil.date();
        DateTime endOfDay = DateUtil.endOfDay(current);
        long between = DateUtil.between(endOfDay, current, DateUnit.SECOND, true);
        redisService.setCacheObject(CommonServiceRedisKeys.getGroupChatCanInviteKey(qo.getType().toString(), null, qo.getCode()), 1, between, TimeUnit.SECONDS);
    }

    @Override
    public GroupCommonInfoVO syncGroupChat(SyncGroupChatQO qo) throws WxErrorException {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新邀请群聊用户
        return handler.syncGroupChat(qo);
    }

    @Override
    public IPage<MsgDeptGroupChatVO> pageByParam(MsgDeptGroupChatQO qo) {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新邀请群聊用户
        return handler.deptPageGroupChat(qo);
    }

    @Override
    public void export(MsgDeptGroupChatQO qo, HttpServletResponse response) {

        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新邀请群聊用户
        handler.deptGroupChatExport(qo, response);
    }

    @Override
    public Boolean checkInvite(CheckInviteQO qo) {
        return !redisService.hasKey(CommonServiceRedisKeys.getGroupChatCanInviteKey(qo.getType(), qo.getStudentType(), qo.getCode()));
    }

    @Override
    public GroupCommonInfoVO getGroupCommonInfo(GetGroupCommonInfoQO qo) {
        //获取处理器
        GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());

        //更新邀请群聊用户
        return handler.getGroupCommonInfo(qo);
    }
}

