package com.lanshan.base.commonservice.todo.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 个人应用待办统计(TodoUserAppStat)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "个人应用待办统计VO")
@Data
@ToString
public class TodoUserAppStatVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户 id")
    private String userId;

    @ApiModelProperty(value = "今天已完成待办数")
    private Integer todayCompleted;

    @ApiModelProperty(value = "今年已完成待办数量")
    private Integer currentYearCompleted;

    @ApiModelProperty(value = "总已完成代办数量")
    private Integer countCompleted;

    @ApiModelProperty(value = "今日创建待办")
    private Integer todayCreateTodo;

    @ApiModelProperty(value = "今年创建待办数")
    private Integer currentYearCreateTodo;

    @ApiModelProperty(value = "总创建待办数")
    private Integer totalCreateTodo;

    @ApiModelProperty(value = "24 小时前每小时已完成的待办")
    private Map<String, Integer> hourAgoCompleted;

    @ApiModelProperty(value = "24 小时前每小时创建的待办")
    private Map<String, Integer> hourAgoCreateTodo;

    @ApiModelProperty(value = "180 天前每天已完成的待办")
    private Map<String, Integer> dayAgoCompleted;

    @ApiModelProperty(value = "180 天前每天创建的待办")
    private Map<String, Integer> dayAgoCreateTodo;

    @ApiModelProperty(value = "应用 id")
    private String appId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "今日待办")
    private Integer todayTodo;

    @ApiModelProperty(value = "今日延期待办")
    private Integer todayDelayTodo;
}

