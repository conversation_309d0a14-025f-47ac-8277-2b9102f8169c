package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.qo.user.BatchInviteQo;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.standardapp.converter.StdGroupChatConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdGroupChatDao;
import com.lanshan.base.commonservice.standardapp.entity.StdGroupChat;
import com.lanshan.base.commonservice.standardapp.enums.GroupChatType;
import com.lanshan.base.commonservice.standardapp.enums.StdCourseUserType;
import com.lanshan.base.commonservice.standardapp.qo.*;
import com.lanshan.base.commonservice.standardapp.service.StdCourseScheduleService;
import com.lanshan.base.commonservice.standardapp.service.StdCourseStudentService;
import com.lanshan.base.commonservice.standardapp.service.StdGroupChatService;
import com.lanshan.base.commonservice.standardapp.vo.CourseListVO;
import com.lanshan.base.commonservice.standardapp.vo.CourseUserVO;
import com.lanshan.base.commonservice.standardapp.vo.StdGroupChatVO;
import com.lanshan.base.commonservice.system.service.impl.SysConfigServiceImpl;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpChatService;
import me.chanjar.weixin.cp.bean.WxCpChat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 群聊表(StdGroupChat)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("stdGroupChatService")
public class StdGroupChatServiceImpl extends ServiceImpl<StdGroupChatDao, StdGroupChat> implements StdGroupChatService {

    private final StdCourseStudentService stdCourseStudentService;

    private final SysConfigServiceImpl sysConfigServiceImpl;

    private final WxCpServiceFactory wxCpServiceFactory;

    @Resource
    @Lazy
    private StdCourseScheduleService stdCourseScheduleService;

    private final UserService userService;

    private final RedisService redisService;

    @Override
    public List<StdGroupChatVO> listByType(String userId, String chatType) {
        List<StdGroupChat> groupChats = this.list(Wrappers.<StdGroupChat>lambdaQuery().eq(StringUtils.isNotEmpty(chatType), StdGroupChat::getGroupChatType, chatType)
                .apply("join_in_userid @> {0}::jsonb", userId));
        return StdGroupChatConverter.INSTANCE.toVO(groupChats);
    }

    @Override
    public Boolean createChat(String userId, GroupChatCreateQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            return createCourseGroup(userId, qo);
        }
        return false;
    }

    @Override
    public Boolean updateChat(String userId, GroupChatUpdateQO qo) {
        StdGroupChat groupChat = getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatId, qo.getGroupChatId()));
        if (Objects.isNull(groupChat)) {
            return false;
        }
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(groupChat.getGroupChatType())) {
            return updateCourseGroup(userId, qo, groupChat);
        }
        return false;
    }

    @Override
    public void batchJoinGroupChat(String userId, GroupChatBatchJoinQO qo) {
        CompletableFuture.runAsync(() -> {
            if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
                batchJoinCourseGroupChat(userId, qo);
            }
        });
    }

    @Override
    public void batchCreateGroupChat(String userId, GroupChatBatchCreateQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            CompletableFuture.runAsync(() -> {
                List<CourseListVO> allCourse = null;
                try {
                    allCourse = stdCourseScheduleService.getAllCourse(null);
                } catch (Exception e) {
                    log.error("获取课程列表失败", e);
                }
                if (CollUtil.isEmpty(allCourse)) {
                    return;
                }
                allCourse.stream().filter(item -> Objects.isNull(item.getGroupChatId())).forEach(item -> {
                    StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getUniqueId, item.getCourseId()));
                    if (Objects.nonNull(groupChat)) {
                        return;
                    }
                    try {
                        GroupChatCreateQO createCourseGroup = new GroupChatCreateQO();
                        createCourseGroup.setGroupChatType(qo.getGroupChatType());
                        createCourseGroup.setUniqueId(item.getCourseId());
                        createCourseGroup.setAdminFlag(qo.getAdminFlag());
                        createCourseGroup.setGroupChatName(item.getCourseName() + "【课程群】");
                        createCourseGroup(userId, createCourseGroup);
                    } catch (Exception e) {
                        log.error("批量创建群聊失败", e);
                    }
                });
            });
        }

    }

    @Override
    public void groupChatInviteUser(String userId, GroupChatInviteUserQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            String stdGroupChat = CommonServiceRedisKeys.STD_GROUP_CHAT;
            String key = stdGroupChat + qo.getGroupChatType() + ":" + qo.getUniqueId() + ":invite";
            if (redisService.hasKey(key) == Boolean.TRUE) {
                return;
            }
            CompletableFuture.runAsync(() -> {
                List<CourseUserVO> courseUserVOS = stdCourseStudentService.listCourseUserVO(qo.getUniqueId());
                if (CollUtil.isEmpty(courseUserVOS)) {
                    return;
                }
                List<String> needInviteUsers = courseUserVOS.stream().filter(item -> item.getActiveFlag() == Boolean.FALSE)
                        .map(CourseUserVO::getUserId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(needInviteUsers)) {
                    BatchInviteQo batchInviteQo = new BatchInviteQo();
                    batchInviteQo.setUser(needInviteUsers);
                    userService.batchInvite(batchInviteQo);
                }
                redisService.setCacheObject(key, "1", 60 * 60 * 24L, TimeUnit.SECONDS);
            });
        }

    }

    @Override
    public StdGroupChatVO getByUniqueIdAndType(String uniqueId, String groupChatType) {
        StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getUniqueId, uniqueId).eq(StdGroupChat::getGroupChatType, groupChatType));
        return StdGroupChatConverter.INSTANCE.toVO(groupChat);
    }

    @Override
    public Boolean getInviteFlag(GroupChatInviteUserQO qo) {
        String stdGroupChat = CommonServiceRedisKeys.STD_GROUP_CHAT;
        String key = stdGroupChat + qo.getGroupChatType() + ":" + qo.getUniqueId() + ":invite";
        return !redisService.hasKey(key);
    }

    @Override
    public Boolean userBatchJoinGroupChat(GroupChatUserBatchJoinQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, qo.getGroupChatType()).eq(StdGroupChat::getGroupChatId, qo.getGroupChatId()));
            WxCpChatService wxCpChatService = getCourseWxCpChatService();
            return updateGroupChat(wxCpChatService, groupChat, qo.getUserIds(), null);
        }
        return false;
    }

    @Override
    public Boolean userBatchRemoveGroupChat(GroupChatUserBatchRemoveQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, qo.getGroupChatType()).eq(StdGroupChat::getGroupChatId, qo.getGroupChatId()));
            WxCpChatService wxCpChatService = getCourseWxCpChatService();
            return updateGroupChat(wxCpChatService, groupChat, null, qo.getRemoveIds());
        }
        return false;
    }

    @Override
    public void updateGroupChatOfCp(GroupChatUpdateQO qo) {
        if (GroupChatType.COURSE.getCode().equalsIgnoreCase(qo.getGroupChatType())) {
            StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, qo.getGroupChatType()).eq(StdGroupChat::getGroupChatId, qo.getGroupChatId()));
            WxCpChatService courseWxCpChatService = getCourseWxCpChatService();
            try {
                WxCpChat chat = getChat(courseWxCpChatService, qo.getGroupChatId());
                groupChat.setGroupChatName(chat.getName());
                String ownerUserId = chat.getOwner();
                groupChat.setManageUserid(ownerUserId);
                CpUser user = userService.getCacheUserByUserid(ownerUserId);
                if (Objects.nonNull(user)) {
                    groupChat.setManageName(user.getName());
                }
                groupChat.setJoinInUserid(chat.getUsers());
            } catch (ServiceException e) {
                this.removeById(groupChat.getId());
            }
        }
    }

    @Override
    public Boolean joinGroupChat(String userId, GroupChatBatchJoinQO qo) {
        WxCpChatService wxCpChatService = getCourseWxCpChatService();
        String groupChatId = qo.getGroupChatIds().get(0);
        StdGroupChat groupChats = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, qo.getGroupChatType()).eq(StdGroupChat::getGroupChatId, groupChatId));
        return updateGroupChat(wxCpChatService, groupChats, Collections.singletonList(userId), null);
    }

    private void batchJoinCourseGroupChat(String userId, GroupChatBatchJoinQO qo) {
        List<StdGroupChat> groupChats = this.list(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, qo.getGroupChatType()).in(StdGroupChat::getGroupChatId, qo.getGroupChatIds()));
        for (StdGroupChat groupChat : groupChats) {
            WxCpChatService wxCpChatService = getCourseWxCpChatService();
            updateGroupChat(wxCpChatService, groupChat, Collections.singletonList(userId), null);
        }
    }

    private Boolean updateCourseGroup(String userId, GroupChatUpdateQO qo, StdGroupChat groupChat) {

        List<CourseUserVO> courseUserVOS = validateAndGetCourseUserList(groupChat.getUniqueId());

        Map<String, List<CourseUserVO>> studentGroup = courseUserVOS.stream().collect(Collectors.groupingBy(CourseUserVO::getCourseUserType));
        Set<String> joinInUserids = courseUserVOS.stream().map(CourseUserVO::getUserId).collect(Collectors.toSet());

        Optional.ofNullable(qo.getGroupChatName()).ifPresent(groupChat::setGroupChatName);

        WxCpChatService wxCpChatService = getCourseWxCpChatService();
        WxCpChat chat = null;
        try {
            chat = getChat(wxCpChatService, groupChat.getGroupChatId());
        } catch (Exception e) {
            return true;
        }
        String owner = chat.getOwner();
        groupChat.setManageUserid(owner);
        groupChat.setJoinInUserid(chat.getUsers());

        handleChatOwner(qo.getAdminFlag(), studentGroup, owner, groupChat, userId);
        Set<String> inCpUserSet = new HashSet<>(chat.getUsers());
        Sets.SetView<String> difference = Sets.difference(joinInUserids, inCpUserSet);
        List<String> newUserids = new ArrayList<>();
        if (CollUtil.isNotEmpty(difference)) {
            newUserids.addAll(difference);
        }
        //有人员变化再更新
        return updateGroupChat(wxCpChatService, groupChat, newUserids, null);
    }

    private Boolean createCourseGroup(String userId, GroupChatCreateQO qo) {
        try {
            List<CourseUserVO> courseUserVOS = validateAndGetCourseUserList(qo.getUniqueId());

            Map<String, List<CourseUserVO>> studentGroup = courseUserVOS.stream().collect(Collectors.groupingBy(CourseUserVO::getCourseUserType));
            Map<String, CourseUserVO> courseUserVOMap = courseUserVOS.stream().collect(Collectors
                    .toMap(CourseUserVO::getUserId, item -> item, (o, n) -> o));

            StdGroupChat stdGroupChat = buildStdGroupChat(qo, userId, studentGroup, courseUserVOMap);

            WxCpChatService wxCpChatService = getCourseWxCpChatService();
            Set<String> joinInUserids = courseUserVOS.stream().map(CourseUserVO::getUserId).collect(Collectors.toSet());
            return createGroupChat(wxCpChatService, stdGroupChat, joinInUserids, qo.getUniqueId());
        } catch (Exception e) {
            //保存错误日志
            StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, GroupChatType.COURSE.getCode()).eq(StdGroupChat::getUniqueId, qo.getUniqueId()));
            if (Objects.nonNull(groupChat)) {
                groupChat.setErrorMsg(e.getMessage());
                this.updateById(groupChat);
            } else {
                StdGroupChat errorGroupChat = new StdGroupChat();
                errorGroupChat.setUniqueId(qo.getUniqueId());
                errorGroupChat.setGroupChatType(GroupChatType.COURSE.getCode());
                errorGroupChat.setErrorMsg(e.getMessage());
                this.save(errorGroupChat);
            }
            throw e;
        }
    }

    private List<CourseUserVO> validateAndGetCourseUserList(String uniqueId) {
        List<CourseUserVO> courseUserVOS = stdCourseStudentService.listCourseUserVO(uniqueId);
        courseUserVOS = Optional.ofNullable(courseUserVOS).orElseGet(ArrayList::new).stream().filter(item -> item.getJoinInCpFlag() && item.getActiveFlag()).collect(Collectors.toList());
        if (CollUtil.isEmpty(courseUserVOS) || courseUserVOS.size() < 3) {
            throw ExceptionCodeEnum.GROUP_CHAT_MEMBER_SIZE_ERROR.toServiceException();
        }
        return courseUserVOS;
    }

    private WxCpChatService getCourseWxCpChatService() {
        String corpId = sysConfigServiceImpl.selectConfigByKey("corpId");
        String courseAgentId = sysConfigServiceImpl.selectConfigByKey("course.agentId");
        return wxCpServiceFactory.get(corpId, courseAgentId).getChatService();
    }

    private WxCpChat getChat(WxCpChatService wxCpChatService, String groupChatId) {
        try {
            return wxCpChatService.get(groupChatId);
        } catch (WxErrorException e) {
            log.error("获取群聊失败", e);
            this.remove(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatId, groupChatId));
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(e.getMessage());
        }
    }

    private void handleChatOwner(Boolean adminFlag, Map<String, List<CourseUserVO>> studentGroup, String
            owner, StdGroupChat groupChat, String userId) {
        List<CourseUserVO> courseTeachVOList = studentGroup.get(StdCourseUserType.TEACHER.getCode());
        if (CollUtil.isNotEmpty(courseTeachVOList)) {
            Map<String, String> courseTeachMap = courseTeachVOList.stream()
                    .collect(Collectors.toMap(CourseUserVO::getUserId, CourseUserVO::getName));

            if (adminFlag && !courseTeachMap.containsKey(owner)) {
                CourseUserVO courseUserVO = courseTeachVOList.get(0);
                groupChat.setManageUserid(courseUserVO.getUserId());
                groupChat.setManageName(courseUserVO.getName());
            } else if (!adminFlag) {
                groupChat.setManageUserid(userId);
                groupChat.setManageName(courseTeachMap.get(userId));
            }
        }
    }

    private Boolean updateGroupChat(WxCpChatService wxCpChatService, StdGroupChat
            groupChat, List<String> joinInUserids, List<String> removeUserList) {
        try {
            wxCpChatService.update(groupChat.getGroupChatId(), groupChat.getGroupChatName(), groupChat.getManageUserid(), joinInUserids, removeUserList);
            log.info("更新课程群聊成功, chatId={}, manageUserid={}, joinInUserids={}, uniqueId={}",
                    groupChat.getGroupChatId(), groupChat.getManageUserid(), joinInUserids, groupChat.getUniqueId());
            if (CollUtil.isNotEmpty(joinInUserids)) {
                groupChat.getJoinInUserid().addAll(joinInUserids);
            }
            if (CollUtil.isNotEmpty(removeUserList)) {
                groupChat.getJoinInUserid().removeAll(removeUserList);
            }
            return this.updateById(groupChat);
        } catch (WxErrorException e) {
            log.error("更新群聊失败", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(e.getMessage());
        }
    }

    private StdGroupChat buildStdGroupChat(GroupChatCreateQO qo, String
            userId, Map<String, List<CourseUserVO>> studentGroup, Map<String, CourseUserVO> courseUserVOMap) {

        List<CourseUserVO> courseTeachVOList = studentGroup.get(StdCourseUserType.TEACHER.getCode());

        if (qo.getAdminFlag() || StringUtils.isBlank(userId)) {
            int index = RandomUtil.randomInt(0, courseTeachVOList.size());
            userId = courseTeachVOList.get(index).getUserId();
        }

        if (Objects.isNull(courseUserVOMap.get(userId))) {
            throw ExceptionCodeEnum.GROUP_CHAT_TCH_NOT_ACTIVE.toServiceException();
        }

        StdGroupChat stdGroupChat = new StdGroupChat();
        stdGroupChat.setGroupChatType(GroupChatType.COURSE.getCode());
        stdGroupChat.setGroupChatName(qo.getGroupChatName());
        stdGroupChat.setJoinInUserid(new ArrayList<>(courseUserVOMap.keySet()));
        stdGroupChat.setUniqueId(qo.getUniqueId());

        if (qo.getAdminFlag()) {
            if (CollUtil.isEmpty(courseTeachVOList)) {
                throw ExceptionCodeEnum.GROUP_CHAT_TCH_NOT_IN_CP.toServiceException();
            }
            CourseUserVO courseUserVO = courseTeachVOList.get(0);
            stdGroupChat.setManageName(courseUserVO.getName());
            stdGroupChat.setManageUserid(courseUserVO.getUserId());
        } else {
            CourseUserVO courseUserVO = courseUserVOMap.get(userId);
            stdGroupChat.setManageName(courseUserVO.getName());
            stdGroupChat.setManageUserid(courseUserVO.getUserId());
        }

        return stdGroupChat;
    }

    private Boolean createGroupChat(WxCpChatService wxCpChatService, StdGroupChat
            stdGroupChat, Set<String> joinInUserids, String uniqueId) {
        StdGroupChat groupChat = this.getOne(Wrappers.<StdGroupChat>lambdaQuery().eq(StdGroupChat::getGroupChatType, GroupChatType.COURSE.getCode()).eq(StdGroupChat::getUniqueId, uniqueId));
        try {
            String chatId = wxCpChatService.create(stdGroupChat.getGroupChatName(), stdGroupChat.getManageUserid(), new ArrayList<>(joinInUserids), null);
            log.info("创建课程群聊成功, chatId={}, manageUserid={}, joinInUserids={}, uniqueId={}",
                    chatId, stdGroupChat.getManageUserid(), joinInUserids, uniqueId);
            stdGroupChat.setGroupChatId(chatId);
            if (Objects.isNull(groupChat)) {
                return this.save(stdGroupChat);
            } else {
                BeanUtil.copyProperties(stdGroupChat, groupChat, "id", "createTime");
                groupChat.setErrorMsg("");
                return this.updateById(groupChat);
            }
        } catch (WxErrorException e) {
            log.error("创建群聊失败", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(e.getMessage());
        }
    }
}

