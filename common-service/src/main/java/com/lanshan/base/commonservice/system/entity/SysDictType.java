package com.lanshan.base.commonservice.system.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lanshan.base.api.dto.system.SysDictTypeVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 字典类型表(SysDictType)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:26:36
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@ToString
public class SysDictType extends Model<SysDictType> {
    private static final long serialVersionUID = -2542965938546097871L;
    /**
     * 字典主键
     */
    private Long dictId;
    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private transient Map<String, Object> params = new HashMap<>(8);

    public SysDictType(SysDictTypeVo vo) {
        BeanUtils.copyProperties(vo, this);
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.dictId;
    }
}

