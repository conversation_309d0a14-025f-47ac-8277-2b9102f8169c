package com.lanshan.base.commonservice.system.entity;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lanshan.base.api.dto.system.SysNoticeVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知公告表(SysNotice)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:26:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SysNotice extends Model<SysNotice> {
    private static final long serialVersionUID = 528678977033714306L;
    /**
     * 公告ID
     */
    private Integer noticeId;
    /**
     * 公告标题
     */
    private String noticeTitle;
    /**
     * 公告类型（1通知 2公告）
     */
    private String noticeType;
    /**
     * 公告内容
     */
    private Object noticeContent;
    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private transient Map<String, Object> params = new HashMap<>(8);

    public SysNotice(SysNoticeVo vo) {
        BeanUtil.copyProperties(vo, this);
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.noticeId;
    }
}

