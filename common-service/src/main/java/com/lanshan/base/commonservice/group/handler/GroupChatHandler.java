package com.lanshan.base.commonservice.group.handler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.entity.MsgClassGroupChat;
import com.lanshan.base.commonservice.group.entity.MsgCourseGroupChat;
import com.lanshan.base.commonservice.group.entity.MsgDeptGroupChat;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.vo.*;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface GroupChatHandler {

    /**
     * 分页查询班级群聊信息
     *
     * @param page   分页对象
     * @param pageQo 分页查询对象
     */
    void pageClassGroupChat(IPage<MsgClassGroupChatVO> page, MsgClassGroupChatPageQo pageQo);

    /**
     * 查询班级群聊详情
     *
     * @param qo 查询对象
     */
    List<MsgClassGroupChatDetailVO> getMsgClassGroupChatDetailVO(MsgClassGroupChatDetailQo qo);

    /**
     * 获取班级群聊用户列表
     *
     * @param saveQo 新增入参
     * @return 群聊用户列表
     */
    List<GroupChatScope> listClassGroupChatUser(MsgClassGroupChatSaveQo saveQo);

    /**
     * 创建班级群聊
     *
     * @param saveQo   新增入参
     * @param userList 群聊用户列表
     */
    void creatClassGroupChat(MsgClassGroupChatSaveQo saveQo, List<GroupChatScope> userList) throws WxErrorException;

    /**
     * 分页查询课程群聊信息
     *
     * @param page   分页对象
     * @param pageQo 分页查询对象
     */
    void pageCourseGroupChat(IPage<MsgCourseGroupChatVO> page, MsgCourseGroupChatPageQo pageQo);

    /**
     * 查询课程群聊详情
     *
     * @param qo 查询对象
     * @return 课程群聊详情
     */
    List<MsgCourseGroupChatDetailVO> getMsgCourseGroupChatDetailVO(MsgCourseGroupChatDetailQo qo);

    /**
     * 获取课程群聊用户列表
     *
     * @param saveQo
     * @return
     */
    List<GroupChatScope> listCourseGroupChatUser(MsgCourseGroupChatSaveQo saveQo);

    /**
     * 创建课程群聊
     *
     * @param saveQo   新增入参
     * @param userList 群聊用户列表
     * @throws WxErrorException
     */
    void creatCourseGroupChat(MsgCourseGroupChatSaveQo saveQo, List<GroupChatScope> userList) throws WxErrorException;


    /**
     * 分页查询部门群聊信息
     *
     * @param userId 用户id
     */
    List<MsgDeptGroupChatVO> listDeptGroupChat(String userId);


    /**
     * 查询课程群聊详情
     *
     * @param deptCode 部门编码
     * @return 部门群聊详情
     */
    List<MsgDeptGroupChatDetailVO> listMsgDeptGroupChatDetail(String deptCode);

    /**
     * 创建部门群聊
     *
     * @param saveQo 新增入参
     */
    void creatDeptGroupChat(MsgDeptGroupChatSaveQo saveQo) throws WxErrorException;

    /**
     * 更新班级群聊用户
     */
    void updateClassGroupChatUser(List<MsgClassGroupChat> list);

    /**
     * 更新课程群聊用户
     */
    void updateCourseGroupChatUser(List<MsgCourseGroupChat> list);

    /**
     * 更新部门群聊用户
     *
     * @param list 部门群聊列表
     */
    void updateDeptGroupChatUser(List<MsgDeptGroupChat> list);

    /**
     * 更新群聊名称
     *
     * @param qo
     */
    Boolean updateGroupChatName(UpdateMsgGroupChatNameQO qo) throws WxErrorException;

    /**
     * 邀请群聊成员
     *
     * @param qo 邀请群聊成员入参
     */
    void inviteGroupUser(InviteGroupUserQO qo);

    /**
     * 同步群聊信息
     *
     * @param qo 同步群聊信息入参
     * @return
     */
    GroupCommonInfoVO syncGroupChat(SyncGroupChatQO qo) throws WxErrorException;

    /**
     * 部门群聊分页
     *
     * @param qo 分页查询对象
     * @return 分页数据
     */
    IPage<MsgDeptGroupChatVO> deptPageGroupChat(MsgDeptGroupChatQO qo);

    /**
     * 部门群聊导出
     *
     * @param qo       查询对象
     * @param response 响应对象
     */
    void deptGroupChatExport(MsgDeptGroupChatQO qo, HttpServletResponse response);

    /**
     * 班级群聊分页查询
     *
     * @param qo 分页查询对象
     * @return 分页数据
     */
    IPage<MsgClassGroupChatVO> classGroupPageParam(MsgClassGroupChatQO qo);

    /**
     * 班级群聊导出
     *
     * @param qo       查询对象
     * @param response 响应对象
     */
    void classGroupExport(MsgClassGroupChatQO qo, HttpServletResponse response);

    /**
     * 课程群聊分页查询
     *
     * @param qo 分页查询对象
     * @return 分页数据
     */
    IPage<MsgCourseGroupChatVO> courseGroupPageByParam(MsgCourseGroupChatQO qo);

    /**
     * 课程群聊导出
     *
     * @param qo       查询对象
     * @param response 响应对象
     */
    void courseGroupExport(MsgCourseGroupChatQO qo, HttpServletResponse response);

    /**
     * 获取群聊通用信息
     *
     * @param qo 查询对象
     * @return 群聊公共信息
     */
    GroupCommonInfoVO getGroupCommonInfo(GetGroupCommonInfoQO qo);

    /**
     * 检查是否有创建权限
     *
     * @return 是否有创建权限
     */
    CheckGroupCreateVO checkCanCreate(String userId);

    /**
     * 检查是否有创建课程群聊权限
     * @param userId 用户id
     * @return 是否有创建课程群聊权限
     */
    CheckGroupCreateVO checkCourseGroupCanCreate(String userId);

    /**
     * 检查是否有创建班级群聊权限
     * @param userId 用户id
     * @return 是否有创建班级群聊权限
     */
    CheckGroupCreateVO checkClassGroupCanCreate(String userId);

    /**
     *  获取班级群聊申请基础信息
     * @return 班级群聊申请基础信息
     */
    ClassGroupChatApplyBaseVO getClassGroupChatApplyBase();

    /**
     * 获取班级信息
     * @return 班级信息
     */
    List<ClassInfoVO> listClassInfo();

    /**
     * 处理审批状态变更事件
     * @param message 消息
     */
    void processOpenApprovalChange(WxCpXmlMessage message);
}
