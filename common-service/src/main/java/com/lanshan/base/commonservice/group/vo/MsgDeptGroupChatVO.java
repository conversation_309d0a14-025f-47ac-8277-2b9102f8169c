package com.lanshan.base.commonservice.group.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 部门群聊(MsgDeptGroupChat)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "部门群聊VO")
@Data
@ToString
public class MsgDeptGroupChatVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "群聊id")
    private String chatId;

    @ApiModelProperty(value = "群聊名称")
    private String chatName;

    @ApiModelProperty(value = "群主 id")
    private String managerUserid;

    @ApiModelProperty(value = "群主名称")
    private String managerName;

    @ApiModelProperty(value = "加入人数")
    private Integer joinCount;

    @ApiModelProperty(value = "群聊总人数")
    private Integer totalGroupMember;

    @ApiModelProperty(value = "可创建群成员")
    private String canCreateName;

    @ApiModelProperty(value = "可创建群成员学工号")
    private String canCreateGh;
}

