package com.lanshan.base.commonservice.message.handler.impl;

import cn.hutool.core.date.DateUtil;
import com.lanshan.base.api.dto.message.BaseMsgBody;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.message.dao.FluxDBRepository;
import com.lanshan.base.commonservice.message.handler.ChannelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public abstract class AbstractChannelHandler<T extends BaseMsgBody> implements ChannelHandler<T> {

    @Resource
    private FluxDBRepository fluxDBRepository;

    public Result<Object> handle(BaseMsgBody msgBody) {

        // 发送前保存日志
        saveLog(msgBody);

        //各个渠道分别发送消息
        Result<Object> result = sendMsg((T) msgBody);

        //更新发送日志状态
        saveLog(msgBody, result);

        return result;
    }

    /**
     * 统一保存发送日志
     *
     * @param msgBody 消息体
     */
    @Override
    public void saveLog(BaseMsgBody msgBody) {
        Map<String, String> tagmap = getTagMap(msgBody);
        Map<String, Object> fieldMap = new HashMap<>(4);
        fieldMap.put("msgBody", msgBody.toString());
        fluxDBRepository.save(FluxDBRepository.MSG_SEND_LOG_TABLE, tagmap, fieldMap);
        log.info(DateUtil.now() + "-" + msgBody);
    }

    private void saveLog(BaseMsgBody msgBody, Result<Object> result) {
        Map<String, String> tagMap = getTagMap(msgBody);
        Map<String, Object> fieldMap = new HashMap<>(4);
        fieldMap.put("result", result.toString());
        fluxDBRepository.save(FluxDBRepository.MSG_SEND_LOG_TABLE, tagMap, fieldMap);
    }

    private Map<String, String> getTagMap(BaseMsgBody msgBody) {
        Map<String, String> map = new HashMap<>(8);
        map.put("toUser", msgBody.getToUser());
        map.put("toParty", msgBody.getToParty());
        map.put("toTag", msgBody.getToTag());
        map.put("msgType", msgBody.getMsgType());
        map.put("content", msgBody.getContent());
        return map;
    }

}
