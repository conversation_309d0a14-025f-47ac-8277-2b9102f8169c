package com.lanshan.base.commonservice.constant;

public class AddressbookConstant {
    private AddressbookConstant() {}


    /**
     * 获取锁超时时间（秒）
     */
    public static final int GET_LOCK_OVERTIME = 5;

    /**
     * 同步任务锁持有时间（秒） - 35分钟
     */
    public static final long SYNC_LOCK_LEASE_TIME = 2100L;

    /**
     * 同步任务超时时间（秒） - 30分钟
     */
    public static final int SYNC_TASK_TIMEOUT = 1800;

    /**
     * 并发任务超时时间（分钟） - 10分钟
     */
    public static final int CONCURRENT_TASK_TIMEOUT_MINUTES = 10;

    /**
     * 事务超时时间（秒） - 10分钟
     */
    public static final int TRANSACTION_TIMEOUT = 600;

    /**
     * 处理线程数量 5个线程
     */
    public static final int PROCESS_THREAD_COUNT_5 = 5;

    /**
     * 处理线程数量 16个线程
     */
    public static final int PROCESS_THREAD_COUNT_16 = 16;

    /**
     * IN查询最大数量
     */
    public static final int IN_QUERY_SIZE = 1000;

    /**
     * 初始部门顺序值
     */
    public static final long INIT_ORDER = 100000000L;

    /**
     * 顺序值间隔
     */
    public static final long ORDER_GAP = 1000L;
}
