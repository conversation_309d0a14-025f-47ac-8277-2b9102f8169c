package com.lanshan.base.commonservice.system.service.impl;


import com.lanshan.base.api.dto.system.SysDictDataVo;
import com.lanshan.base.api.utils.system.DictUtils;
import com.lanshan.base.commonservice.system.entity.SysDictData;
import com.lanshan.base.commonservice.system.mapper.SysDictDataMapper;
import com.lanshan.base.commonservice.system.service.ISysDictDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service("sysDictDataService")
public class SysDictDataServiceImpl implements ISysDictDataService {
    @Resource
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return sysDictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return sysDictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return sysDictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            sysDictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            List<SysDictDataVo> voList = dictDatas.stream().map(SysDictData::toVo).collect(Collectors.toList());
            DictUtils.setDictCache(data.getDictType(), voList);
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData data) {
        int row = sysDictDataMapper.insertDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            List<SysDictDataVo> voList = dictDatas.stream().map(SysDictData::toVo).collect(Collectors.toList());
            DictUtils.setDictCache(data.getDictType(), voList);
        }
        return row;
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData data) {
        int row = sysDictDataMapper.updateDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas.stream().map(SysDictData::toVo).collect(Collectors.toList()));
        }
        return row;
    }
}
