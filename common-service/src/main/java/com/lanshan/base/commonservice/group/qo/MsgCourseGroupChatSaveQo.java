package com.lanshan.base.commonservice.group.qo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class MsgCourseGroupChatSaveQo implements Serializable {

    @ApiModelProperty(value = "课表编码")
    private String code;

    @ApiModelProperty(value = "学年学期")
    private String semester;

    @ApiModelProperty(value = "课程编码")
    private String courseCode;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "教学班")
    private String teachingClass;

    @ApiModelProperty(value = "群聊名称")
    @NotBlank(message = "群聊名称不能为空")
    private String chatName;

    @ApiModelProperty(value = "学生类型 1：本科生 2：研究生")
    private Integer studentType;

    @ApiModelProperty(value = "教师列表", hidden = true)
    private List<String> teacherList;
}

