package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.common.TagIdDTO;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import com.lanshan.base.api.dto.user.TagDto;
import com.lanshan.base.api.dto.user.TagUserDto;
import com.lanshan.base.api.dto.user.TagUserSetDTO;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.feign.user.TagFeign;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.TagInfoVo;
import com.lanshan.base.commonservice.addressbook.entity.*;
import com.lanshan.base.commonservice.addressbook.mapper.CpTagMapper;
import com.lanshan.base.commonservice.addressbook.service.CommonUseTagService;
import com.lanshan.base.commonservice.addressbook.service.DepartmentTagRelationService;
import com.lanshan.base.commonservice.addressbook.service.TagService;
import com.lanshan.base.commonservice.addressbook.service.UserTagRelationService;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.enums.OperateStatusEnum;
import com.lanshan.base.starter.redis.util.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxCpErrorMsgEnum;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 标签表(Tag)服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-19
 */

@Slf4j
@Service
public class TagServiceImpl extends ServiceImpl<CpTagMapper, CpTag> implements TagService {

    @Resource
    private TagFeign tagFeign;

    @Resource
    private UserTagRelationService userTagRelationService;

    @Resource
    private DepartmentTagRelationService departmentTagRelationService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private CommonUseTagService commonUseTagService;

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CpTagMapper cpTagMapper;

    @PostConstruct
    public void initTransactionTemplate() {
        transactionTemplate.setTimeout(30);
    }

    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncTagToCp(String corpId, List<CpTagOperate> tagOpList, Map<Long, CpTag> tagMap) {
        try {
            // 分批处理，每批最多20个标签
            int batchSize = 20;
            List<List<CpTagOperate>> batchLists = CollUtil.split(tagOpList, batchSize);

            int successCount = 0;
            int failCount = 0;

            // 逐批处理，每批用一个事务
            for (List<CpTagOperate> batchList : batchLists) {
                // 记录批次处理结果
                BatchProcessResult result = processBatchTagsToCp(corpId, batchList, tagMap);
                successCount += result.getSuccessCount();
                failCount += result.getFailCount();
            }

            log.info("线程[{}] ==> 异步方式同步标签至企微执行完成，成功: {}, 失败: {}",
                    Thread.currentThread().getName(), successCount, failCount);
            return new AsyncResult<>(successCount > 0 ? YnEnum.YES.getValue() : YnEnum.NO.getValue());
        } catch (Exception e) {
            log.error("批量同步标签异常: {}", e.getMessage(), e);
            return new AsyncResult<>(YnEnum.NO.getValue());
        }
    }

    // 新增的批处理方法
    private BatchProcessResult processBatchTagsToCp(String corpId, List<CpTagOperate> batchList, Map<Long, CpTag> tagMap) {
        int successCount = 0;
        int failCount = 0;

        // 使用单一事务处理一批标签
        Boolean result = transactionTemplate.execute(status -> {
            try {
                for (CpTagOperate operate : batchList) {
                    CpTag tag = tagMap.get(operate.getTagid());
                    saveOrUpdateCpTag(corpId, operate, tag);
                }
                return true;
            } catch (Exception e) {
                log.error("批量保存标签失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                return false;
            }
        });

        if (Boolean.TRUE.equals(result)) {
            successCount = batchList.size();
        } else {
            // 批处理失败，退回到单个处理模式，减少影响范围
            for (CpTagOperate operate : batchList) {
                try {
                    CpTag tag = tagMap.get(operate.getTagid());
                    Boolean singleResult = transactionTemplate.execute(s -> {
                        try {
                            saveOrUpdateCpTag(corpId, operate, tag);
                            return true;
                        } catch (Exception e) {
                            log.error("保存单个标签失败, tagId: {}, error: {}", operate.getTagid(), e.getMessage());
                            s.setRollbackOnly();
                            return false;
                        }
                    });

                    if (Boolean.TRUE.equals(singleResult)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("单个标签事务执行异常: {}, tagId: {}", e.getMessage(), operate.getTagid(), e);
                    failCount++;
                }
            }
        }

        return new BatchProcessResult(successCount, failCount);
    }

    // 批处理结果类
    @Data
    @AllArgsConstructor
    private static class BatchProcessResult {
        private int successCount;
        private int failCount;
    }

    /**
     * 保存或新增标签至企微
     */
    public void saveOrUpdateCpTag(String corpId, CpTagOperate operate, CpTag tag) {
        TagSaveQo qo = TagSaveQo.builder()
                .tagid(operate.getTagid())
                .tagname(operate.getTagname()).build();

        //不存在则新增
        if (tag == null) {

            //新增至企微
            Result<TagIdDTO> tagCreateResult = tagFeign.tagCreate(corpId, null, qo);
            if (tagCreateResult.success()) {
                operate.setOperateStatus(OperateStatusEnum.SUCCESS.getCode());
                operate.setTagid(tagCreateResult.getResult().getTagid());
            } else if (!tagCreateResult.success()
                    && WxCpErrorMsgEnum.CODE_1.getCode() != tagCreateResult.getCode()
                    && WxCpErrorMsgEnum.CODE_45033.getCode() != tagCreateResult.getCode()) {
                //设为失败
                operate.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                operate.setErrorMsg(tagCreateResult.getMsg());
            }

            //存在则更新
        } else {
            //更新至企微

            //设置agentId
            String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);
            Result<?> tagUpdateResult = tagFeign.tagUpdate(corpId, agentIdStr, qo);
            if (tagUpdateResult.success()) {
                operate.setOperateStatus(OperateStatusEnum.SUCCESS.getCode());
            } else if (!tagUpdateResult.success()
                    && WxCpErrorMsgEnum.CODE_1.getCode() != tagUpdateResult.getCode()
                    && WxCpErrorMsgEnum.CODE_45033.getCode() != tagUpdateResult.getCode()) {
                //设为失败
                operate.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                operate.setErrorMsg(tagUpdateResult.getMsg());
            }
        }
    }


    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncUserTagToCp(String corpId, List<List<CpUserTagRelationOperate>> lists, Map<Long, CpTag> tagMap) {
        int successCount = 0;
        int failCount = 0;

        try {
            for (List<CpUserTagRelationOperate> operateList : lists) {
                if (CollUtil.isEmpty(operateList)) {
                    continue;
                }

                Long tagId = operateList.get(0).getTagid();
                List<String> useridList = operateList.stream().map(CpUserTagRelationOperate::getUserid).collect(Collectors.toList());

                // 标签用户成员一次最多只能添加1000个，每1000个进行拆分
                List<List<String>> split = CollUtil.split(useridList, 1000);

                for (List<String> splitUseridList : split) {
                    if (CollUtil.isEmpty(splitUseridList)) {
                        continue;
                    }

                    // 使用事务模板保证数据一致性
                    Boolean result = transactionTemplate.execute(status -> {
                        try {
                            return processUserTagBatch(corpId, tagId, splitUseridList, operateList, tagMap);
                        } catch (Exception e) {
                            status.setRollbackOnly();
                            log.error("处理用户标签批次失败: tagId={}, error={}", tagId, e.getMessage(), e);
                            return false;
                        }
                    });

                    if (Boolean.TRUE.equals(result)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }

            log.info("线程[{}] ==> 异步方式同步用户标签关系至企微完成, 成功: {}, 失败: {}",
                    Thread.currentThread().getName(), successCount, failCount);
            return new AsyncResult<>(successCount > 0 ? YnEnum.YES.getValue() : YnEnum.NO.getValue());
        } catch (Exception e) {
            log.error("用户标签同步任务整体失败: {}", e.getMessage(), e);
            return new AsyncResult<>(YnEnum.NO.getValue());
        }
    }

    // 处理单个标签用户批次的方法
    public boolean processUserTagBatch(String corpId, Long tagId, List<String> userList,
                                        List<CpUserTagRelationOperate> operateList, Map<Long, CpTag> tagMap) {
        TagUsersQo qo = TagUsersQo.builder()
                .tagId(tagId)
                .userList(userList).build();

        try {
            // 设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagId))
                    .map(CpTag::getAgentId)
                    .map(String::valueOf)
                    .orElse(null);

            // 增加标签成员
            Result<TagAddOrRemoveUsersDto> result = tagFeign.addTagUsers(corpId, agentIdStr, qo);

            if (result.success()) {
                // 找出匹配当前批次的记录并更新状态
                operateList.stream()
                        .filter(item -> userList.contains(item.getUserid()))
                        .forEach(item -> item.setOperateStatus(OperateStatusEnum.SUCCESS.getCode()));

                log.info("新增标签成员成功, tagId:{}, userCount:{}", tagId, userList.size());
                return true;
            } else if (!result.success() &&
                    WxCpErrorMsgEnum.CODE_1.getCode() != result.getCode() &&
                    WxCpErrorMsgEnum.CODE_45033.getCode() != result.getCode()) {

                // 设为失败
                String errorMsg = result.getMsg();
                operateList.stream()
                        .filter(item -> userList.contains(item.getUserid()))
                        .forEach(item -> {
                            item.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                            item.setErrorMsg(errorMsg);
                        });

                log.error("新增标签成员失败, tagId:{}, userCount:{}, error:{}", tagId, userList.size(), errorMsg);
                return false;
            }
            return true; // 忽略的特定错误码视为成功
        } catch (Exception e) {
            log.error("同步用户标签-调用feign异常：tagId={}, error={}", tagId, e.getMessage(), e);
            throw e; // 重新抛出异常让事务回滚
        }
    }



    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncDeptTagToCp(String corpId, List<List<CpDepartmentTagRelationOperate>> lists, Map<Long, CpTag> tagMap) {
        int successCount = 0;
        int failCount = 0;

        try {
            for (List<CpDepartmentTagRelationOperate> operateList : lists) {
                if (CollUtil.isEmpty(operateList)) {
                    continue;
                }

                Long tagId = operateList.get(0).getTagid();
                List<Long> deptIdList = operateList.stream()
                        .map(CpDepartmentTagRelationOperate::getDepartmentid).collect(Collectors.toList());

                // 标签部门一次最多只能添加100个，每100个进行拆分
                List<List<Long>> split = CollUtil.split(deptIdList, 100);

                for (List<Long> splitDeptIdList : split) {
                    if (CollUtil.isEmpty(splitDeptIdList)) {
                        continue;
                    }

                    // 使用事务模板保证数据一致性
                    Boolean result = transactionTemplate.execute(status -> {
                        try {
                            return processDeptTagBatch(corpId, tagId, splitDeptIdList, operateList, tagMap);
                        } catch (Exception e) {
                            status.setRollbackOnly();
                            log.error("处理部门标签批次失败: tagId={}, error={}", tagId, e.getMessage(), e);
                            return false;
                        }
                    });

                    if (Boolean.TRUE.equals(result)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }

            log.info("线程[{}] ==> 异步方式同步部门标签关系至企微完成, 成功: {}, 失败: {}",
                    Thread.currentThread().getName(), successCount, failCount);
            return new AsyncResult<>(successCount > 0 ? YnEnum.YES.getValue() : YnEnum.NO.getValue());
        } catch (Exception e) {
            log.error("部门标签同步任务整体失败: {}", e.getMessage(), e);
            return new AsyncResult<>(YnEnum.NO.getValue());
        }
    }

    // 处理单个标签部门批次的方法
    public boolean processDeptTagBatch(String corpId, Long tagId, List<Long> deptIdList,
                                        List<CpDepartmentTagRelationOperate> operateList, Map<Long, CpTag> tagMap) {
        TagUsersQo qo = TagUsersQo.builder()
                .tagId(tagId)
                .partyList(deptIdList).build();

        try {
            // 设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagId))
                    .map(CpTag::getAgentId)
                    .map(String::valueOf)
                    .orElse(null);

            // 增加标签成员
            Result<TagAddOrRemoveUsersDto> result = tagFeign.addTagUsers(corpId, agentIdStr, qo);

            if (result.success()) {
                // 找出匹配当前批次的记录并更新状态
                operateList.stream()
                        .filter(item -> deptIdList.contains(item.getDepartmentid()))
                        .forEach(item -> item.setOperateStatus(OperateStatusEnum.SUCCESS.getCode()));

                log.info("新增标签部门成功, tagId:{}, deptCount:{}", tagId, deptIdList.size());
                return true;
            } else if (!result.success() &&
                    WxCpErrorMsgEnum.CODE_1.getCode() != result.getCode() &&
                    WxCpErrorMsgEnum.CODE_45033.getCode() != result.getCode()) {

                // 设为失败
                String errorMsg = result.getMsg();
                operateList.stream()
                        .filter(item -> deptIdList.contains(item.getDepartmentid()))
                        .forEach(item -> {
                            item.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                            item.setErrorMsg(errorMsg);
                        });

                log.error("新增标签部门失败, tagId:{}, deptCount:{}, error:{}", tagId, deptIdList.size(), errorMsg);
                return false;
            }
            return true; // 忽略的特定错误码视为成功
        } catch (Exception e) {
            log.error("同步部门标签-调用feign异常：tagId={}, error={}", tagId, e.getMessage(), e);
            throw e; // 重新抛出异常让事务回滚
        }
    }


    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncGetTagUser(String corpId, List<TagDto> cpTagList, Map<Long, List<String>> tagUserMap, Map<Long, List<Long>> tagDeptMap) {
        for (TagDto wxCpTag : cpTagList) {
            try {
                //查询全部标签成员
                Result<TagUserDto> result = tagFeign.tagGet(corpId, null, wxCpTag.getTagid());
                //企微查询异常
                if (!result.success()) {
                    log.error("查询企微标签成员异常：{}", result.getMsg());
                    continue;
                }

                TagUserDto tagUser = result.getResult();
                //关联用户
                List<TagUserDto.User> userlist = tagUser.getUserlist();
                if (CollUtil.isNotEmpty(userlist)) {
                    List<String> userIdList = userlist.stream().map(TagUserDto.User::getUserid).collect(Collectors.toList());
                    //标签id-用户id列表
                    tagUserMap.put(wxCpTag.getTagid(), userIdList);
                }

                //关联部门
                List<Long> partyIdlist = tagUser.getPartylist();
                if (CollUtil.isNotEmpty(partyIdlist)) {
                    //标签id-部门id列表
                    tagDeptMap.put(wxCpTag.getTagid(), partyIdlist);
                }
            } catch (Exception e) {
                log.error("调用feign异常：{}", e.getMessage(), e);
            }
        }
        log.info("线程[{}] ==> 异步方式获取标签成员成功", Thread.currentThread().getName());
        return new AsyncResult<>(YnEnum.YES.getValue());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void batchAddTagUsers(BatchTagUsersQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询标签
        List<CpTag> tags = super.listByIds(qo.getTagIdList());
        Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        //查询已经存在的用户关联
        Map<Long, List<String>> tagUserMap = new HashMap<>();
        if (CollUtil.isEmpty(qo.getUserList())) {
            List<CpUserTagRelation> list = userTagRelationService.list(new LambdaQueryWrapper<CpUserTagRelation>().in(CpUserTagRelation::getTagid, qo.getTagIdList()));
            tagUserMap = list.stream().collect(Collectors.groupingBy(CpUserTagRelation::getTagid, Collectors.mapping(CpUserTagRelation::getUserid, Collectors.toList())));
        }
        //查询已经存在的成员关联
        Map<Long, List<Long>> tagDeptMap = new HashMap<>();
        if (CollUtil.isEmpty(qo.getTagIdList())) {
            List<CpDepartmentTagRelation> list = departmentTagRelationService.list(new LambdaQueryWrapper<CpDepartmentTagRelation>().in(CpDepartmentTagRelation::getTagid, qo.getTagIdList()));
            tagDeptMap = list.stream().collect(Collectors.groupingBy(CpDepartmentTagRelation::getTagid, Collectors.mapping(CpDepartmentTagRelation::getDepartmentid, Collectors.toList())));
        }

        //新增标签关联
        addTagRelation(corpId, qo, tagUserMap, tagDeptMap, tagMap);
    }

    /**
     * 新增标签关联
     */
    private void addTagRelation(String corpId, BatchTagUsersQo qo, Map<Long, List<String>> tagUserMap, Map<Long, List<Long>> tagDeptMap, Map<Long, CpTag> tagMap) {
        List<CpUserTagRelation> userTagRelationList = new ArrayList<>();
        List<CpDepartmentTagRelation> deptTagRelationList = new ArrayList<>();
        //遍历添加标签成员
        for (Long tagid : qo.getTagIdList()) {
            CpTag tag = tagMap.get(tagid);
            TagUsersQo tagUsersQo = TagUsersQo.builder()
                    .tagId(tagid)
                    .userList(qo.getUserList())
                    .partyList(qo.getPartyList())
                    .build();
            //新增标签成员
            addTagUsers(corpId, tagUsersQo, tagUserMap, tagDeptMap, userTagRelationList, deptTagRelationList, tag);
        }
        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();
        //保存标签关联
        proxy.saveTagRelation(userTagRelationList, deptTagRelationList);
    }

    /**
     * 保存标签关联
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveTagRelation(List<CpUserTagRelation> userTagRelationList, List<CpDepartmentTagRelation> deptTagRelationList) {
        //批量保存用户标签关联
        if (CollUtil.isNotEmpty(userTagRelationList)) {
            //查询已存在的用户标签关联
            List<CpUserTagRelation> cpUserTagRelations = userTagRelationService.list(Wrappers.<CpUserTagRelation>lambdaQuery()
                    .in(CpUserTagRelation::getUserid, userTagRelationList.stream().map(CpUserTagRelation::getUserid).collect(Collectors.toList())));
            //过滤已存在的用户标签关联
            userTagRelationList.removeIf(item -> cpUserTagRelations.stream().anyMatch(cp -> cp.getTagid().equals(item.getTagid()) && cp.getUserid().equals(item.getUserid())));
            if (CollUtil.isNotEmpty(userTagRelationList)) {
                userTagRelationService.saveBatch(userTagRelationList);
            }
        }
        //批量保存部门标签关联
        if (CollUtil.isNotEmpty(deptTagRelationList)) {
            //查询已存在的部门标签关联
            List<CpDepartmentTagRelation> cpDepartmentTagRelations = departmentTagRelationService.list(Wrappers.<CpDepartmentTagRelation>lambdaQuery()
                    .in(CpDepartmentTagRelation::getDepartmentid, deptTagRelationList.stream().map(CpDepartmentTagRelation::getDepartmentid).collect(Collectors.toList())));
            //过滤已存在的部门标签关联
            deptTagRelationList.removeIf(item -> cpDepartmentTagRelations.stream().anyMatch(cp -> cp.getTagid().equals(item.getTagid()) && cp.getDepartmentid().equals(item.getDepartmentid())));
            if (CollUtil.isNotEmpty(deptTagRelationList)) {
                departmentTagRelationService.saveBatch(deptTagRelationList);
            }
        }
    }

    /**
     * 新增标签成员
     */
    private void addTagUsers(String corpId, TagUsersQo qo, Map<Long, List<String>> tagUserMap, Map<Long, List<Long>> tagDeptMap, List<CpUserTagRelation> userTagRelationList, List<CpDepartmentTagRelation> deptTagRelationList, CpTag tag) {
        //设置agentId
        String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);

        //新增企微标签成员
        Result<TagAddOrRemoveUsersDto> result = tagFeign.addTagUsers(corpId, agentIdStr, qo);
        if (result.success()) {
            //新增用户标签关联标准表
            addUserTagRelation(qo, tagUserMap, userTagRelationList);

            //新增部门标签关联标准表
            addDeptTagRelation(qo, tagDeptMap, deptTagRelationList);

        } else {
            //获取当前类的代理类
            TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();
            //保存标签关联
            proxy.saveTagRelation(userTagRelationList, deptTagRelationList);
            throw new ServiceException(String.format("添加标签成员失败：%s", result.getMsg()), result.getCode());
        }
    }

    /**
     * 新增部门标签关联标准表
     */
    private void addDeptTagRelation(TagUsersQo qo, Map<Long, List<Long>> tagDeptMap, List<CpDepartmentTagRelation> deptTagRelationList) {
        if (CollUtil.isNotEmpty(qo.getPartyList())) {
            for (Long deptId : qo.getPartyList()) {
                //如果已经存在，则跳过
                List<Long> deptIdList = tagDeptMap.get(qo.getTagId());
                if (CollUtil.isNotEmpty(deptIdList) && deptIdList.contains(deptId)) {
                    continue;
                }

                CpDepartmentTagRelation entity = new CpDepartmentTagRelation();
                entity.setTagid(qo.getTagId());
                entity.setDepartmentid(deptId);
                deptTagRelationList.add(entity);
            }
        }
    }

    /**
     * 新增用户标签关联标准表
     */
    private void addUserTagRelation(TagUsersQo qo, Map<Long, List<String>> tagUserMap, List<CpUserTagRelation> userTagRelationList) {
        if (CollUtil.isNotEmpty(qo.getUserList())) {
            for (String userid : qo.getUserList()) {
                //如果已经存在，则跳过
                List<String> usridList = tagUserMap.get(qo.getTagId());
                if (CollUtil.isNotEmpty(usridList) && usridList.contains(userid)) {
                    continue;
                }

                CpUserTagRelation entity = new CpUserTagRelation();
                entity.setTagid(qo.getTagId());
                entity.setUserid(userid);
                userTagRelationList.add(entity);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelTagUsers(BatchTagUsersQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询标签
        List<CpTag> tags = super.listByIds(qo.getTagIdList());
        Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        //遍历删除标签成员
        for (Long tagid : qo.getTagIdList()) {
            CpTag tag = tagMap.get(tagid);
            TagUsersQo tagUsersQo = TagUsersQo.builder()
                    .tagId(tagid)
                    .userList(qo.getUserList())
                    .partyList(qo.getPartyList())
                    .build();

            //设置agentId
            String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);

            //删除企微标签
            Result<TagAddOrRemoveUsersDto> result = tagFeign.delTagUsers(corpId, agentIdStr, tagUsersQo);
            if (result.success()) {
                //删除关联标准表
                proxy.delRelationStandard(tagUsersQo);

            } else {
                throw new ServiceException(String.format("删除标签成员失败：%s", result.getMsg()), result.getCode());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddTag(List<TagSaveQo> list) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        //查询全部标签
        List<CpTag> tagList = super.list();
        List<String> tagNameList = tagList.stream().map(CpTag::getTagname).collect(Collectors.toList());

        List<CpTag> entityList = new ArrayList<>();
        for (TagSaveQo qo : list) {
            //不包含则新增
            if (!tagNameList.contains(qo.getTagname())) {
                //设置agentId
                String agentIdStr = Optional.ofNullable(qo.getAgentId()).map(String::valueOf).orElse(null);

                //新增企微标签
                Result<TagIdDTO> result = tagFeign.tagCreate(corpId, agentIdStr, qo);
                if (result.success()) {
                    CpTag entity = CpTag.builder()
                            .tagid(result.getResult().getTagid())
                            .tagname(qo.getTagname())
                            .agentId(qo.getAgentId()).build();
                    entityList.add(entity);

                } else {
                    //不为空则新增已经成功的数据
                    if (CollUtil.isNotEmpty(entityList)) {
                        proxy.saveStandard(entityList);
                    }
                    throw new ServiceException(String.format("创建标签失败：%s", result.getMsg()), result.getCode());
                }
            }
        }

        proxy.saveStandard(entityList);
    }

    /**
     * 新增标签标准表
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveStandard(List<CpTag> entityList) {
        super.saveBatch(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelTags(List<Long> tagidList) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        //查询标签列表
        List<CpTag> tags = super.listByIds(tagidList);
        Map<Long, Integer> tagMap = CollUtil.toMap(tags, null, CpTag::getTagid, CpTag::getAgentId);

        // 获取自动打标签任务关联的标签列表
        List<CpTag> ruleTagIds = this.baseMapper.getRuleTagIds();
        Set<Long> tagRuleIdSet = ruleTagIds.stream().map(CpTag::getTagid).collect(Collectors.toSet());
        List<Long> idList = new ArrayList<>();
        for (Long tagId : tagidList) {

            //判断是否关联自动打标签
            if(tagRuleIdSet.contains(tagId)){
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("无法删除标签：标签已用于'自动打标签'任务，请先前往相关任务移除该标签");
            }
            //设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagId)).map(String::valueOf).orElse(null);

            //删除企微标签
            Result<Object> result = tagFeign.tagDelete(corpId, agentIdStr, tagId);
            if (result.success()) {
                idList.add(tagId);

            } else {
                //不为空则删除已经成功的数据
                if (CollUtil.isNotEmpty(idList)) {
                    //删除标签和关联
                    proxy.delTagAndRelation(idList);
                }
                throw new ServiceException(String.format("删除标签失败：%s", result.getMsg()), result.getCode());
            }
        }

        //删除标签和关联
        proxy.delTagAndRelation(idList);
    }

    /**
     * 删除标签和关联
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void delTagAndRelation(List<Long> idList) {
        //删除标签标准表
        super.removeByIds(idList);
        //删除用户标签关联
        userTagRelationService.delByTagidList(idList);
        //删除部门标签关联
        departmentTagRelationService.delByTagidList(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelTagsByTagNameList(List<String> tagNameList) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询全部标签
        List<CpTag> tagList = super.list();
        Map<String, CpTag> tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagname, Function.identity()));

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        List<Long> idList = new ArrayList<>();
        for (String tagName : tagNameList) {
            CpTag tag = tagMap.get(tagName);
            //存在则删除
            if (tag != null) {
                //设置agentId
                String agentIdStr = Optional.ofNullable(tagMap.get(tagName).getAgentId()).map(String::valueOf).orElse(null);

                //删除企微标签
                Result<Object> result = tagFeign.tagDelete(corpId, agentIdStr, tag.getTagid());
                if (result.success()) {
                    idList.add(tag.getTagid());

                } else {
                    //不为空则新增已经成功的数据
                    if (CollUtil.isNotEmpty(idList)) {
                        //删除标签和关联
                        proxy.delTagAndRelation(idList);
                    }
                    throw new ServiceException(String.format("删除标签失败：%s", result.getMsg()), result.getCode());
                }
            }
        }

        //删除标签和关联
        proxy.delTagAndRelation(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTagNameByOldTagName(UpdateTagNameByOldNameQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //根据旧标签名查询标签
        CpTag tag = this.getTagByTagName(qo.getOldTagname());
        if (tag == null) {
            throw ExceptionCodeEnum.TAG_NOT_EXIST.toServiceException();
        }

        TagSaveQo saveQo = TagSaveQo.builder()
                .tagid(tag.getTagid())
                .tagname(qo.getNewTagname())
                .build();

        //设置agentId
        String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);

        //更新企微标签
        Result<Object> result = tagFeign.tagUpdate(corpId, agentIdStr, saveQo);
        if (result.hasError()) {
            throw new ServiceException(String.format("更新标签名字失败：%s", result.getMsg()), result.getCode());
        }

        //更新标签标准表
        tag.setTagname(qo.getNewTagname());
        super.updateById(tag);
    }

    @Override
    public CpTag getTagByTagName(String tagName) {
        return super.getOne(new LambdaQueryWrapper<CpTag>().eq(CpTag::getTagname, tagName));
    }

    @Override
    public Integer queryUserTagAuth(UserTagAuthQo qo) {
        //查询用户关联的标签列表
        List<CpUserTagRelation> userTagRelationList = userTagRelationService.listTagRelationByUserid(qo.getUserid());
        List<Long> tagidList = userTagRelationList.stream().map(CpUserTagRelation::getTagid).collect(Collectors.toList());
        YnEnum result = YnEnum.NO;
        switch (qo.getMatchType()) {
            case ALL:
                //用户标签列表中包含全部入参标签
                if (CollUtil.isNotEmpty(tagidList) && (new HashSet<>(tagidList).containsAll(qo.getTagidList()))) {
                    result = YnEnum.YES;
                }
                break;
            case ANY:
                //用户标签列表中包含任意入参标签
                if (CollUtil.isNotEmpty(tagidList) && (tagidList.stream().anyMatch(item -> qo.getTagidList().contains(item)))) {
                    result = YnEnum.YES;
                }
                break;
            case NONE:
                //用户标签列表中不包含入参标签
                if (CollUtil.isEmpty(tagidList)) {
                    result = YnEnum.YES;
                }
                if (CollUtil.isNotEmpty(tagidList) && tagidList.stream().noneMatch(item -> qo.getTagidList().contains(item))) {
                    result = YnEnum.YES;
                }
                break;
            default:
                break;
        }
        return result.getValue();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTagUsersByMsg(WxCpXmlMessage wxMessage) {
        //处理用户标签关联
        processUserTagRelation(wxMessage);
        //处理部门标签关联
        processDeptTagRelation(wxMessage);
        log.info("通讯录回调-更新标签成员[{}]成功", wxMessage.getTagId());
    }

    @Override
    public List<TagInfoVo> listTagInfo() {
        //查询全部标签
        List<CpTag> tagList = super.list();

        //查询常用标签
        List<CpCommonUseTag> list = commonUseTagService.list();
        List<Long> commonUseTagIdList = list.stream().map(CpCommonUseTag::getTagid).collect(Collectors.toList());

        //查询用户标签关联，统计每个标签包含的用户数量
        List<CpUserTagRelation> userTagRelations = userTagRelationService.list();
        Map<Long, Long> tagUserMap = userTagRelations.stream().collect(Collectors.groupingBy(CpUserTagRelation::getTagid, Collectors.counting()));

        //查询部门标签关联，统计每个标签包含的部门数量
        List<CpDepartmentTagRelation> departmentTagRelations = departmentTagRelationService.list();
        Map<Long, Long> tagDeptMap = departmentTagRelations.stream().collect(Collectors.groupingBy(CpDepartmentTagRelation::getTagid, Collectors.counting()));

        List<TagInfoVo> tagInfoVos = BeanUtil.copyToList(tagList, TagInfoVo.class);
        for (TagInfoVo tagInfoVo : tagInfoVos) {
            //设置常用标签
            if (commonUseTagIdList.contains(tagInfoVo.getTagid())) {
                tagInfoVo.setIsCommonUse(YnEnum.YES.getValue());
            }

            //设置包含成员数量
            if (tagUserMap.containsKey(tagInfoVo.getTagid())) {
                tagInfoVo.setUserCount(tagUserMap.get(tagInfoVo.getTagid()));
            }

            //设置包含部门数量
            if (tagDeptMap.containsKey(tagInfoVo.getTagid())) {
                tagInfoVo.setDepartmentCount(tagDeptMap.get(tagInfoVo.getTagid()));
            }
        }

        return tagInfoVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void commonUseTagOperate(CommonUseTagOperateQo qo) {
        //新增常用标签
        if (YnEnum.YES.getValue() == qo.getIsCommonUse()) {
            commonUseTagService.save(CpCommonUseTag.builder().tagid(qo.getTagid()).build());

            //删除常用标签
        } else {
            commonUseTagService.removeById(qo.getTagid());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUsersUpdateTag(BatchTagUsersQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询标签
        List<CpTag> tags = super.listByIds(qo.getTagIdList());
        Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        List<String> userList = qo.getUserList();
        List<Long> partyList = qo.getPartyList();

        //移除用户之前关联的标签
        removeUserTag(corpId, userList);

        //移除部门之前关联的标签
        removeDeptTag(corpId, partyList);

        //遍历添加标签成员
        addTagRelation(corpId, qo, Collections.emptyMap(), Collections.emptyMap(), tagMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateTag(TagSaveQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //id不为空含则新增
        if (qo.getTagid() == null) {
            //查询标签名称是否存在
            LambdaQueryWrapper<CpTag> queryWrapper = Wrappers.lambdaQuery(CpTag.class);
            queryWrapper.eq(CpTag::getTagname, qo.getTagname());
            List<CpTag> tagList = super.list(queryWrapper);
            if (CollUtil.isNotEmpty(tagList)) {
                throw ExceptionCodeEnum.TAG_EXIST.toServiceException();
            }

            //设置agentId
            String agentIdStr = Optional.ofNullable(qo.getAgentId()).map(String::valueOf).orElse(null);

            //新增企微标签
            Result<TagIdDTO> result = tagFeign.tagCreate(corpId, agentIdStr, qo);
            if (result.success()) {
                CpTag entity = CpTag.builder().tagid(result.getResult().getTagid()).tagname(qo.getTagname()).agentId(qo.getAgentId()).build();
                //新增标准表
                super.save(entity);
            } else {
                throw new ServiceException(String.format("创建标签失败：%s", result.getMsg()), result.getCode());
            }

        } else {
            //查询标签
            CpTag tag = super.getById(qo.getTagid());
            if (tag == null) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("标签不存在").toServiceException();
            }
            //设置agentId
            String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);

            //id为空则更新
            Result<Object> result = tagFeign.tagUpdate(corpId, agentIdStr, qo);
            if (result.success()) {
                CpTag entity = CpTag.builder().tagid(qo.getTagid()).tagname(qo.getTagname()).build();
                //更新标准表
                super.updateById(entity);

            } else {
                throw new ServiceException(String.format("更新标签失败：%s", result.getMsg()), result.getCode());
            }
        }
    }

    @Override
    public List<CpTag> listByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return super.listByIds(ids);
    }

    /**
     * 同步标签关联的成员信息
     *
     * @param dto 标签用户信息
     * @return 同步失败的成员列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> setTagUserToWx(TagUserSetDTO dto) {

        Result<TagUserDto> result = tagFeign.tagGet(agentProperties.getCorpId(), null, dto.getTagId());

        TagUserDto tagUser = result.getResult();

        if (result.hasError()) {
            log.error("从企业微信获取标签（{}）成员信息失败：{}", dto.getTagId(), result.getMsg());
            return dto.getUserIdList();
        }

        //同步标签关联的用户数据到本地标准库
        List<TagUserDto.User> userlist = tagUser.getUserlist();
        List<String> userInTag = new ArrayList<>();
        if (userlist != null) {
            userInTag.addAll(userlist.stream().map(TagUserDto.User::getUserid).collect(Collectors.toList()));
        }

        List<String> errorUserList = new ArrayList<>();
        // 增量增加标签关联的成员 1：增量；0：全量
        if ("1".equals(dto.getSyncType())) {
            List<String> userIds = dto.getUserIdList().stream().filter(u -> !userInTag.contains(u)).collect(Collectors.toList());
            // 分批处理，企业微信接口一次最多能设置1000个标签关联成员
            int batchSize = 1000;
            IntStream.iterate(0, i -> i + batchSize)
                    .limit((userIds.size() + batchSize - 1) / batchSize)
                    .forEach(start -> {
                        List<String> subList = userIds.stream()
                                .skip(start)
                                .limit(batchSize)
                                .collect(Collectors.toList());
                        TagUsersQo qo = TagUsersQo.builder().tagId(dto.getTagId()).userList(subList).build();
                        Result<TagAddOrRemoveUsersDto> feignResult
                                = tagFeign.addTagUsers(agentProperties.getCorpId(), agentProperties.getAddressBookAgentId(), qo);
                        if (feignResult.hasError()) {
                            log.error("tagFeign设置标签{}成员{}失败：{}", dto.getTagId(), subList, feignResult.getMsg());
                            errorUserList.addAll(subList);
                        }
                        if (feignResult.getResult() != null && StringUtils.isNotEmpty(feignResult.getResult().getInvalidUsers())) {
                            errorUserList.addAll(Arrays.asList(feignResult.getResult().getInvalidUsers().split("\\|")));
                        }
                    });
        } else {
            //TODO 全量暂时不考虑
        }

        log.info("设置标签（{}）关联成员信息到企业微信，关联失败用户：{}", dto.getTagId(), errorUserList);
        //最后将标签关联的成员数据到本地标准库
        syncTagRelationFromWx(dto.getTagId());
        return errorUserList;
    }

    /**
     * 从微信同步标签关联的成员信息
     *
     * @param tagId 标签id
     */
    @Override
    public void syncTagRelationFromWx(Long tagId) {
        Result<TagUserDto> result = tagFeign.tagGet(agentProperties.getCorpId(), null, tagId);

        TagUserDto tagUser = result.getResult();

        if (result.hasError()) {
            log.error("从企业微信获取标签（{}）成员信息失败：{}", tagId, result.getMsg());
            return;
        }

        //同步标签关联的用户数据到本地标准库
        List<TagUserDto.User> userlist = tagUser.getUserlist();
        if (userlist != null) {
            List<String> userIdList = userlist.stream().map(TagUserDto.User::getUserid).collect(Collectors.toList());
            userTagRelationService.syncTagUserFromWx(tagId, userIdList);
        }

        //同步标签关联的部门数据到本地标准库
        List<Long> partyIdlist = tagUser.getPartylist();
        if (partyIdlist != null) {
            departmentTagRelationService.syncTagDeptFromWx(tagId, partyIdlist);
        }
    }

    /**
     * 删除部门标签
     */
    private void removeDeptTag(String corpId, List<Long> partyList) {
        if (CollUtil.isEmpty(partyList)) {
            return;
        }

        //获取部门标签关联列表
        List<CpDepartmentTagRelation> departmentTagRelationList = departmentTagRelationService.list(new LambdaQueryWrapper<CpDepartmentTagRelation>().in(CpDepartmentTagRelation::getDepartmentid, partyList));
        Map<Long, List<Long>> tagDeptMap = departmentTagRelationList.stream().collect(Collectors.groupingBy(CpDepartmentTagRelation::getTagid, Collectors.mapping(CpDepartmentTagRelation::getDepartmentid, Collectors.toList())));

        //查询标签
        List<CpTag> tags = this.listByIds(new ArrayList<>(tagDeptMap.keySet()));
        Map<Long, Integer> tagMap = CollUtil.toMap(tags, null, CpTag::getTagid, CpTag::getAgentId);

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        //删除成功的列表
        List<Long> successTagidList = new ArrayList<>();
        tagDeptMap.forEach((tagid, partyIdList) -> {
            //设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagid)).map(String::valueOf).orElse(null);
            //删除企微部门标签关联
            Result<TagAddOrRemoveUsersDto> result = tagFeign.delTagUsers(corpId, agentIdStr, TagUsersQo.builder().tagId(tagid).partyList(partyIdList).build());
            if (result.hasError()) {
                //删除已经处理成功的标签关联
                proxy.removeDeptTagStandard(partyList, successTagidList);
                throw new ServiceException(String.format("删除企微部门标签关联失败：%s", result.getMsg()), result.getCode());
            } else {
                successTagidList.add(tagid);
            }
        });
        //删除部门标签关联
        proxy.removeDeptTagStandard(partyList, successTagidList);
    }

    /**
     * 删除部门标签关联
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void removeDeptTagStandard(List<Long> partyList, List<Long> successTagidList) {
        if (CollUtil.isNotEmpty(successTagidList)) {
            LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class)
                    .in(CpDepartmentTagRelation::getTagid, successTagidList)
                    .in(CpDepartmentTagRelation::getDepartmentid, partyList);
            departmentTagRelationService.remove(queryWrapper);
        }
    }

    /**
     * 删除用户标签
     */
    private void removeUserTag(String corpId, List<String> userList) {
        if (CollUtil.isEmpty(userList)) {
            return;
        }

        //获取用户标签关联列表
        List<CpUserTagRelation> userTagRelationList = userTagRelationService.listTagRelationByUseridList(userList);
        Map<Long, List<String>> tagUserMap = userTagRelationList.stream().collect(Collectors.groupingBy(CpUserTagRelation::getTagid, Collectors.mapping(CpUserTagRelation::getUserid, Collectors.toList())));

        //查询标签
        List<CpTag> tags = this.listByIds(new ArrayList<>(tagUserMap.keySet()));
        Map<Long, Integer> tagMap = CollUtil.toMap(tags, null, CpTag::getTagid, CpTag::getAgentId);

        //获取当前类的代理类
        TagServiceImpl proxy = (TagServiceImpl) AopContext.currentProxy();

        //删除成功的列表
        List<Long> successTagidList = new ArrayList<>();
        tagUserMap.forEach((tagid, useridList) -> {
            //设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagid)).map(String::valueOf).orElse(null);
            //删除企微用户标签关联
            Result<TagAddOrRemoveUsersDto> result = tagFeign.delTagUsers(corpId, agentIdStr, TagUsersQo.builder().tagId(tagid).userList(useridList).build());
            if (result.hasError()) {
                //删除已经处理成功的标签关联
                proxy.removeUserTagStandard(userList, successTagidList);
                throw new ServiceException(String.format("删除企微用户标签关联失败：%s", result.getMsg()), result.getCode());
            } else {
                successTagidList.add(tagid);
            }
        });
        //删除用户标签关联
        proxy.removeUserTagStandard(userList, successTagidList);
    }

    /**
     * 删除用户标签关联
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void removeUserTagStandard(List<String> userList, List<Long> successTagidList) {
        if (CollUtil.isNotEmpty(successTagidList)) {
            LambdaQueryWrapper<CpUserTagRelation> queryWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class)
                    .in(CpUserTagRelation::getTagid, successTagidList)
                    .in(CpUserTagRelation::getUserid, userList);
            userTagRelationService.remove(queryWrapper);
        }
    }

    /**
     * 处理用户标签关联
     */
    private void processUserTagRelation(WxCpXmlMessage wxMessage) {
        List<String> addUseridList = CharSequenceUtil.split(wxMessage.getAddUserItems(), ",");
        //新增用户标签关联
        if (CollUtil.isNotEmpty(addUseridList)) {
            List<CpUserTagRelation> userTagRelationList = new ArrayList<>();
            for (String userid : addUseridList) {
                CpUserTagRelation userTagRelation = CpUserTagRelation.builder()
                        .userid(userid)
                        .tagid(Long.valueOf(wxMessage.getTagId()))
                        .build();
                userTagRelationList.add(userTagRelation);
            }
            userTagRelationService.saveBatch(userTagRelationList);
        }

        List<String> delUseridList = CharSequenceUtil.split(wxMessage.getDelUserItems(), ",");
        //删除用户标签关联
        if (CollUtil.isNotEmpty(delUseridList)) {

            //查询设置的无需删除标识，如果存在则去除该用户
            List<String> excludUseridList = redisUtils.lGetAll(CommonServiceRedisKeys.COMMON_SERVICE_CALLBACK_USER_TAG_EXCLUDE_USERID);
            if (CollUtil.isNotEmpty(excludUseridList)) {
                delUseridList.removeAll(excludUseridList);
            }

            if (CollUtil.isEmpty(delUseridList)) {
                return;
            }

            LambdaQueryWrapper<CpUserTagRelation> queryWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class);
            queryWrapper.eq(CpUserTagRelation::getTagid, wxMessage.getTagId())
                    .in(CpUserTagRelation::getUserid, delUseridList);
            userTagRelationService.remove(queryWrapper);
        }
    }

    /**
     * 处理部门标签关联
     */
    private void processDeptTagRelation(WxCpXmlMessage wxMessage) {
        List<String> addDeptIdList = CharSequenceUtil.split(wxMessage.getAddPartyItems(), ",");
        //新增部门标签关联
        if (CollUtil.isNotEmpty(addDeptIdList)) {
            List<CpDepartmentTagRelation> deptTagRelationList = new ArrayList<>();
            for (String deptId : addDeptIdList) {
                CpDepartmentTagRelation userTagRelation = CpDepartmentTagRelation.builder()
                        .departmentid(Long.valueOf(deptId))
                        .tagid(Long.valueOf(wxMessage.getTagId()))
                        .build();
                deptTagRelationList.add(userTagRelation);
            }
            departmentTagRelationService.saveBatch(deptTagRelationList);
        }

        List<String> delDeptIdStrList = CharSequenceUtil.split(wxMessage.getDelPartyItems(), ",");
        //删除用户标签关联
        if (CollUtil.isNotEmpty(delDeptIdStrList)) {
            List<Long> delDeptIdList = delDeptIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
            LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
            queryWrapper.eq(CpDepartmentTagRelation::getTagid, wxMessage.getTagId())
                    .in(CpDepartmentTagRelation::getDepartmentid, delDeptIdList);
            departmentTagRelationService.remove(queryWrapper);
        }
    }

    /**
     * 删除标签关联标准表
     */
    @Transactional(rollbackFor = Exception.class)
    public void delRelationStandard(TagUsersQo qo) {
        //删除用户标签关联标准表
        if (CollUtil.isNotEmpty(qo.getUserList())) {
            LambdaQueryWrapper<CpUserTagRelation> queryWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class);
            queryWrapper.eq(CpUserTagRelation::getTagid, qo.getTagId());
            queryWrapper.in(CpUserTagRelation::getUserid, qo.getUserList());
            userTagRelationService.remove(queryWrapper);
        }
        //删除部门标签关联标准表
        if (CollUtil.isNotEmpty(qo.getPartyList())) {
            LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
            queryWrapper.eq(CpDepartmentTagRelation::getTagid, qo.getTagId());
            queryWrapper.in(CpDepartmentTagRelation::getDepartmentid, qo.getPartyList());
            departmentTagRelationService.remove(queryWrapper);
        }
    }



}
