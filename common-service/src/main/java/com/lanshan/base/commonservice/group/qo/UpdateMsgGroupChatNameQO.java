package com.lanshan.base.commonservice.group.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 更新群聊名称QO
 */
@Data
@ApiModel(value = "更新群聊名称QO")
public class UpdateMsgGroupChatNameQO {

    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @ApiModelProperty(value = "应用 id", required = true)
    private String agentId;

    @ApiModelProperty(value = "群聊 id", required = true)
    private String chatId;

    @ApiModelProperty(value = "群聊名称", required = true)
    private String chatName;

    @ApiModelProperty(value = "群聊类型 1 班级 2 课程 3 部门", required = true)
    private Integer type;

}
