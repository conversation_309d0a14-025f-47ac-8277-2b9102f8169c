package com.lanshan.base.commonservice.standardapp.enums;

import lombok.Getter;

@Getter
public enum RefundLogOpTypeEnum {
    IMPORT("数据导入"),
    ADD_BANK_CARD("添加银行卡"),
    UPDATE_BANK_CARD("更换银行卡"),
    DONATE("捐赠支出"),
    UPDATE_BALANCE("设置校园网余额"),
    UPDATE_BALANCE_CARD("设置一卡通余额"),
    ;
    private final String mapping;

    RefundLogOpTypeEnum(String mapping) {
        this.mapping = mapping;
    }

}
