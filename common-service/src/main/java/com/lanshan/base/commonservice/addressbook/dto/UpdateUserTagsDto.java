package com.lanshan.base.commonservice.addressbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@ApiModel("更新标签")
public class UpdateUserTagsDto {
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("企业idid")
    private String corpId;
    @ApiModelProperty("旧的一级身份名字")
    private String oldFidentify;
    @ApiModelProperty("旧的2级身份名字")
    private String oldSidentify;
    @ApiModelProperty("新的一级身份名字")
    private String newFidentify;
    @ApiModelProperty("新的2级身份名字")
    private String newSidentify;
}
