package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.converter.StdBookArrearsViolationConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdBookArrearsViolationDao;
import com.lanshan.base.commonservice.standardapp.entity.StdBookArrearsViolation;
import com.lanshan.base.commonservice.standardapp.service.StdBookArrearsViolationService;
import com.lanshan.base.commonservice.standardapp.vo.StdBookArrearsViolationVO;
import org.springframework.stereotype.Service;

/**
 * 图书欠费违章表(StdBookArrearsViolation)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdBookArrearsViolationService")
public class StdBookArrearsViolationServiceImpl extends ServiceImpl<StdBookArrearsViolationDao, StdBookArrearsViolation> implements StdBookArrearsViolationService {

    @Override
    public StdBookArrearsViolationVO getBookArrearsViolation() {
        String userId = SecurityContextHolder.getUserId();
        LambdaQueryWrapper<StdBookArrearsViolation> qw = new LambdaQueryWrapper<>();
        qw.eq(StdBookArrearsViolation::getUserId, userId);
        StdBookArrearsViolation bookArrearsViolation = this.getOne(qw);
        if (ObjectUtil.isNull(bookArrearsViolation)) {
            return null;
        }
        return StdBookArrearsViolationConverter.INSTANCE.toVO(bookArrearsViolation);
    }
}

