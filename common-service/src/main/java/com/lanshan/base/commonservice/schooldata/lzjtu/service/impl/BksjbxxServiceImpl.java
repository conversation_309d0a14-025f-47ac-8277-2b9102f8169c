package com.lanshan.base.commonservice.schooldata.lzjtu.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.commonservice.schooldata.lzjtu.dao.BksjbxxDao;
import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Bksjbxx;
import com.lanshan.base.commonservice.schooldata.lzjtu.properties.LzjtuOpenApiProperties;
import com.lanshan.base.commonservice.schooldata.lzjtu.service.BksjbxxService;
import demo.DSDemo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 本科生信息(Bksjbxx)表服务实现类
 */
@Service
@Slf4j
public class BksjbxxServiceImpl extends ServiceImpl<BksjbxxDao, Bksjbxx> implements BksjbxxService {

    @Resource
    private LzjtuOpenApiProperties lzjtuOpenApiProperties;

    @Resource
    private BksjbxxDao bksjbxxDao;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncBksjbxx() {
        String key = lzjtuOpenApiProperties.getKey();
        String secret = lzjtuOpenApiProperties.getSecret();
        String url = lzjtuOpenApiProperties.getBaseUrl() + lzjtuOpenApiProperties.getBksjbxx();

        try {
            String result = DSDemo.getData(url, key, secret);
            String value = JacksonUtils.toObj(result).get("value").toString();

            List<Bksjbxx> entityList = JacksonUtils.toObj(value, new TypeReference<>() {
            });

            if (CollUtil.isNotEmpty(entityList)) {
                //清空数据
                bksjbxxDao.truncate();
                //新增数据
                super.saveBatch(entityList);

                log.info("同步本科生信息{}条", entityList.size());
            }
        } catch (Exception e) {
            log.error("同步本科生信息失败", e);
        }
    }
}

