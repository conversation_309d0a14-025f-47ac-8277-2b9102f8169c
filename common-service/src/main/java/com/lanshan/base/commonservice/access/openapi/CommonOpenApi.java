package com.lanshan.base.commonservice.access.openapi;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.JsonObject;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.qo.user.UserInfoPageQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/open/access/common")
@Api(tags = "公共接口API", hidden = true)
public class CommonOpenApi extends OpenApi {

    @Resource
    private UserService userService;

    @ApiOperation("文本消息推送")
    @PostMapping("/send/text/v1")
    public Result<WxCpMessageSendResult> sendTextMessage(@RequestBody WxCpMessage wxCpMessage) throws WxErrorException {
        wxCpMessage.setMsgType("text");
        WxCpMessageSendResult result = getMessageService().send(wxCpMessage);
        return Result.build(result);
    }

    @ApiOperation("获取人员列表")
    @GetMapping("/get/cpUserList/v1")
    public Result<List<Object>> getCpUserList() {
        UserInfoPageQo qo = new UserInfoPageQo();
        qo.setPage(1L);
        qo.setSize((long) Integer.MAX_VALUE);
        qo.setDesensitization(false);
        Page<UserInfoVo> page = userService.pageUserInfo(qo);
        List<Object> result = page.getRecords().stream()
                .map(userInfoVo -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("userId", userInfoVo.getUserid());
                    map.put("name", userInfoVo.getName());
                    map.put("email", userInfoVo.getEmail());
                    map.put("userType", userInfoVo.getUserType());
                    map.put("mobile", userInfoVo.getMobile());
                    map.put("departmentName", userInfoVo.getMainDepartmentName());
                    map.put("departmentVoList", userInfoVo.getDepartmentVoList());
                    return map;
                })
                .collect(Collectors.toList());
        return Result.build(result);
    }

    @ApiOperation("获取审批详情")
    @GetMapping("/get/getApprovalDetail/v1")
    public Result<Object> getApprovalDetail(@RequestParam() String spno) throws WxErrorException {
        //2025年3月7日17:56:46
        // 使用第三方库获取详情有异常（序列化报错） class me.chanjar.weixin.cp.bean.oa.WxCpApprovalApplier declares multiple JSON fields named userid
        // 所以使用JSONObject 进行解析
        //getWxCpOaService().getApprovalDetail(spno)
        WxCpService wxCpService = getWxCpService();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("sp_no", spno);
        String url = wxCpService.getWxCpConfigStorage().getApiUrl("/cgi-bin/oa/getapprovaldetail");
        String responseContent = wxCpService.post(url, jsonObject.toString());
        log.info("获取审批详情返回结果：{}", responseContent);
        JSONObject responseJson = new JSONObject(responseContent);
        if (responseJson.getInt("errcode") != 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(responseJson.getStr("errmsg"));
        }
        JSONObject info = responseJson.getJSONObject("info");
        Map<String, Object> detail = new HashMap<>();
        detail.put("spno", info.getStr("sp_no"));
        detail.put("spName", info.getStr("sp_name"));
        UserInfoVo userInfoVo = userService.getUserByUserid(info.getByPath("applyer.userid", String.class));
        if (userInfoVo != null) {
            detail.put("applyUserName", userInfoVo.getName());
            detail.put("applyUserId", userInfoVo.getUserid());
            detail.put("applyUserType", userInfoVo.getUserType());
        }
        detail.put("applyTime", info.getLong("apply_time"));
        detail.put("spStatus", info.getInt("sp_status"));
        // 获取 SpRecords 集合中 details 属性的最大 spTime 值
        JSONArray spRecords = info.getJSONArray("sp_record");
        long maxSpTime = 0L;
        if (spRecords != null) {
            maxSpTime = spRecords.stream()
                    .flatMap(record -> {
                        JSONArray details = ((JSONObject) record).getJSONArray("details");
                        return details != null ? details.stream() : Stream.empty();
                    })
                    .mapToLong(detailItem -> ((JSONObject) detailItem).getLong("sptime"))
                    .max()
                    .orElse(0L);
        }
        detail.put("updateTime", maxSpTime);
        return Result.build(detail);
    }
}
