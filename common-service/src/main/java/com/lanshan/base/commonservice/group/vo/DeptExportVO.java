package com.lanshan.base.commonservice.group.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.lanshan.base.commonservice.group.converter.excel.ChatStatusExportConverter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "部门群聊导出VO")
public class DeptExportVO {

    @ExcelProperty(value = "部门", order = 0)
    private String deptName;

    @ExcelProperty(value = "可创建群成员", order = 1)
    private String canCreateName;

    @ExcelProperty(value = "学工号", order = 2)
    private String canCreateGh;

    @ExcelProperty(value = "群名称", order = 3)
    private String chatName;

    @ExcelProperty(value = "群聊状态", order = 4, converter = ChatStatusExportConverter.class)
    private String chatId;
}
