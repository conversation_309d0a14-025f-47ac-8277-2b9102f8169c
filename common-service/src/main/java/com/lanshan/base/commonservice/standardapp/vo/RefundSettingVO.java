package com.lanshan.base.commonservice.standardapp.vo;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "RefundSettingVO", description = "退费设置视图对象")
public class RefundSettingVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "转换日期")
    private Date transferDate;

    @ApiModelProperty(value = "禁止日期(不允许学生修改信息)")
    private Date disableDate;

    @ApiModelProperty(value = "退费日期")
    private Date refundDate;

    @com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = PreserveWhitespaceDeserializer.class)
    @ApiModelProperty(value = "捐赠说明")
    private String donateRemark;

    @com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = PreserveWhitespaceDeserializer.class)
    @ApiModelProperty(value = "捐赠提示")
    private String donateTip;

    // 然后在项目中添加这个自定义反序列化器
    public static class PreserveWhitespaceDeserializer extends JsonDeserializer<String> {
        @Override
        public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            return p.getValueAsString(); // 不做任何处理，保留原始字符串
        }
    }

}

