package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppStat;
import com.lanshan.base.commonservice.workbench.vo.AppStatOverviewVO;
import com.lanshan.base.commonservice.workbench.vo.AppStatTimeScaleVO;
import com.lanshan.base.commonservice.workbench.vo.WbAppStatVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 应用统计(WbAppStat)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppStatConverter {

    WbAppStatConverter INSTANCE = Mappers.getMapper(WbAppStatConverter.class);

    WbAppStatVO toVO(WbAppStat entity);

    WbAppStat toEntity(WbAppStatVO vo);

    List<WbAppStatVO> toVO(List<WbAppStat> entityList);

    List<WbAppStat> toEntity(List<WbAppStatVO> voList);


    @Mapping(source = "totalUserClick", target = "todayUserClick")
    @Mapping(source = "totalStuClick", target = "todayStuClick")
    @Mapping(source = "totalTchClick", target = "todayTchClick")
    @Mapping(source = "totalUserUsed", target = "todayUserUsed")
    @Mapping(source = "totalStuUsed", target = "todayStuUsed")
    @Mapping(source = "totalTchUsed", target = "todayTchUsed")
    @Mapping(source = "totalStuUndergraduateClick", target = "todayStuUndergraduateClick")
    @Mapping(source = "totalStuUndergraduateUsed", target = "todayStuUndergraduateUsed")
    @Mapping(source = "totalStuMasterClick", target = "todayStuMasterClick")
    @Mapping(source = "totalStuMasterUsed", target = "todayStuMasterUsed")
    @Mapping(source = "totalOtherClick", target = "todayOtherClick")
    @Mapping(source = "totalOtherUsed", target = "todayOtherUsed")
    void dayTimeScaleVOToOverviewVO(AppStatTimeScaleVO timeScaleVO, @MappingTarget AppStatOverviewVO overviewVO);

    @Mapping(source = "totalUserClick", target = "userMonthClick")
    @Mapping(source = "totalStuClick", target = "stuMonthClick")
    @Mapping(source = "totalTchClick", target = "tchMonthClick")
    @Mapping(source = "totalUserUsed", target = "monthUsed")
    @Mapping(source = "totalStuUsed", target = "stuMonthUsed")
    @Mapping(source = "totalTchUsed", target = "tchMonthUsed")
    @Mapping(source = "totalStuUndergraduateClick", target = "stuUndergraduateMonthClick")
    @Mapping(source = "totalStuUndergraduateUsed", target = "stuUndergraduateMonthUsed")
    @Mapping(source = "totalStuMasterClick", target = "stuMasterMonthClick")
    @Mapping(source = "totalStuMasterUsed", target = "stuMasterMonthUsed")
    @Mapping(source = "totalOtherClick", target = "otherMonthClick")
    @Mapping(source = "totalOtherUsed", target = "otherMonthUsed")
    void monthTimeScaleVOToOverviewVO(AppStatTimeScaleVO timeScaleVO, @MappingTarget AppStatOverviewVO overviewVO);

}


