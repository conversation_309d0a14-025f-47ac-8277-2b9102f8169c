package com.lanshan.base.commonservice.schooldata.whut.sms;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.lanshan.base.commonservice.schooldata.whut.erp package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.lanshan.base.commonservice.schooldata.whut.erp
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link SaveSmsInfo }
     */
    public SaveSmsInfo createSaveSmsInfo() {
        return new SaveSmsInfo();
    }

    /**
     * Create an instance of {@link SaveSmsInfoResponse }
     */
    public SaveSmsInfoResponse createSaveSmsInfoResponse() {
        return new SaveSmsInfoResponse();
    }

}
