package com.lanshan.base.commonservice.addressbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.api.dto.user.DeptTagDto;
import com.lanshan.base.commonservice.addressbook.entity.CpDepartmentTagRelation;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 部门标签-关联表(TagDepartmentRelation)数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Mapper
public interface CpDepartmentTagRelationMapper extends BaseMapper<CpDepartmentTagRelation> {

    //清空表
    @Update("TRUNCATE TABLE cp_department_tag_relation")
    void truncate();

    //复制备份表的数据到标准表
    @Insert("INSERT INTO cp_department_tag_relation SELECT * FROM cp_department_tag_relation_bak")
    void copyBakToStandard();

    /**
     * 查询部门与对应的标签名称拼接
     *
     * @param deptIds 部门ids
     * @return DeptTagNameConcatDto
     */
    List<DeptTagDto> listDeptTagNameConcat(List<Long> deptIds);
}

