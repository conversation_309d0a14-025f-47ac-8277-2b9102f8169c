package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.json.JSONUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.file.FileInfo;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.minio.service.MinIOService;
import com.lanshan.base.commonservice.standardapp.dao.RefundUserDao;
import com.lanshan.base.commonservice.standardapp.dto.RefundUserExcelDTO;
import com.lanshan.base.commonservice.standardapp.entity.RefundOpLog;
import com.lanshan.base.commonservice.standardapp.entity.RefundUser;
import com.lanshan.base.commonservice.standardapp.enums.RefundLogOpTypeEnum;
import com.lanshan.base.commonservice.standardapp.qo.RefundUserQO;
import com.lanshan.base.commonservice.standardapp.service.RefundOpLogService;
import com.lanshan.base.commonservice.standardapp.service.RefundSettingService;
import com.lanshan.base.commonservice.standardapp.service.RefundUserService;
import com.lanshan.base.commonservice.standardapp.vo.RefundSettingVO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserDetailVO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserVO;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 退费用户服务实现类
 */
@Service
public class RefundUserServiceImpl extends ServiceImpl<RefundUserDao, RefundUser> implements RefundUserService {

    @Resource
    private MinIOService minIOService;

    @Resource
    private RefundOpLogService refundOpLogService;

    @Resource
    private RefundSettingService refundSettingService;

    @Override
    public IPage<RefundUserVO> getRefundUserList(RefundUserQO qo) {
        IPage<RefundUserVO> page = new Page<>(qo.getPage(), qo.getSize());
        page.setRecords(baseMapper.getRefundUserList(page, qo));
        return page;
    }

    @Override
    public void export(HttpServletResponse response, RefundUserQO qo) {
        IPage<RefundUserVO> page = new Page<>(1, Integer.MAX_VALUE);
        List<RefundUserVO> refundUserList = baseMapper.getRefundUserList(page, qo);
        ServletOutputStream io;
        try {
            io = response.getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        response.setContentType("application/octet-stream;charset=utf-8");
        String templateFileName = URLEncoder.encode("退费用户列表.xlsx", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + templateFileName);
        EasyExcelFactory.write(io, RefundUserVO.class).sheet("").doWrite(refundUserList);
    }

    private RefundUserDetailVO getRefundUser(Long id, String userId, boolean isManager) {
        RefundUserQO refundUserQO = new RefundUserQO();
        if (id != null) {
            refundUserQO.setId(id);
        }
        if (userId != null) {
            refundUserQO.setUserId(userId);
        }
        List<RefundUserVO> refundUserList = baseMapper.getRefundUserList(null, refundUserQO);
        if (refundUserList.isEmpty()) {
            // 此处不抛出异常，由前端自行处理
            return null;
        }
        RefundUserVO refundUserVO = refundUserList.get(0);

        //当前学员相关的所有日志
        List<RefundOpLog> list = refundOpLogService.list(
                new LambdaQueryWrapper<RefundOpLog>()
                        .eq(RefundOpLog::getOpUserId, refundUserVO.getUserId())
        );

        //用户操作日志 list中optype为ADD_BANK_CARD、UPDATE_BANK_CARD和DONATE的数据
        List<RefundOpLog> refundOpLogUserList = list.stream()
                .filter(item -> RefundLogOpTypeEnum.ADD_BANK_CARD.toString().equals(item.getOpType())
                        || RefundLogOpTypeEnum.UPDATE_BANK_CARD.toString().equals(item.getOpType())
                        || RefundLogOpTypeEnum.DONATE.toString().equals(item.getOpType()))
                .sorted(Comparator.comparing(RefundOpLog::getOpTime).reversed())
                .collect(Collectors.toList());

        // 组装返回结果
        RefundUserDetailVO detailVO = new RefundUserDetailVO();
        detailVO.setUserInfo(refundUserVO);
        if (isManager) {
            //管理员日志 list中optype为UPDATE_BALANCE和UPDATE_BALANCE_CARD的数据
            List<RefundOpLog> refundOpLogManagerList = list.stream()
                    .filter(item -> RefundLogOpTypeEnum.UPDATE_BALANCE.toString().equals(item.getOpType())
                            || RefundLogOpTypeEnum.UPDATE_BALANCE_CARD.toString().equals(item.getOpType()))
                    .sorted(Comparator.comparing(RefundOpLog::getOpTime).reversed())
                    .collect(Collectors.toList());
            detailVO.setManagerLogs(refundOpLogManagerList);
        } else {
            //手机端查询用户信息时，需要带回退费设置信息
            RefundSettingVO refundSetting = refundSettingService.getRefundSettingByYear(refundUserVO.getYear());
            detailVO.setRefundSetting(refundSetting);
        }
        detailVO.setUserLogs(refundOpLogUserList);

        return detailVO;
    }

    @Override
    public RefundUserDetailVO getRefundUserDetail(Long id) {
        if (id == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("退费用户ID不能为空");
        }
        return getRefundUser(id, null, true);
    }

    @Override
    public RefundUserDetailVO getMyRefundUserDetail() {
        String userId = SecurityContextHolder.getUserId();
        if (StringUtils.isEmpty(userId)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请重新登录后再查询");
        }
        return getRefundUser(null, userId, false);
    }

    @Override
    public boolean saveRefundUser(RefundUserVO vo) {
        RefundUser entity = new RefundUser();
        BeanUtils.copyProperties(vo, entity);
        return this.save(entity);
    }

    @Override
    public boolean updateRefundUser(RefundUserVO vo) {
        if (vo == null || vo.getId() == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("参数ID不能为空");
        }

        // 先查询数据库中的记录
        RefundUser existingUser = this.getById(vo.getId());
        if (existingUser == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("退费用户不存在");
        }

        RefundSettingVO refundSetting = refundSettingService.getRefundSettingByYear(existingUser.getYear());
        if (refundSetting != null) {
            //判断当前时间是否大于refundSetting的disableDate
            if (new Date().after(refundSetting.getDisableDate())) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("当前时间已禁止编辑");
            }
        }

        if (!StringUtil.isEmpty((vo.getBankNumber()))) {
            if (vo.getBankNumber().length() < 15) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("银行卡号错误");
            }
            //传入银行卡号 才修改银行卡
            if (StringUtil.isEmpty(existingUser.getBankNumber())) {
                // 当前用户没有银行卡
                refundOpLogService.save(new RefundOpLog() {
                    {
                        setOpType(RefundLogOpTypeEnum.ADD_BANK_CARD.toString());
                        setOpUserId(existingUser.getUserId());
                        setOpContent(String.format("成功添加%s(尾号%s)", vo.getBankName(),
                                vo.getBankNumber().substring(vo.getBankNumber().length() - 4)));
                        setOpUser(SecurityContextHolder.getUserName());
                        setOpTime(new Date());
                    }
                });
            } else if (!Objects.equals(existingUser.getBankName(), vo.getBankUserName())
                    || !Objects.equals(existingUser.getBankNumber(), vo.getBankNumber())
                    || !Objects.equals(existingUser.getBankUserName(), vo.getBankUserName())) {
                // 提交的银行卡信息和数据库不一致 则写入更新日志
                refundOpLogService.save(new RefundOpLog() {
                    {
                        setOpType(RefundLogOpTypeEnum.UPDATE_BANK_CARD.toString());
                        setOpUserId(existingUser.getUserId());
                        setOpContent(String.format("添加%s(尾号%s)", vo.getBankName(),
                                vo.getBankNumber().substring(vo.getBankNumber().length() - 4)));
                        setOpUser(SecurityContextHolder.getUserName());
                        setOpTime(new Date());
                    }
                });
            }
        }

        // 数据库中未捐赠 则允许修改捐赠
        if (!Boolean.TRUE.equals(existingUser.getDonate())) {
            if (Boolean.TRUE.equals(vo.getDonate())) {
                // 如果数据库中donate为true，则保持原值不变
                vo.setDonate(true);
                refundOpLogService.save(new RefundOpLog() {
                    {
                        setOpType(RefundLogOpTypeEnum.DONATE.toString());
                        setOpUserId(existingUser.getUserId());
                        setOpContent("从一卡通捐赠");
                        setOpUser(SecurityContextHolder.getUserName());
                        setOpTime(new Date());
                    }
                });
            } else {
                vo.setDonate(null);
            }
        } else {
            vo.setDonate(true);
        }


        RefundUser entity = new RefundUser();
        BeanUtils.copyProperties(vo, entity);
        return this.updateById(entity);
    }

    @Override
    public boolean deleteRefundUser(Long id) {
        return this.removeById(id);
    }

    /**
     * 异步处理导入数据
     */
    public void importRefundUser(MultipartFile file) {
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        FileInfo uploadInfo = minIOService.upload("open-file", file, false);
        // 立即读取文件内容到内存
        byte[] fileBytes;
        try {
            fileBytes = file.getBytes();
        } catch (IOException e) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("文件读取失败");
        }
        // 获取当前年份
        final int currentYear = LocalDate.now().getYear();
        final String opUserName = SecurityContextHolder.getUserName();
        // 异步处理导入
        CompletableFuture.runAsync(() -> {
            int successCounter = 0;
            int failCounter = 0;
            List<Map<String, Object>> failRecordsList = new ArrayList<>();

            try (InputStream is = new ByteArrayInputStream(fileBytes)) {
                // 使用EasyExcel读取数据，表头在第二行
                List<RefundUserExcelDTO> dataList = EasyExcel.read(is)
                        .head(RefundUserExcelDTO.class)
                        .sheet()
                        .headRowNumber(1)  // 指定表头在第2行
                        .doReadSync();

                // 提取所有用户ID用于查询现有数据
                List<String> userIds = dataList.stream()
                        .map(RefundUserExcelDTO::getUserId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

                // 查询现有用户信息
                LambdaQueryWrapper<RefundUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(RefundUser::getUserId, userIds)
                        .eq(RefundUser::getYear, currentYear);
                List<RefundUser> existingUserList = list(queryWrapper);

                // 处理每条数据
                for (RefundUserExcelDTO data : dataList) {
                    try {
                        // 校验必填字段
                        if (data.getUserId() == null || data.getUserId().trim().isEmpty()) {
                            throw new IllegalArgumentException("学工号不能为空");
                        }

                        // 查找现有用户
                        RefundUser existingUser = existingUserList.stream()
                                .filter(user -> user.getUserId().equals(data.getUserId()))
                                .findFirst()
                                .orElse(null);

                        if (existingUser == null) {
                            // 新增逻辑
                            RefundUser newUser = new RefundUser();
                            newUser.setUserId(data.getUserId());
                            newUser.setYear(currentYear);
                            newUser.setBalance(data.getBalance() != null ? data.getBalance() : "");
                            newUser.setBalanceCard(data.getBalanceCard() != null ? data.getBalanceCard() : "");
                            newUser.setBankName(data.getBankName() != null ? data.getBankName() : "");
                            newUser.setBankNumber(data.getBankNumber() != null ? data.getBankNumber() : "");
                            newUser.setBankUserName(data.getBankUserName() != null ? data.getBankUserName() : "");

                            // 保存到数据库
                            boolean saveResult = save(newUser);
                            if (saveResult) {
                                successCounter++;
                                // 记录余额更新日志
                                if (!newUser.getBalance().isEmpty()) {
                                    createAndSaveLog(newUser.getUserId(), RefundLogOpTypeEnum.UPDATE_BALANCE, newUser.getBalance(), opUserName);
                                }
                                if (!newUser.getBalanceCard().isEmpty()) {
                                    createAndSaveLog(newUser.getUserId(), RefundLogOpTypeEnum.UPDATE_BALANCE_CARD, newUser.getBalanceCard(), opUserName);
                                }
                            } else {
                                throw new RuntimeException("保存数据失败");
                            }
                        } else {
                            // 更新逻辑
                            RefundUser updateUser = new RefundUser();
                            updateUser.setId(existingUser.getId());
                            updateUser.setUserId(data.getUserId());
                            updateUser.setYear(currentYear);
                            if (StringUtil.isEmpty(updateUser.getBankNumber())) {
                                updateUser.setBankNumber(data.getBankNumber() != null ? data.getBankNumber() : existingUser.getBankNumber());
                                updateUser.setBankName(data.getBankName() != null ? data.getBankName() : existingUser.getBankName());
                                updateUser.setBankUserName(data.getBankUserName() != null ? data.getBankUserName() : existingUser.getBankUserName());
                            }
                            updateUser.setBalance(data.getBalance() != null ? data.getBalance() : existingUser.getBalance());
                            updateUser.setBalanceCard(data.getBalanceCard() != null ? data.getBalanceCard() : existingUser.getBalanceCard());

                            // 保持捐赠状态不变
                            if (Boolean.TRUE.equals(existingUser.getDonate())) {
                                updateUser.setDonate(true);
                            }

                            // 更新数据库
                            boolean updateResult = updateById(updateUser);
                            if (updateResult) {
                                successCounter++;

                                // 记录余额变更日志
                                if (!updateUser.getBalance().isEmpty() &&
                                        !updateUser.getBalance().equals(existingUser.getBalance())) {
                                    createAndSaveLog(updateUser.getUserId(), RefundLogOpTypeEnum.UPDATE_BALANCE, updateUser.getBalance(), opUserName);
                                }

                                if (!updateUser.getBalanceCard().isEmpty() &&
                                        !updateUser.getBalanceCard().equals(existingUser.getBalanceCard())) {
                                    createAndSaveLog(updateUser.getUserId(), RefundLogOpTypeEnum.UPDATE_BALANCE_CARD, updateUser.getBalanceCard(), opUserName);
                                }
                            } else {
                                throw new RuntimeException("更新数据失败");
                            }
                        }
                    } catch (Exception e) {
                        // 记录失败信息
                        Map<String, Object> failRecord = new HashMap<>();
                        failRecord.put("姓名", data.getUserName() != null ? data.getUserName() : "未知");
                        failRecord.put("学工号", data.getUserId() != null ? data.getUserId() : "未知");
                        failRecord.put("失败原因", e.getMessage());
                        failRecordsList.add(failRecord);
                        failCounter++;
                    }
                }

                RefundOpLog refundOpLog = new RefundOpLog();
                refundOpLog.setFileName(uploadInfo.getOriginalName());
                refundOpLog.setFileUrl(uploadInfo.getUrl());
                refundOpLog.setOpType(RefundLogOpTypeEnum.IMPORT.toString());
                refundOpLog.setSuccessCount(successCounter);
                refundOpLog.setFailCount(failCounter);
                refundOpLog.setFailDetails(failRecordsList.isEmpty() ? null : JSONUtil.toJsonStr(failRecordsList));
                refundOpLog.setStatus("已完成");
                refundOpLog.setOpTime(new Date());
                refundOpLog.setOpUser(opUserName);
                refundOpLogService.save(refundOpLog);


            } catch (IOException e) {
                RefundOpLog refundOpLog = new RefundOpLog();
                refundOpLog.setFileName(uploadInfo.getOriginalName());
                refundOpLog.setFileUrl(uploadInfo.getUrl());
                refundOpLog.setOpType(RefundLogOpTypeEnum.IMPORT.toString());
                refundOpLog.setSuccessCount(successCounter);
                refundOpLog.setFailCount(failCounter);
                refundOpLog.setOpContent(e.getMessage());
                refundOpLog.setStatus("异常");
                refundOpLog.setOpTime(new Date());
                refundOpLog.setOpUser(opUserName);
                refundOpLogService.save(refundOpLog);
            }
        });
    }

    /**
     * 辅助方法：创建并保存操作日志
     */
    private void createAndSaveLog(String userId, RefundLogOpTypeEnum opType, String content, String opUserName) {
        refundOpLogService.save(new RefundOpLog() {{
            setOpType(opType.toString());
            setOpUserId(userId);
            setOpContent(content);
            setOpTime(new Date());
            setOpUser(opUserName);
        }});
    }

    /**
     * 生成导入模板
     */
    public void generateImportTemplate(HttpServletResponse response) {
        //生成导入模板
        ServletOutputStream io;
        try {
            io = response.getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        response.setContentType("application/octet-stream;charset=utf-8");
        String templateFileName = URLEncoder.encode("退费用户导入模板.xlsx", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + templateFileName);
        EasyExcelFactory.write(io, RefundUserExcelDTO.class).sheet("").doWrite(Collections.emptyList());
    }

    @Override
    public List<String> getCollegeList() {
        return baseMapper.getCollegeList();
    }
}
