package com.lanshan.base.commonservice.workbench.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 用户访问应用记录表(WbAppAccessLog)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户访问应用记录表VO")
@Data
@ToString
public class WbAppAccessLogVO implements Serializable {

    private static final long serialVersionUID = -4729437104494524206L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "应用ID ")
    private Long appId;

    @ApiModelProperty(value = "访问时间")
    private Date createTime;

    @ApiModelProperty(value = "用户访问地址")
    private String ipAddr;

    @ApiModelProperty(value = "访问目标应用地址")
    private String accessUrl;
}

