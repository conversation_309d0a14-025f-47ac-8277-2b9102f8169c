package com.lanshan.base.commonservice.group.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 群聊导出历史(MsgGroupExportHistory)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class MsgGroupExportHistory extends Model<MsgGroupExportHistory> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 导出状态 1 正在导出 2 导出失败 3 导出成功
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

