package com.lanshan.base.commonservice.addressbook.converter;


import com.lanshan.base.commonservice.addressbook.dto.UserPhoneSearchDTO;
import com.lanshan.base.commonservice.addressbook.entity.CpUserPhone;
import com.lanshan.base.commonservice.addressbook.vo.CpUserPhoneVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户手机号绑定表(CpUserPhone)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface CpUserPhoneConverter {

    CpUserPhoneConverter INSTANCE = Mappers.getMapper(CpUserPhoneConverter.class);

    CpUserPhoneVO toVO(CpUserPhone entity);

    CpUserPhone toEntity(CpUserPhoneVO vo);

    List<CpUserPhoneVO> toVO(List<CpUserPhone> entityList);

    List<CpUserPhone> toEntity(List<CpUserPhoneVO> voList);

    CpUserPhone toEntity(UserPhoneSearchDTO dto);
}


