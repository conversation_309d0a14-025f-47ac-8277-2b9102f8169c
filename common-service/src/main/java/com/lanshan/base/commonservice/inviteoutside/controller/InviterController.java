package com.lanshan.base.commonservice.inviteoutside.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.inviteoutside.pojo.qo.InviteOutsidePageQO;
import com.lanshan.base.commonservice.inviteoutside.pojo.vo.InviteOutsideJoinLogVO;
import com.lanshan.base.commonservice.inviteoutside.service.InviteOutsideJoinLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 邀请外部人员-邀请端接口
 */
@RestController
@RequestMapping("/invite-outside/inviter")
@Api(tags = "邀请外部人员-邀请端接口")
public class InviterController {

    @Resource
    private InviteOutsideJoinLogService inviteOutsideJoinLogService;

    @ApiOperation("审核")
    @PostMapping(value = "/audit")
    public Result<Boolean> audit(Long submitId, Integer auditStatus) throws Exception {
        return Result.build(inviteOutsideJoinLogService.audit(submitId, auditStatus));
    }

    @ApiOperation("分页查询")
    @GetMapping(value = "/page")
    public Result<IPage<InviteOutsideJoinLogVO>> page(InviteOutsidePageQO qo) {
        qo.setUserId(SecurityContextHolder.getUserId());
        return Result.build(inviteOutsideJoinLogService.pageByParam(qo));
    }
}
