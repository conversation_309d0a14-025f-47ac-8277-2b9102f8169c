package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdTuitionFee;
import com.lanshan.base.commonservice.standardapp.vo.StdTuitionFeeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学费查询表(StdTuitionFee)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdTuitionFeeConverter {

    StdTuitionFeeConverter INSTANCE = Mappers.getMapper(StdTuitionFeeConverter.class);

    StdTuitionFeeVO toVO(StdTuitionFee entity);

    StdTuitionFee toEntity(StdTuitionFeeVO vo);

    List<StdTuitionFeeVO> toVO(List<StdTuitionFee> entityList);

    List<StdTuitionFee> toEntity(List<StdTuitionFeeVO> voList);
}


