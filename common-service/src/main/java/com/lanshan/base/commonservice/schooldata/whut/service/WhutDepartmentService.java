package com.lanshan.base.commonservice.schooldata.whut.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutDepartment;

import java.util.List;
import java.util.Set;

/**
 * 院系所单位信息表(WhutDepartment)表服务接口
 *
 * <AUTHOR>
 */
public interface WhutDepartmentService extends IService<WhutDepartment> {

    List<WhutDepartment> listByLevelAndIsSchoolData(Integer level, int isSchoolData);

    /**
     * 获取指定层级下指定名称的部门信息（是校内数据）
     *
     * @param level             层级
     * @param designatedDeptSet 指定名称的部门集合
     * @return 部门信息
     */
    List<WhutDepartment> listByLevelAndDwmc(int level, Set<String> designatedDeptSet);

    /**
     * 同步院系所单位信息
     */
    void syncData();
}

