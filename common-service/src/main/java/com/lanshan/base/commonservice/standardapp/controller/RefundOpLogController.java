package com.lanshan.base.commonservice.standardapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.qo.RefundOpLogQO;
import com.lanshan.base.commonservice.standardapp.service.RefundOpLogService;
import com.lanshan.base.commonservice.standardapp.vo.RefundOpLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 退费操作日志控制器
 */
@RestController
@RequestMapping("/refund/oplog")
@Api(tags = "退费操作日志管理")
public class RefundOpLogController {

    @Autowired
    private RefundOpLogService refundOpLogService;

    @PostMapping("/list")
    @ApiOperation("获取退费操作日志列表")
    public Result<IPage<RefundOpLogVO>> getRefundOpLogList(@RequestBody RefundOpLogQO qo) {
        return Result.build(refundOpLogService.getRefundOpLogList(qo));
    }

    @PostMapping("/save")
    @ApiOperation("保存退费操作日志")
    public Result<Boolean> saveRefundOpLog(@RequestBody RefundOpLogVO vo) {
        return Result.build(refundOpLogService.saveRefundOpLog(vo));
    }

    @PostMapping("/update")
    @ApiOperation("更新退费操作日志")
    public Result<Boolean> updateRefundOpLog(@RequestBody RefundOpLogVO vo) {
        return Result.build(refundOpLogService.updateRefundOpLog(vo));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除退费操作日志")
    public Result<Boolean> deleteRefundOpLog(@PathVariable Long id) {
        return Result.build(refundOpLogService.deleteRefundOpLog(id));
    }
}