package com.lanshan.base.commonservice.group.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.group.entity.MsgClassGroupChatApply;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatApplyPageQo;
import com.lanshan.base.commonservice.group.qo.MsgClassGroupChatApplySaveQo;
import com.lanshan.base.commonservice.group.vo.ClassGroupChatApplyBaseVO;
import com.lanshan.base.commonservice.group.vo.ClassInfoVO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatApplyDetailVO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatApplyVO;

import java.util.List;

/**
 * 班级建群申请(MsgClassGroupChatApply)表服务接口
 */
public interface MsgClassGroupChatApplyService extends IService<MsgClassGroupChatApply> {

    /**
     * 获取班级群聊申请基础信息
     * @return 班级群聊申请基础信息
     */
    ClassGroupChatApplyBaseVO getClassGroupChatApplyBase();

    /**
     * 获取班级信息
     * @return 班级信息
     */
    List<ClassInfoVO> listClassInfo();

    /**
     * 分页查询班级群聊申请
     * @param pageQo 查询条件
     * @return 分页查询结果
     */
    IPage<MsgClassGroupChatApplyVO> pageClassGroupChatApply(MsgClassGroupChatApplyPageQo pageQo);

    /**
     * 分页查询班级群聊审批
     * @param pageQo 查询条件
     * @return 分页查询结果
     */
    IPage<MsgClassGroupChatApplyVO> pageClassGroupChatApproval(MsgClassGroupChatApplyPageQo pageQo);

    /**
     * 查询班级群聊申请详情
     * @param id 申请id
     * @return 申请详情
     */
    List<MsgClassGroupChatApplyDetailVO> listClassGroupChatApplyDetail(Long id);

    /**
     * 查询班级群聊审批详情
     * @param id 审批id
     * @return 审批详情
     */
    List<MsgClassGroupChatApplyDetailVO> listClassGroupChatApprovalDetail(Long id);

    /**
     * 创建班级群聊申请
     * @param saveQo 申请信息
     */
    void createClassGroupChatApply(MsgClassGroupChatApplySaveQo saveQo);

    /**
     * 根据id查询申请单状态
     * @param id 申请id
     * @return 申请单状态
     */
    Integer getApplyStatusById(Long id);
}

