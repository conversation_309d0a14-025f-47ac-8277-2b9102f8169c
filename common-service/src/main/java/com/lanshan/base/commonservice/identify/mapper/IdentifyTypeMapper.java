package com.lanshan.base.commonservice.identify.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.identify.entity.IdentifyTypeEntity;
import org.apache.ibatis.annotations.Param;
/**
 * (IdentifyType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-10 16:37:55
 */
public interface IdentifyTypeMapper extends BaseMapper<IdentifyTypeEntity> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<IdentifyTypeEntity> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<IdentifyTypeEntity> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<IdentifyTypeEntity> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<IdentifyTypeEntity> entities);

    IdentifyTypeEntity selectIdentify(@Param("fClassIdentify") String fClassIdentify, @Param("sClassIdentify") String sClassIdentify);
}

