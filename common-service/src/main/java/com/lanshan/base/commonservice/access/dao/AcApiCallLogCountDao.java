package com.lanshan.base.commonservice.access.dao;

import com.lanshan.base.commonservice.access.qo.AcApiCallLogCountQO;
import com.lanshan.base.commonservice.access.qo.AcApiRankByAppQO;
import com.lanshan.base.commonservice.access.qo.AcAppTop10ByApiQO;
import com.lanshan.base.commonservice.access.vo.AcApiCallLogAppTop10VO;
import com.lanshan.base.commonservice.access.vo.AcApiCallLogCountVO;
import com.lanshan.base.commonservice.access.vo.AcApiRankByAppVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.access.entity.AcApiCallLogCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 系统开放API调用日志次数(AcApiCallLogCount)数据库访问层
 */

@Mapper
public interface AcApiCallLogCountDao extends BaseMapper<AcApiCallLogCount> {

    /**
     * 查询调用日志次数
     * @param param 查询参数
     * @return 调用日志次数
     */
    List<AcApiCallLogCountVO> listCallCount(@Param("param") AcApiCallLogCountQO param);

    /**
     * 根据应用查询API调用排行
     * @param param 查询参数
     * @return API调用排行
     */
    List<AcApiRankByAppVO> apiRankByApp(@Param("param") AcApiRankByAppQO param);

    /**
     * 根据API查询应用调用Top10
     * @return 应用调用Top10
     */
    List<AcApiCallLogAppTop10VO> appTop10ByApi(@Param("param") AcAppTop10ByApiQO param);

}

