package com.lanshan.base.commonservice.config;

import com.lanshan.base.commonservice.addressbook.handler.AddressbookHandlerFactory;
import com.lanshan.base.commonservice.addressbook.handler.UserBindHandlerFactory;
import com.lanshan.base.commonservice.common.handler.OpenApprovalChangeHandlerFactory;
import com.lanshan.base.commonservice.group.handler.GroupChatHandlerFactory;
import com.lanshan.base.commonservice.inviteoutside.handler.InviteOutsideHandlerFactory;
import com.lanshan.base.commonservice.message.handler.ChannelHandlerFactory;
import com.lanshan.base.commonservice.task.batch.excutor.BatchExecutorFactory;
import com.lanshan.base.commonservice.todo.handler.TodoHandlerFactory;
import org.springframework.beans.factory.config.ServiceLocatorFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * BeanConfiguration
 *
 * <AUTHOR>
 */
@Configuration
public class BeanConfiguration {

    @Bean("channelHandlerFactory")
    public ServiceLocatorFactoryBean serviceLocatorFactoryBean() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(ChannelHandlerFactory.class);
        return factoryBean;
    }

    @Bean("userBindHandlerFactory")
    public ServiceLocatorFactoryBean userBindHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(UserBindHandlerFactory.class);
        return factoryBean;
    }

    @Bean("groupChatHandlerFactory")
    public ServiceLocatorFactoryBean groupChatHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(GroupChatHandlerFactory.class);
        return factoryBean;
    }

    @Bean("inviteOutsideHandlerFactory")
    public ServiceLocatorFactoryBean inviteOutsideHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(InviteOutsideHandlerFactory.class);
        return factoryBean;
    }

    @Bean("todoHandlerFactory")
    public ServiceLocatorFactoryBean todoHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(TodoHandlerFactory.class);
        return factoryBean;
    }

    @Bean("openApprovalChangeHandlerFactory")
    public ServiceLocatorFactoryBean openApprovalChangeHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(OpenApprovalChangeHandlerFactory.class);
        return factoryBean;
    }

    @Bean("batchExecutorFactory")
    public ServiceLocatorFactoryBean batchExecutorFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(BatchExecutorFactory.class);
        return factoryBean;
    }

    @Bean("addressbookHandlerFactory")
    public ServiceLocatorFactoryBean addressbookHandlerFactory() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        // 设置服务定位接口
        factoryBean.setServiceLocatorInterface(AddressbookHandlerFactory.class);
        return factoryBean;
    }
}
