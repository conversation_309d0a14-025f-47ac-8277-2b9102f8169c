package com.lanshan.base.commonservice.todo.converter;


import com.lanshan.base.commonservice.todo.entity.TodoUserOperateLog;
import com.lanshan.base.commonservice.todo.vo.TodoUserOperateLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 待办用户操作表(TodoUserOperateLog)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoUserOperateLogConverter {

    TodoUserOperateLogConverter INSTANCE = Mappers.getMapper(TodoUserOperateLogConverter.class);

    TodoUserOperateLogVO toVO(TodoUserOperateLog entity);

    TodoUserOperateLog toEntity(TodoUserOperateLogVO vo);

    List<TodoUserOperateLogVO> toVO(List<TodoUserOperateLog> entityList);

    List<TodoUserOperateLog> toEntity(List<TodoUserOperateLogVO> voList);
}


