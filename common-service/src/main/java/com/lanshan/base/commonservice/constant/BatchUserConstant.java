package com.lanshan.base.commonservice.constant;

/**
 * @program: capability-platform-base
 * @Description: 批量操作企微用户
 * @author: JZ
 * @createTime: 2024-12-10 16:43
 */
public class BatchUserConstant {

    //通用配置
    //批量删除用户时间间隔
    public static final String BATCH_DEL_CP_USER_INTERVAL = "batch.del.user.interval";
    //企业微信id
    public static final String CORP_ID ="corpId";
    //通讯录操作 应用id
    public static final String AGENT_ID ="agentId";

    //批量删除用户，每次往企微发送的数量
    public static final String BATCH_DEL_USER_SIZE = "batch.del.user.size";
    public static final Integer BATCH_DEL_CP_USER_SIZE = 200;
    //批量操作中的状态
    public static final Integer BATCH_ONE = 1;

    public static final Integer BATCH_TWO = 2;
    //正常装她爱
    public static final Integer BATCH_ZERO = 0;

    public static final Integer BATCH_STATE = 6;


    //企微 批量删除用户入参属性
    public static final String USER_ID_LIST ="userIdList";

    public static final String BATCH_USER_ID = "userId";
    public static final String BATCH_ENABLE = "enable";



}
