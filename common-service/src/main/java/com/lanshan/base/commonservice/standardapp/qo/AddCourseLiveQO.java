package com.lanshan.base.commonservice.standardapp.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "添加在线课程QO")
@Data
public class AddCourseLiveQO {

    @ApiModelProperty(value = "直播类型 1 会议 2 直播")
    private String liveType;

    @ApiModelProperty(value = "直播地址")
    private String liveUrl;

    @ApiModelProperty(value = "会议号")
    private String meetingNo;
}
