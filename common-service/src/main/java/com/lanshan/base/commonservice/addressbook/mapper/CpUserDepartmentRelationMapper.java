package com.lanshan.base.commonservice.addressbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.api.dto.user.DeptLeaderDto;
import com.lanshan.base.commonservice.addressbook.entity.CpUserDepartmentRelation;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户-部门关联表(UserDepartmentRelation)数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Mapper
public interface CpUserDepartmentRelationMapper extends BaseMapper<CpUserDepartmentRelation> {

    //清空表
    @Update("TRUNCATE TABLE cp_user_department_relation")
    void truncate();

    //复制备份表的数据到标准表
    @Insert("INSERT INTO cp_user_department_relation SELECT * FROM cp_user_department_relation_bak")
    void copyBakToStandard();

    /**
     * 根据部门id列表查询部门领导
     * @param deptIds 部门id列表
     * @return 部门领导列表
     */
    List<DeptLeaderDto> listDeptLeader(List<Long> deptIds);
}

