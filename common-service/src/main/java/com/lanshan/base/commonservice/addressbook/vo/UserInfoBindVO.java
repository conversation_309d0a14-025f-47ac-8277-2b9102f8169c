package com.lanshan.base.commonservice.addressbook.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 用户认证表(UserInfoBind)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户认证表VO")
@Data
@ToString
public class UserInfoBindVO implements Serializable {

    private static final long serialVersionUID = -3541733475424313952L;
    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "微信openid")
    private String openId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "证件号码（加密）")
    private String identityNumber;

    @ApiModelProperty(value = "工号")
    private String userId;

    @ApiModelProperty(value = "证件图片,多个用逗号分割")
    private String certificateImg;

    @ApiModelProperty(value = "是否默认（切换身份后设置默认，下次进入默认显示此身份）")
    private Boolean isDefault;

    @ApiModelProperty(value = "是否已删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "证件号码   420****3211")
    private String identityNumberWrapper;
}

