package com.lanshan.base.commonservice.todo.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("待办修改入参")
public class TodoUpdateQo extends TodoBaseQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("操作模式，在待办为重复时有效 类型 1：指定当前待办修改 2：未来所有待办修改")
    private Integer opMode;

    @ApiModelProperty("操作开始时间，该时间必须是重复待办的某一次开始时间，start_time和end_time，必须是op_start_time当天或之后的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opStartTime;

    @ApiModelProperty("是否只更新基础信息 名称、描述、链接地址")
    @NotNull(message = "是否只更新基础信息不能为空")
    private Integer updateBasic;

    @ApiModelProperty(value = "企微日程id", hidden = true)
    private String scheduleId;
}

