package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 部门可建群人员关联表(WhutDeptHeadInfo)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "部门可建群人员关联表VO")
@Data
@ToString
public class WhutDeptHeadInfoVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "教师工号")
    private String userid;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;
}

