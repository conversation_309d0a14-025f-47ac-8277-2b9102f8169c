package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.schooldata.whut.constant.WhutOpenApiConstant;
import com.lanshan.base.commonservice.schooldata.whut.converter.WhutMasterCounselorClassConverter;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutMasterCounselorClassDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutMasterCounselorClass;
import com.lanshan.base.commonservice.schooldata.whut.properties.WhutOpenApiProperties;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutCommonService;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutMasterCounselorClassService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 研究生辅导员带班信息(WhutMasterCounselorClass)表服务实现类
 */
@Slf4j
@Service
public class WhutMasterCounselorClassServiceImpl extends ServiceImpl<WhutMasterCounselorClassDao, WhutMasterCounselorClass> implements WhutMasterCounselorClassService {

    @Resource
    private WhutCommonService whutCommonService;

    @Resource
    private WhutOpenApiProperties whutOpenApiProperties;

    @Resource
    private WhutMasterCounselorClassDao whutMasterCounselorClassDao;

    @Resource
    private UserService userService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sync() {
        // 获取access_token
        String accessToken = whutCommonService.getAccessToken();

        String url = whutOpenApiProperties.getBaseUrl() + whutOpenApiProperties.getTgxxsyjsfdydbxx();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put(WhutOpenApiConstant.ACCESS_TOKEN, accessToken);
        queryMap.put(WhutOpenApiConstant.PAGE, 1);
        queryMap.put(WhutOpenApiConstant.PAGE_SIZE, whutOpenApiProperties.getPageSize());
        // 查询第一页
        JsonNode firstNode;
        try {
            String first = HttpUtil.get(url, queryMap);
            firstNode = JacksonUtils.toObj(first);
            //查询失败
            int code = firstNode.get(WhutOpenApiConstant.CODE).asInt();
            if (code != WhutOpenApiConstant.SUCCESS_CODE) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步【研究生辅导员带生信息】获取第1页数据失败，错误代码：" + code).toServiceException();
            }
        } catch (Exception e) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步【研究生辅导员带生信息】获取第1页数据失败：" + e.getMessage()).toServiceException();
        }
        JsonNode firstResult = firstNode.get(WhutOpenApiConstant.RESULT);
        List<WhutMasterCounselorClass> list = JacksonUtils.toObj(firstResult.get(WhutOpenApiConstant.DATA).toString(), new TypeReference<>() {
        });
        log.info("同步【研究生辅导员带生信息】第1页数据获取成功，当前总共获取{}条数据", list.size());

        //判断是否还有其他页数据，如果有，则继续获取
        int maxPage = firstResult.get(WhutOpenApiConstant.MAX_PAGE).asInt();
        if (maxPage > 1) {
            // 从第二页开始查询
            for (int i = 2; i <= maxPage; i++) {
                queryMap.put(WhutOpenApiConstant.PAGE, i);
                JsonNode tempNode;
                try {
                    String temp = HttpUtil.get(url, queryMap);
                    tempNode = JacksonUtils.toObj(temp);
                    //查询失败
                    int code = tempNode.get(WhutOpenApiConstant.CODE).asInt();
                    if (code != WhutOpenApiConstant.SUCCESS_CODE) {
                        throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步【研究生辅导员带生信息】获取第" + i + "页数据失败，错误代码：" + code).toServiceException();
                    }
                } catch (Exception e) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步【研究生辅导员带生信息】获取第" + i + "页数据失败：", e.getMessage()).toServiceException();
                }
                List<WhutMasterCounselorClass> tempList = JacksonUtils.toObj(tempNode.get(WhutOpenApiConstant.RESULT).get(WhutOpenApiConstant.DATA).toString(), new TypeReference<>() {
                });
                list.addAll(tempList);
                log.info("同步【研究生辅导员带生信息】第{}页数据获取成功，当前总共获取{}条数据", i, list.size());
            }
        }

        if (CollUtil.isNotEmpty(list)) {
            // 删除原有数据
            whutMasterCounselorClassDao.truncate();

            //删除2019年之前的数据
            // list.removeIf(item -> Integer.parseInt(item.getNj()) < 2019);
            list.removeIf(item -> {
                String nj = item.getNj();
                return StringUtils.isBlank(nj) || Integer.parseInt(nj) < 2019;
            });

            List<WhutMasterCounselorClass> entityList = new ArrayList<>();
            for (WhutMasterCounselorClass counselor : list) {
                //辅导员工号含逗号的分割
                String userid = counselor.getFdygh();
                if (CharSequenceUtil.isNotBlank(userid) && userid.contains(",")) {
                    //工号列表
                    List<String> useridList = CharSequenceUtil.split(userid, ",");
                    for (String s : useridList) {
                        //为空串则跳过
                        if (CharSequenceUtil.isBlank(s)) {
                            continue;
                        }

                        WhutMasterCounselorClass entity = WhutMasterCounselorClassConverter.INSTANCE.toEntity(counselor);
                        entity.setFdygh(s);
                        entity.setFdyxm(null);
                        entityList.add(entity);
                    }
                } else {
                    entityList.add(counselor);
                }
            }

            //根据工号、年级、专业、学号去重
            entityList = entityList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(o -> o.getFdygh() + "-" + o.getNj() + "-" + o.getZybm() + "-" + o.getXh()))), ArrayList::new));

            //填充用户名
            for (WhutMasterCounselorClass entity : entityList) {
                CpUser user = userService.getCacheUserByUserid(entity.getFdygh());
                if (user != null) {
                    entity.setFdyxm(user.getName());
                }
            }

            // 批量插入新数据
            super.saveBatch(entityList);
        }

        log.info("同步【研究生辅导员带生信息】完成，总共获取{}条数据", list.size());
    }

}

