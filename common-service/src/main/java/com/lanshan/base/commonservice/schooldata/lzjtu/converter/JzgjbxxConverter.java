package com.lanshan.base.commonservice.schooldata.lzjtu.converter;


import java.util.List;

import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Jzgjbxx;
import com.lanshan.base.commonservice.schooldata.lzjtu.vo.JzgjbxxVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 人事教职工基本信息(Jzgjbxx)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface JzgjbxxConverter {

    JzgjbxxConverter INSTANCE = Mappers.getMapper(JzgjbxxConverter.class);

    JzgjbxxVO toVO(Jzgjbxx entity);

    Jzgjbxx toEntity(JzgjbxxVO vo);
    
    List<JzgjbxxVO> toVO(List<Jzgjbxx> entityList);

    List<Jzgjbxx> toEntity(List<JzgjbxxVO> voList);
}


