package com.lanshan.base.commonservice.identify.dto;

import com.lanshan.base.api.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * @program: capability-platform-base
 * @Description:
 * @createTime: 2025-04-24 15:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddStaffDto {
    /**
     {
     "staffName":"王五",
     "source":"管理员",
     "sex":"男",
     "idCardType":"身份证",
     "idCard":"******************",
     "phone":"13666666666",
     "fClassIdentify":"学生",
     "sClassIdentify":"研究生",
     "staffNoType":"学号",
     "staffNo":"2000",
     "fClassDept":"计算机学院",
     "sClassDept":"软件工程",
     "endTime":"2025-06-01 12:00:00"
     }
     */
    private String staffName;
    private String source;
    private String sex;
    private String idCardType;
    private String idCard;
    private String phone;
    private String fClassIdentify;
    private String sClassIdentify;
    private String staffNoType;
    private String staffNo;
    private String fClassDept;
    private String sClassDept;
    private String endTime;

    public void checkParam(AddStaffDto body) {
        if (StringUtils.isEmpty(body.getStaffName())) {
            throw new ServiceException("姓名不能为空",400);
        }
        if (StringUtils.isEmpty(body.getSex())) {
            throw new ServiceException("性别不能为空",400);
        }
        if (StringUtils.isEmpty(body.getIdCardType())) {
            throw new ServiceException("请选择证件类型",400);
        }
        if (StringUtils.isEmpty(body.getIdCard())) {
            throw new ServiceException("证件不能为空",400);
        }
        if (StringUtils.isEmpty(body.getPhone())) {
            throw new ServiceException("手机号不能为空",400);
        }
        if (StringUtils.isEmpty(body.getFClassIdentify())) {
            throw new ServiceException("身份一级分类不能为空",400);
        }
        if (StringUtils.isEmpty(body.getSClassIdentify())) {
            throw new ServiceException("身份二级分类不能为空",400);
        }
        if (StringUtils.isEmpty(body.getEndTime())) {
            throw new ServiceException("到期日期不能为空",400);
        }
        if (StringUtils.isEmpty(body.getSource())) {
            throw new ServiceException("来源不能为空，默认：管理员",400);
        }
    }
}
