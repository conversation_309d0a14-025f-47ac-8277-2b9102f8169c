package com.lanshan.base.commonservice.schooldata.whut.converter;


import com.lanshan.base.commonservice.schooldata.whut.entity.WhutDepartment;
import com.lanshan.base.commonservice.schooldata.whut.vo.WhutDepartmentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 院系所单位信息表(WhutDepartment)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WhutDepartmentConverter {

    WhutDepartmentConverter INSTANCE = Mappers.getMapper(WhutDepartmentConverter.class);

    WhutDepartmentVO toVO(WhutDepartment entity);

    WhutDepartment toEntity(WhutDepartmentVO vo);
    
    List<WhutDepartmentVO> toVO(List<WhutDepartment> entityList);

    List<WhutDepartment> toEntity(List<WhutDepartmentVO> voList);
}


