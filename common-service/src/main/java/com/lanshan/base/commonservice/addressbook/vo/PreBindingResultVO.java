package com.lanshan.base.commonservice.addressbook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "绑定前校验结果对象")
public class PreBindingResultVO {

    @ApiModelProperty(value = "传入的用户id是否绑定过 true 绑定 false 未绑定")
    private Boolean bindFlag;

    @ApiModelProperty(value = "传入的手机号是否自身身份绑定的 true 是 false 否")
    private Boolean selfFlag;

    @ApiModelProperty(value = "只有当 bindFlag selfFlag 同时为 true 时才有值 ")
    private String userid;

    @ApiModelProperty(value = "传入的手机号是否已被绑定 true 已绑定 false 未绑定")
    private Boolean phoneFlag;
}
