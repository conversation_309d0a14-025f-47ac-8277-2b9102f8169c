package com.lanshan.base.commonservice.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.workbench.entity.WbAppWxset;

/**
 * 应用企业微信设置信息表(WbAppWxset)表服务接口
 *
 * <AUTHOR>
 */
public interface WbAppWxsetService extends IService<WbAppWxset> {

    /**
     * 根据agentId获取应用企业微信设置信息
     *
     * @param agentId 应用agentId
     * @return 应用企业微信设置信息
     */
    WbAppWxset getByAgentId(Integer agentId);
}

