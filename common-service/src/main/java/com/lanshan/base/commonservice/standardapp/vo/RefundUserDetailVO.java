package com.lanshan.base.commonservice.standardapp.vo;

import com.lanshan.base.commonservice.standardapp.entity.RefundOpLog;
import lombok.Data;

import java.util.List;

/**
 * 退费用户详情视图对象
 */
@Data
public class RefundUserDetailVO {
    
    /**
     * 退费用户基本信息
     */
    private RefundUserVO userInfo;
    
    /**
     * 管理员操作日志（校园网余额和一卡通余额操作日志）
     */
    private List<RefundOpLog> managerLogs;
    
    /**
     * 用户操作日志（银行卡和捐赠操作日志）
     */
    private List<RefundOpLog> userLogs;

    /**
     * 退费设置信息
     */
    private  RefundSettingVO  refundSetting;
} 