package com.lanshan.base.commonservice.standardapp.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 学历层次转换器
 * 0：其他
 * 1：教职工
 * 2：本科生
 * 3：研究生
 */
public class RefundUserGenderConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new WriteCellData<>("-");
        }
        
        switch (value) {
            case "0":
                return new WriteCellData<>("未知");
            case "1":
                return new WriteCellData<>("男");
            case "2":
                return new WriteCellData<>("女");
            default:
                return new WriteCellData<>(value);
        }
    }
}