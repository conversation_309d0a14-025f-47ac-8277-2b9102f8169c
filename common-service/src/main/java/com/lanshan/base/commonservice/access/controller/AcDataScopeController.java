package com.lanshan.base.commonservice.access.controller;


import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.converter.AcDataScopeConverter;
import com.lanshan.base.commonservice.access.service.AcDataScopeService;
import com.lanshan.base.commonservice.access.vo.AcDataScopeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.simpleframework.xml.core.Validate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 数据集的权限控制(AcDataScope)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("acDataScope")
@Api(tags = "数据源权限控制层", hidden = true)
public class AcDataScopeController {

    /**
     * 服务对象
     */
    @Resource
    private AcDataScopeService acDataScopeService;

    @ApiOperation("根据appId查询权限控制")
    @GetMapping("listByAppId")
    public Result<List<AcDataScopeVO>> listByAppId(@Validate @RequestParam @NotBlank(message = "appId不能为空") Long appId) {
        return Result.build(AcDataScopeConverter.INSTANCE.toVO(acDataScopeService.listByAppId(appId)));
    }
}

