package com.lanshan.base.commonservice.workbench.controller;


import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.entity.AcApp;
import com.lanshan.base.commonservice.access.entity.AcCompany;
import com.lanshan.base.commonservice.access.service.AcAppService;
import com.lanshan.base.commonservice.access.service.AcCompanyService;
import com.lanshan.base.commonservice.workbench.converter.WbAppSdkConfigConverter;
import com.lanshan.base.commonservice.workbench.entity.WbApp;
import com.lanshan.base.commonservice.workbench.service.WbAppSdkConfigService;
import com.lanshan.base.commonservice.workbench.service.WbAppService;
import com.lanshan.base.commonservice.workbench.vo.WbAppSdkConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 应用悬浮球SDK配置表(WbAppSdkConfig)表控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("app-sdk-config")
@Api(tags = "应用悬浮球SDK配置控制层", hidden = true)
public class WbAppSdkConfigController {
    /**
     * 服务对象
     */
    @Resource
    private WbAppSdkConfigService wbAppSdkConfigService;

    @Resource
    private WbAppService wbAppService;
    @Resource
    private AcAppService acAppService;
    @Resource
    private AcCompanyService acCompanyService;

    @Resource
    private RedisService redisService;

    private static final String SDK_CONFIG_CACHE_KEY = "app_sdk_config:";

    private static final long CACHE_EXPIRE_TIME = 30 * 60; // 30分钟缓存


    @ApiOperation("设置应用接入")
    @PostMapping("/set/integrated")
    public Result<Boolean> setAppIntegrated(@RequestParam Long appId) {
        return Result.build(wbAppSdkConfigService.setAppIntegrated(appId));
    }

    @ApiOperation("获取应用的SDK配置信息")
    @GetMapping("/get-by-appid")
    public Result<WbAppSdkConfigVO> getConfigIdByAppId(@RequestParam Long appId) {

        try {
            // 尝试从缓存获取
            String cacheKey = SDK_CONFIG_CACHE_KEY + appId;
            Object cachedValue = redisService.getCacheObject(cacheKey);
            if (cachedValue != null) {
                return Result.build((WbAppSdkConfigVO) cachedValue);
            }

            WbAppSdkConfigVO vo = WbAppSdkConfigConverter.INSTANCE.toVO(wbAppSdkConfigService.getConfigIdByAppId(appId));

            WbApp appInfo = wbAppService.getById(appId);
            if (appInfo != null) {
                vo.setDeptName(appInfo.getDeptName());
            }
            AcApp acApp = acAppService.getById(appId);
            if (acApp != null && acApp.getCompanyId() != null) {
                AcCompany acCompany = acCompanyService.getById(acApp.getCompanyId());
                if (acCompany != null) {
                    vo.setCompanyName(acCompany.getName());
                }
            }
            // 设置缓存
            redisService.setCacheObject(cacheKey, vo, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            return Result.build(vo);
        } catch (Exception e) {
            log.error("获取应用SDK配置信息异常，应用ID：{}", appId, e);
            return Result.build(new WbAppSdkConfigVO()).error("获取应用SDK配置信息异常");
        }
    }

    @ApiOperation("切换应用的SDK启用状态")
    @PostMapping("/switch/open")
    public Result<Boolean> switchAppSdkOpenStatus(@RequestParam Long appId) {
        return Result.build(wbAppSdkConfigService.switchAppSdkOpenStatus(appId));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<WbAppSdkConfigVO> selectOne(@PathVariable Serializable id) {
        return Result.build(WbAppSdkConfigConverter.INSTANCE.toVO(this.wbAppSdkConfigService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody WbAppSdkConfigVO vo) {
        return Result.build(this.wbAppSdkConfigService.save(WbAppSdkConfigConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody WbAppSdkConfigVO vo) {
        return Result.build(this.wbAppSdkConfigService.updateById(WbAppSdkConfigConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.wbAppSdkConfigService.removeByIds(idList));
    }
}

