package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppScope;
import com.lanshan.base.commonservice.workbench.vo.WbAppScopeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工作台应用可见范围信息表(WbAppScope)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppScopeConverter {

    WbAppScopeConverter INSTANCE = Mappers.getMapper(WbAppScopeConverter.class);

    WbAppScopeVO toVO(WbAppScope entity);

    WbAppScope toEntity(WbAppScopeVO vo);

    List<WbAppScopeVO> toVO(List<WbAppScope> entityList);

    List<WbAppScope> toEntity(List<WbAppScopeVO> voList);
}


