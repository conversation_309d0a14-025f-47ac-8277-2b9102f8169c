package com.lanshan.base.commonservice.standardapp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 退费操作日志
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class RefundOpLog extends Model<RefundOpLog> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作时间
     */
    private Date opTime;

    /**
     * 操作人
     */
    private String opUser;
    /**
     * 操作人
     */
    private String opUserId;

    /**
     * 附件名称 仅后端执行导入操作才有值
     */
    private String fileName;

    /**
     * 附件URL 仅后端执行导入操作才有值
     */
    private String fileUrl;

    /**
     * 操作类型  （添加银行卡、更换银行卡、捐赠支出、导入数据表等）
     */
    private String opType;
    /**
     * 操作内容（添加中国建设银行卡 (尾号5678)、确认捐赠全部余额）
     */
    private String opContent;

    /**
     * 导入成功记录数
     */
    private Integer successCount;

    /**
     * 导入失败记录数
     */
    private Integer failCount;

    /**
     * 失败详情记录（JSON格式）
     */
    private String failDetails;
    /**
     * 状态
     */
    private String status;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
