package com.lanshan.base.commonservice.access.qo;


import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "推送日志日志QO")
@Data
@ToString
public class AcAppPushLogPageQO extends PageQo implements Serializable {

    private static final long serialVersionUID = -215428800556704430L;

    @ApiModelProperty(value = "appId")
    private Long appId;

    @ApiModelProperty(value = "操作类型")
    private String operateType;

    @ApiModelProperty("调用结果状态")
    private Integer responseStatus;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern ="yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern ="yyyy-MM-dd")
    private Date endTime;
}

