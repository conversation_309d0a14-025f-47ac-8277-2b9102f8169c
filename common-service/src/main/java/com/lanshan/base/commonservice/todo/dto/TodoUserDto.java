package com.lanshan.base.commonservice.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "待办用户dto")
public class TodoUserDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序列号")
    private String serialNo;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;
}
