package com.lanshan.base.commonservice.addressbook.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "证件核请求验对象")
public class VerificationOfDocumentQO {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件类型")
    private String identityType;

    @ApiModelProperty(value = "证件号码")
    private String identityNumber;

    @ApiModelProperty(value = "学工号")
    private String userid;

    @ApiModelProperty(value = "证件照片")
    private String certificateImg;
}
