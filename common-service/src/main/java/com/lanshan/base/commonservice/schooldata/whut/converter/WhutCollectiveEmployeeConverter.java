package com.lanshan.base.commonservice.schooldata.whut.converter;


import com.lanshan.base.commonservice.schooldata.whut.entity.WhutCollectiveEmployee;
import com.lanshan.base.commonservice.schooldata.whut.vo.WhutCollectiveEmployeeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *  集体所有制教职工基本信息(WhutCollectiveEmployee)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WhutCollectiveEmployeeConverter {

    WhutCollectiveEmployeeConverter INSTANCE = Mappers.getMapper(WhutCollectiveEmployeeConverter.class);

    WhutCollectiveEmployeeVO toVO(WhutCollectiveEmployee entity);

    WhutCollectiveEmployee toEntity(WhutCollectiveEmployeeVO vo);
    
    List<WhutCollectiveEmployeeVO> toVO(List<WhutCollectiveEmployee> entityList);

    List<WhutCollectiveEmployee> toEntity(List<WhutCollectiveEmployeeVO> voList);
}


