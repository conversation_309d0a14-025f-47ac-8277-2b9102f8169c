package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户-标签关联备份表(CpUserTagRelationBak)实体
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpUserTagRelationBak implements Serializable {
    private static final long serialVersionUID = 995832905715364832L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private String userid;

    @ApiModelProperty("标签id")
    private Long tagid;

}

