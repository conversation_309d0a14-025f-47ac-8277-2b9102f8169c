package com.lanshan.base.commonservice.doublecheck.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.enums.AgentTypeEnum;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.doublecheck.entity.NewEnrollmentStuInfo;
import com.lanshan.base.commonservice.doublecheck.service.NewEnrollmentStuInfoService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 二次验证任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DoubleCheckTask {

    private final NewEnrollmentStuInfoService newEnrollmentStuInfoService;

    private final WxCpServiceFactory wxCpServiceFactory;

    private final AgentProperties agentProperties;

    @XxlJob("doubleCheck-changeUserid")
    public void changeUserid() {
        //获取有学号还未将学号修改过来的用户
        List<NewEnrollmentStuInfo> list = newEnrollmentStuInfoService.list(Wrappers.lambdaQuery(NewEnrollmentStuInfo.class)
                .isNotNull(NewEnrollmentStuInfo::getXh)
                .ne(NewEnrollmentStuInfo::getXh, "")
                .isNotNull(NewEnrollmentStuInfo::getUserid)
                .ne(NewEnrollmentStuInfo::getUserid, "")
                .eq(NewEnrollmentStuInfo::getChanged, false)
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Set<Long> idSet = list.stream().map(NewEnrollmentStuInfo::getId).collect(Collectors.toSet());
        WxCpUserService userService = wxCpServiceFactory
                .get(agentProperties.getCorpId(), AgentTypeEnum.ADDRESS_BOOK.getCode()).getUserService();
        for (NewEnrollmentStuInfo newEnrollmentStuInfo : list) {
            WxCpUser wxCpUser = new WxCpUser();
            wxCpUser.setUserId(newEnrollmentStuInfo.getUserid());
            wxCpUser.setNewUserId(newEnrollmentStuInfo.getXh());
            try {
                userService.update(wxCpUser);
            } catch (WxErrorException e) {
                log.error("修改 userid 失败", e);
                idSet.remove(newEnrollmentStuInfo.getId());
            }
        }
        //批量更新用户
        newEnrollmentStuInfoService.update(Wrappers.lambdaUpdate(NewEnrollmentStuInfo.class)
                .set(NewEnrollmentStuInfo::getChanged, true)
                .setSql("userid = xh")
                .in(NewEnrollmentStuInfo::getId, idSet)
        );
    }
}
