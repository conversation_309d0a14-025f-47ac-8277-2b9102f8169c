package com.lanshan.base.commonservice.identify.uitls;

import org.apache.commons.lang.StringUtils;

/**
 * @createTime: 2025-05-26 19:50
 */
public class IdCardValidator {

    // 18位身份证校验码
    private static final int[] WEIGHT_18 = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    private static final char[] VERIFY_CODE = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    /**
     * 校验身份证号码是否合法
     * @param idCard 身份证号码
     * @return true-合法 false-不合法
     */
    public static boolean isValidIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }

        // 去除空格并转为大写
        idCard = idCard.trim().toUpperCase();

        // 校验长度
        if (idCard.length() == 15) {
            return validate15IdCard(idCard);
        } else if (idCard.length() == 18) {
            return validate18IdCard(idCard);
        }
        return false;
    }

    /**
     * 校验18位身份证
     */
    private static boolean validate18IdCard(String idCard) {
        // 前17位必须是数字
        String first17 = idCard.substring(0, 17);
        if (!first17.matches("\\d+")) {
            return false;
        }

        // 计算校验码
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (idCard.charAt(i) - '0') * WEIGHT_18[i];
        }
        int mod = sum % 11;
        char verifyChar = VERIFY_CODE[mod];

        // 校验最后一位
        return verifyChar == idCard.charAt(17);
    }

    /**
     * 校验15位身份证
     */
    private static boolean validate15IdCard(String idCard) {
        // 全部必须是数字
        if (!idCard.matches("\\d+")) {
            return false;
        }

        // 校验年份
        String year = idCard.substring(6, 8);
        int yearInt = Integer.parseInt(year);
        if (yearInt < 0 || yearInt > 99) {
            return false;
        }

        // 校验月份
        String month = idCard.substring(8, 10);
        int monthInt = Integer.parseInt(month);
        if (monthInt < 1 || monthInt > 12) {
            return false;
        }

        // 校验日期
        String day = idCard.substring(10, 12);
        int dayInt = Integer.parseInt(day);
        return dayInt >= 1 && dayInt <= 31;
    }
}
