package com.lanshan.base.commonservice.access.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ResultTypeEnum;
import com.lanshan.base.api.utils.file.ExcelUtils;
import com.lanshan.base.commonservice.access.converter.AcAppPushLogConverter;
import com.lanshan.base.commonservice.access.converter.AcAppPushLogCountConverter;
import com.lanshan.base.commonservice.access.dao.AcAppPushLogCountDao;
import com.lanshan.base.commonservice.access.dao.AcAppPushLogDao;
import com.lanshan.base.commonservice.access.dto.AcAppPushLogGroupCountDto;
import com.lanshan.base.commonservice.access.entity.AcAppPushLog;
import com.lanshan.base.commonservice.access.entity.AcAppPushLogCount;
import com.lanshan.base.commonservice.access.excel.AppPushLogExportDto;
import com.lanshan.base.commonservice.access.qo.AcAppPushLogCountQO;
import com.lanshan.base.commonservice.access.qo.AcAppPushLogPageQO;
import com.lanshan.base.commonservice.access.service.AcAppPushLogCountService;
import com.lanshan.base.commonservice.access.service.AcAppPushLogService;
import com.lanshan.base.commonservice.access.vo.AcPushLogCountVO;
import com.lanshan.base.commonservice.access.vo.AcAppPushLogVO;
import com.lanshan.base.commonservice.access.vo.AcPushLogTodayCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 应用数据推送日志(AcAppPushLog)服务实现类
 */
@Slf4j
@Service("acAppPushLogService")
public class AcAppPushLogServiceImpl extends ServiceImpl<AcAppPushLogDao, AcAppPushLog> implements AcAppPushLogService {

    @Resource
    private AcAppPushLogDao acAppPushLogDao;

    @Resource
    private AcAppPushLogCountService acAppPushLogCountService;

    @Resource
    private AcAppPushLogCountDao acAppPushLogCountDao;

    @Override
    public IPage<AcAppPushLogVO> pageAppPushLog(AcAppPushLogPageQO pageQO) {
        Page<AcAppPushLog> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcAppPushLog> queryWrapper = Wrappers.lambdaQuery(AcAppPushLog.class);

        //appId
        if (pageQO.getAppId() != null) {
            queryWrapper.eq(AcAppPushLog::getAppId, pageQO.getAppId());
        }
        //操作类型
        if (pageQO.getOperateType() != null) {
            queryWrapper.eq(AcAppPushLog::getOperateType, pageQO.getOperateType());
        }
        //接口调用结果状态
        if (pageQO.getResponseStatus() != null) {
            queryWrapper.eq(AcAppPushLog::getResponseStatus, pageQO.getResponseStatus());
        }
        //接口调用时间范围
        if (pageQO.getStartTime() != null && pageQO.getEndTime() != null) {
            queryWrapper.ge(AcAppPushLog::getCreateTime, pageQO.getStartTime());
            queryWrapper.lt(AcAppPushLog::getCreateTime, DateUtil.offsetDay(pageQO.getEndTime(), 1));
        }

        //分页查询
        IPage<AcAppPushLog> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        IPage<AcAppPushLogVO> pageVo = result.convert(AcAppPushLogConverter.INSTANCE::toVO);
        for (AcAppPushLogVO vo : pageVo.getRecords()) {
            //设置结果状态
            vo.setResponseStatusDesc(EnumUtil.getFieldBy(ResultTypeEnum::getMsg, ResultTypeEnum::getCode, vo.getResponseStatus()));
        }

        return pageVo;
    }

    @Override
    public void exportAppPushLog(AcAppPushLogPageQO pageQO, HttpServletResponse response) throws IOException {
        //不分页查询
        pageQO.setPage(1L);
        pageQO.setSize(Long.MAX_VALUE);
        //查询接口调用日志
        IPage<AcAppPushLogVO> page = pageAppPushLog(pageQO);
        List<AcAppPushLogVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<AppPushLogExportDto> userExportDtoList = BeanUtil.copyToList(records, AppPushLogExportDto.class);
        //导出Excel
        ExcelUtils.exportExcel(response, userExportDtoList, "接口推送日志");
    }

    @Override
    public List<AcPushLogCountVO> listPushCount(AcAppPushLogCountQO qo) {
        //查询调用日志次数
        List<AcPushLogCountVO> list = acAppPushLogCountDao.listPushCount(qo);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list;
    }

    @Override
    public List<AcPushLogTodayCountVO> listTodayPushCount(AcAppPushLogCountQO qo) {
        //查询今日推送日志次数
        List<AcPushLogTodayCountVO> list = acAppPushLogDao.listTodayPushCount(qo);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list;
    }

    @Override
    public void genAppPushLogCount(String dateStr) {
        Date date;
        //如果为空，则默认取昨天的数据
        if (StringUtils.isBlank(dateStr)) {
            date = DateUtil.yesterday();
        } else {
            date = DateUtil.parse(dateStr);
        }

        //查询调用日志次数，按接入方、APP、操作类型分组
        List<AcAppPushLogGroupCountDto> list = acAppPushLogDao.listPushLogGroupCount(date);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<AcAppPushLogCount> entityList = AcAppPushLogCountConverter.INSTANCE.toEntity(list);
        //批量新增
        acAppPushLogCountService.saveBatch(entityList);
    }
}
