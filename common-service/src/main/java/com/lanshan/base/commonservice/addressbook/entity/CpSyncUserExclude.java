package com.lanshan.base.commonservice.addressbook.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;


/**
 * 同步用户排除表(CpSyncUserExclude)表实体类
 */
@Data
public class CpSyncUserExclude implements Serializable{

    @ApiModelProperty(value = "用户id")
    @TableId
    private String userid;

    @ApiModelProperty(value = "用户名")
    private String name;

}

