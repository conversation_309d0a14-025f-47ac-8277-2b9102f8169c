package com.lanshan.base.commonservice.task.batch.enums;

import lombok.Getter;


@Getter
public enum BatchTypeEnum {

    QW_BATCH_REMOVE_USER("qw_batch_remove_user", "企业微信批量删除成员", "qwUserBatchExecutor"),
    QW_BATCH_UPDATE_USER("qw_batch_update_user", "企业微信批量更新成员（禁用/启用）", "qwUserBatchExecutor"),
    QW_BATCH_INVITE_USER("qw_batch_invite_user", "企业微信批量邀请成员", "qwUserBatchExecutor");

    final String batchType;
    final String batchName;
    final String batchExecutorName;

    BatchTypeEnum(String batchType, String batchName, String batchExecutorName) {
        this.batchType = batchType;
        this.batchName = batchName;
        this.batchExecutorName = batchExecutorName;
    }

    public static BatchTypeEnum getBatchTypeEnum(String batchType) {
        for (BatchTypeEnum batchTypeEnum : values()) {
            if (batchTypeEnum.batchType.equals(batchType)) {
                return batchTypeEnum;
            }
        }
        return null;
    }
}
