package com.lanshan.base.commonservice.todo.controller;

import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.annotation.ApiCallLog;
import com.lanshan.base.commonservice.todo.qo.*;
import com.lanshan.base.commonservice.todo.service.TodoDefService;
import com.lanshan.base.commonservice.todo.vo.TodoFlowInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 待办控制层
 */
@RestController
@RequestMapping(value = "/open/access/todo/v1")
@Validated
@Api(tags = "待办开放api控制层", hidden = true)
public class TodoOpenApiV1Controller {

    @Resource
    private TodoDefService todoDefService;

    @ApiCallLog
    @ApiOperation("新增流程信息V1")
    @PostMapping("saveFlowInfo")
    public Result<Object> saveFlowInfo(@RequestBody @Validated TodoSaveFlowInfoQo qo) {
        if (Objects.isNull(qo.getCreateTime())) {
            throw ExceptionCodeEnum.TODO_CREATE_TIME_INVALID.toServiceException();
        }
        todoDefService.saveFlowInfo(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("删除流程信息V1")
    @PostMapping("delFlowInfo")
    public Result<Object> delFlowInfo(@RequestBody @Validated TodoDelFlowInfoQo qo) {
        todoDefService.delFlowInfo(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("更新流程信息V1")
    @PostMapping("updateFlowInfo")
    public Result<Object> updateFlowInfo(@RequestBody @Validated TodoUpdateFlowInfoQo qo) {
        todoDefService.updateFlowInfo(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("新增流程节点V1")
    @PostMapping("saveFlowNode")
    public Result<Object> saveFlowNode(@RequestBody @Validated TodoSaveFlowNodeQo qo) {
        if (Objects.isNull(qo.getFlowNode().getStartTime())) {
            throw ExceptionCodeEnum.TODO_START_TIME_INVALID.toServiceException();
        }
        todoDefService.saveFlowNode(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("删除流程节点V1")
    @PostMapping("delFlowNode")
    public Result<Object> delFlowNode(@RequestBody @Validated TodoDelFlowNodeQo qo) {
        todoDefService.delFlowNode(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("更新流程节点V1")
    @PostMapping("updateFlowNode")
    public Result<Object> updateFlowNode(@RequestBody @Validated TodoSaveFlowNodeQo qo) {
        todoDefService.updateFlowNode(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation("查询流程信息V1")
    @GetMapping("getFlowInfo")
    public Result<TodoFlowInfoVO> getFlowInfo(@RequestParam("serialNo") String serialNo) {
        return Result.build(todoDefService.getFlowInfo(serialNo));
    }

    @ApiCallLog
    @ApiOperation(value = "新增用户待办V1",
            notes = "新增用户待办（只新增），开始时间由调用时间决定，如果该用户已有同一事务同一节点下的待办，无论是否完成，都将变为待完成")
    @PostMapping("addUserTodo")
    public Result<Void> addUserTodo(@RequestBody @Validated AddUserTodoQO qo) {
        todoDefService.addUserTodo(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation(value = "完成用户待办V1",
            notes = "完成用户待办（只完成），完成时间由调用时间决定，如果该用户已有同一事务同一节点下的待办，无论是否完成，都将变为已完成")
    @PostMapping("completeUserTodo")
    public Result<Void> completeUserTodo(@RequestBody @Validated CompleteUserTodoQO qo) {
        todoDefService.completeUserTodo(qo);
        return Result.build();
    }

    @ApiCallLog
    @ApiOperation(value = "删除用户待办V1",
            notes = "删除用户待办（只删除），删除指定事务指定节点下的用户数据，如果未指定用户，删除节点下得所有人员待办或抄送")
    @PostMapping("delUserTodo")
    public Result<Void> delUserTodo(@RequestBody @Validated DelUserTodoQO qo) {
        todoDefService.delUserTodo(qo);
        return Result.build();
    }

}
