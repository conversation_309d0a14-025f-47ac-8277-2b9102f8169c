package com.lanshan.base.commonservice.access.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.access.entity.AcApiCallLog;
import com.lanshan.base.commonservice.access.qo.AcApiCallLogCountQO;
import com.lanshan.base.commonservice.access.qo.AcApiCallLogPageQO;
import com.lanshan.base.commonservice.access.qo.AcApiRankByAppQO;
import com.lanshan.base.commonservice.access.qo.AcAppTop10ByApiQO;
import com.lanshan.base.commonservice.access.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 系统开放API调用日志(AcApiCallLog)表服务接口
 *
 * <AUTHOR>
 */
public interface AcApiCallLogService extends IService<AcApiCallLog> {

    /**
     * 分页查询调用日志
     * @param pageQO 查询条件
     * @return 调用日志列表
     */
    IPage<AcApiCallLogVO> pageApiCallLog(AcApiCallLogPageQO pageQO);

    /**
     * 导出调用日志
     * @param pageQO 查询条件
     * @param response 响应
     */
    void exportApiCallLog(AcApiCallLogPageQO pageQO, HttpServletResponse response) throws IOException;

    /**
     * 统计调用日志次数
     * @param qo 查询条件
     * @return 调用日志次数列表
     */
    List<AcApiCallLogCountVO> listCallCount(AcApiCallLogCountQO qo);

    /**
     * 统计今日调用日志次数
     * @return 调用日志次数列表
     */
    List<AcApiCallLogTodayCountVO> listTodayCallCount(AcApiCallLogCountQO qo);

    /**
     * 生成调用日志次数
     * @param dateStr 日期
     */
    void genApiCallLogCount(String dateStr);

    /**
     * 根据应用查询API调用排行
     * @param qo 查询条件
     * @return API调用排行
     */
    List<AcApiRankByAppVO> apiRankByApp(AcApiRankByAppQO qo);

    /**
     * 根据API查询应用调用Top10
     * @param qo 查询条件
     * @return 应用top10
     */
    List<AcApiCallLogAppTop10VO> appTop10ByApi(AcAppTop10ByApiQO qo);

    /**
     * 批量删除调用日志
     * @param ids 日志id
     */
    void batchDel(List<Long> ids);
}

