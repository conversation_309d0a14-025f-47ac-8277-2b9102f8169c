package com.lanshan.base.commonservice.schooldata.lzjtu.entity;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;


/**
 * 研究生信息(Yjsjbxx)表实体类
 */
@Data
public class Yjsjbxx implements Serializable{

    @ApiModelProperty(value = "学号")
    @JsonProperty("XH")
    private String xh;

    @ApiModelProperty(value = "姓名")
    @JsonProperty("XM")
    private String xm;

    @ApiModelProperty(value = "专业代码")
    @JsonProperty("ZYDM")
    private String zydm;

    @ApiModelProperty(value = "专业名称")
    @JsonProperty("ZYMC")
    private String zymc;

    @ApiModelProperty(value = "学生类别")
    @JsonProperty("XSLBMC")
    private String xslbmc;

    @ApiModelProperty(value = "学院代码")
    @JsonProperty("XYDM")
    private String xydm;

    @ApiModelProperty(value = "学院名称")
    @JsonProperty("XYMC")
    private String xymc;

    @ApiModelProperty(value = "年级")
    @JsonProperty("NJ")
    private String nj;

    @ApiModelProperty(value = "入学日期")
    @JsonProperty("RXRQ")
    private String rxrq;

    @ApiModelProperty(value = "学制")
    @JsonProperty("XZ")
    private String xz;

    @ApiModelProperty(value = "性别")
    @JsonProperty("XBMC")
    private String xbmc;

    @ApiModelProperty(value = "学籍状态名称")
    @JsonProperty("XJZTMC")
    private String xjztmc;

    @ApiModelProperty(value = "数据来源")
    @JsonProperty("SJLY")
    private String sjly;

    @ApiModelProperty(value = "移动电话")
    @JsonProperty("LXDH")
    private String yddh;

}

