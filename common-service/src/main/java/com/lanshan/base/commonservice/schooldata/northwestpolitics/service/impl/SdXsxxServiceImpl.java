package com.lanshan.base.commonservice.schooldata.northwestpolitics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dao.SdXsxxMapper;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.MasterContactDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.StudentBasicInfoDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.StudentContactDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdXsxx;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdXsxxService;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.NorthwestPoliticsUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生信息表 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-07
 */

@Slf4j
@Service
public class SdXsxxServiceImpl extends ServiceImpl<SdXsxxMapper, SdXsxx> implements SdXsxxService {

    @Resource
    private SdXsxxMapper sdXsxxMapper;

    @Override
    public void syncSdXsx() {
        List<StudentBasicInfoDTO> basicList = NorthwestPoliticsUtil.getStudentBasicInfo(null);
        List<StudentContactDTO> contactList = NorthwestPoliticsUtil.getStudentContactInfo(null);

        List<MasterContactDTO> contactMasterList = NorthwestPoliticsUtil.getMasterContactInfo(null);
        if (CollUtil.isEmpty(basicList) || CollUtil.isEmpty(contactList)) {
            log.info("学生信息为空，同步终止");
            return;
        }
        sdXsxxMapper.truncate();
        //联系方式
        Map<String, StudentContactDTO> contactMap = contactList.parallelStream()
                .collect(Collectors.toMap(StudentContactDTO::getXH, s -> s,(existing, newValue) -> newValue));

        Map<String, MasterContactDTO> contactMasterMap = contactMasterList.parallelStream()
                .collect(Collectors.toMap(MasterContactDTO::getXH, s -> s,(existing, newValue) -> newValue));

        List<SdXsxx> records = new ArrayList<>(basicList.size());
        SdXsxx record = null;
        for (StudentBasicInfoDTO dto : basicList) {
            record = new SdXsxx();
            record.setXh(dto.getXh());
            record.setXm(dto.getXm());
            record.setXbm(dto.getXb());
            record.setYxdm(dto.getXybm());
            record.setYxmc(dto.getXymc());
            record.setZymc(dto.getZymc());
            record.setBjmc(dto.getBjmc());
            record.setNj(dto.getNj());
            record.setXqmc(dto.getXqmc());
            record.setLxsj(dto.getYjbysj());
            record.setPycc(dto.getRylx());
            record.setRxrq(dto.getRxrq());

            String sfzjh = SM4Util.decryptSM4(dto.getSfzjh());
            record.setSfzjh(sfzjh);
            StudentContactDTO contactDTO = contactMap.get(dto.getXh());
            if (contactDTO != null) {
                String lxdh = SM4Util.decryptSM4(contactDTO.getLXDH());
                String sjhm = SM4Util.decryptSM4(contactDTO.getSJHM());
                record.setLxdh(lxdh);
                record.setSjh(sjhm);
            }
            MasterContactDTO masterContactDTO = contactMasterMap.get(dto.getXh());
            if (masterContactDTO != null) {
                record.setLxdh(masterContactDTO.getSJHM());
            }
            records.add(record);
        }
        SdXsxxServiceImpl proxy = (SdXsxxServiceImpl) AopContext.currentProxy();
        proxy.insertBatch(records);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<SdXsxx> records) {
        List<List<SdXsxx>> batchList = CollUtil.split(records, 2000);
        for (List<SdXsxx> batch : batchList) {
            sdXsxxMapper.insertBatch(batch);
        }
    }


}
