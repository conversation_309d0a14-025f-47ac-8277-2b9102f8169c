package com.lanshan.base.commonservice.standardapp.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * Boolean类型转换器，将true转为"是"，false转为"否"
 */
public class BooleanYesNoConverter implements Converter<Boolean> {

    @Override
    public WriteCellData<?> convertToExcelData(Boolean value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value != null && value) {
            return new WriteCellData<>("是");
        }
        return new WriteCellData<>("否");
    }
}