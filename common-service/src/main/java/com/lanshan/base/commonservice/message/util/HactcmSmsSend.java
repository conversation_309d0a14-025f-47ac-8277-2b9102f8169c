package com.lanshan.base.commonservice.message.util;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.util.DigestUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Slf4j
public class HactcmSmsSend {

    private static final String API_URL = "https://gateway.hactcm.edu.cn/mp_message_pocket_web-mp-restful-message-send/ProxyService/message_pocket_web-mp-restful-message-sendProxyService";
    private static final String ACCESS_TOKEN = "ed94aba5aa0451f5b9b111697d285fec";
    private static final String APP_ID = "1305567900423020544";

    private static final String SCHOOL_CODE = "10471";

    private final static String CONTENT_TEMPLATE = "验证码为:%s,您正在使用该手机号进行企业微信绑定操作。验证码10分钟内有效，请勿泄露。";

    /**
     * 发送短信验证码
     *
     * @param phone  手机号码
     * @param code   验证码
     */
    public static void sendMessage(String phone, String code) throws IOException, JSONException {
        URL url = new URL(API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json; utf-8");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("accessToken", ACCESS_TOKEN);
        connection.setRequestProperty("appId", APP_ID);

        // 构建 JSON 请求体
        JSONObject jsonInput = new JSONObject();
        jsonInput.put("sign", getSign(phone));
        jsonInput.put("schoolCode", SCHOOL_CODE);
        jsonInput.put("msgType", "2");
        jsonInput.put("content", String.format(CONTENT_TEMPLATE, code));
        jsonInput.put("sendType", "4");
        jsonInput.put("receiverType", "1");
        JSONObject receiver = new JSONObject();
        receiver.put("userId", phone);
        receiver.put("mobile", phone);
        jsonInput.put("receivers", new JSONArray().put(receiver));
        connection.setDoOutput(true);
        log.info("短信验证码发送请求参数: " + jsonInput);
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInput.toString().getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        int responseCode = connection.getResponseCode();
        log.info("短信验证码发送请求响应Code : " + responseCode);
        //处理响应
        //例如读取响应内容
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            log.info("短信验证码发送请求响应: " + response);
        }

        connection.disconnect();
    }

    private static String getSign(String userId) {
        return DigestUtils.md5DigestAsHex((ACCESS_TOKEN + SCHOOL_CODE + userId).getBytes(StandardCharsets.UTF_8));
    }
}
