package com.lanshan.base.commonservice.identify.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: capability-platform-base
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChangeCurrentIdentifyDto extends IdentifyCommonDto{
    @ApiModelProperty(value = "览山用户id")
    private String lanshanUserId;
    @ApiModelProperty(value = "新身份id")
    private Long identifyId;
}
