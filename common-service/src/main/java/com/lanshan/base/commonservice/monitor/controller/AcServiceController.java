package com.lanshan.base.commonservice.monitor.controller;


import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.monitor.enums.MonitorOperateEnum;
import com.lanshan.base.commonservice.monitor.model.vo.AcServiceVO;
import com.lanshan.base.commonservice.monitor.service.AcServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 应用服务信息表(AcService)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("acService")
@Api(tags = "应用服务信息表(AcService)控制层", hidden = true)
public class AcServiceController {
    /**
     * 服务对象
     */
    @Resource
    private AcServiceService acServiceService;

    /**
     * 查询所有服务数据
     *
     * @return 所有数据
     */
    @RequiresPermissions("api:acService:list")
    @ApiOperation("分页查询所有数据")
    @GetMapping("/listAll")
    public Result<List<AcServiceVO>> listAll() {
        return Result.build(acServiceService.listAll());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @RequiresPermissions("api:acService:list")
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/getOne/{id}")
    public Result<AcServiceVO> selectOne(@PathVariable Long id) {
        return Result.build(this.acServiceService.selectOne(id));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @RequiresPermissions("api:acService:add")
    @ApiOperation("新增数据")
    @PostMapping("/create")
    public Result<Boolean> insert(@RequestBody AcServiceVO vo) {
        return Result.build(this.acServiceService.insert(vo));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @RequiresPermissions("api:acService:edit")
    @ApiOperation("修改数据")
    @PostMapping("/update/put")
    public Result<Boolean> update(@RequestBody AcServiceVO vo) {
        return Result.build(this.acServiceService.update(vo));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @RequiresPermissions("api:acService:remove")
    @ApiOperation("删除数据")
    @PostMapping("/delete/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.acServiceService.delete(idList));
    }
    @RequiresPermissions("api:acService:operate")
    @ApiOperation("操作应用服务")
    @GetMapping("/operate")
    public Result<Boolean> start(@RequestParam(value = "id") Long id, @RequestParam(value = "operate") Integer operate, HttpServletRequest request) {
        return Result.build(acServiceService.doOperate(id, MonitorOperateEnum.getEnum(operate), request));
    }
}

