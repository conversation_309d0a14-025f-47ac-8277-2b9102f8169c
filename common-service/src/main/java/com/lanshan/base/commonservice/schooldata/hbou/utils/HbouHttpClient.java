package com.lanshan.base.commonservice.schooldata.hbou.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class HbouHttpClient {

    /**
     * 发送POST请求并返回响应
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @param resourceName 资源名称（用于日志）
     * @return 响应字符串
     */
    public String post(String url, String body, Map<String, String> headers, String resourceName) {

            log.info("湖北开放大学{}请求: URL={}, Body={}",
                    resourceName, url, body);
            try (HttpResponse response = HttpUtil.createPost(url)
                    .body(body)
                    .addHeaders(headers)
                    .execute()) {

                String result = response.body();
                log.info("湖北开放大学{}响应: Status={}, Body={}", resourceName, response.getStatus(), result);

                // 检查HTTP状态码
                if (response.getStatus() != 200) {
                    throw ExceptionCodeEnum.BAD_REQUEST.toServiceException(
                            "请求湖北开放大学" + resourceName + "接口失败！HTTP状态码: " + response.getStatus() + ", 响应: " + result);
                }
                return result;
            }
    }
}
