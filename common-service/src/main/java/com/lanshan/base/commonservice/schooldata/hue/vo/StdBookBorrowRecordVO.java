package com.lanshan.base.commonservice.schooldata.hue.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 图书借阅记录表(StdBookBorrowRecord)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "图书借阅记录表VO")
@Data
@ToString
public class StdBookBorrowRecordVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "图书条码号")
    private String tstmh;

    @ApiModelProperty(value = "图书名称")
    private String tsmc;

    @ApiModelProperty(value = "图书索书号")
    private String tsssh;

    @ApiModelProperty(value = "读者条码号")
    private String dztmh;

    @ApiModelProperty(value = "读者证件号（学号/教工号）")
    private String dzzjh;

    @ApiModelProperty(value = "读者姓名")
    private String dzxm;

    @ApiModelProperty(value = "借书日期")
    private String jsrq;

    @ApiModelProperty(value = "还书日期")
    private String hsrq;

    @ApiModelProperty(value = "应还日期")
    private String yhrq;

    @ApiModelProperty(value = "续借标志")
    private String xjbs;

    @ApiModelProperty(value = "续借日期")
    private String xjrq;

    @ApiModelProperty(value = "借书经手人")
    private String jsjsr;

    @ApiModelProperty(value = "还书经手人")
    private String hsjsr;

    @ApiModelProperty(value = "登记号（财产号）")
    private String djh;

    @ApiModelProperty(value = "ISBN号")
    private String isbnh;

    @ApiModelProperty(value = "责任者（作者）")
    private String zrz;

    @ApiModelProperty(value = "是否归还(0未还 1已还)")
    private String sfgh;

    @ApiModelProperty(value = "记录类型")
    private String jllx;

    @ApiModelProperty(value = "时间戳")
    private String tstamp;

    @ApiModelProperty(value = "借阅状态 1 在借中 2 续借中 3 将过期 4 已过期")
    private String borrowStatus;
}

