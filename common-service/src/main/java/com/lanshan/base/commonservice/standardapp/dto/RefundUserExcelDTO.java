package com.lanshan.base.commonservice.standardapp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.Data;

@Data
@HeadFontStyle(fontHeightInPoints = 12)
public class RefundUserExcelDTO {
    @ColumnWidth(14)
    @HeadStyle(fillForegroundColor = 10)
    @ExcelProperty("姓名")
    private String userName;
    @ColumnWidth(14)
    @HeadStyle(fillForegroundColor = 10)
    @ExcelProperty("学工号")
    private String userId;

    @ColumnWidth(25)
    @ExcelProperty("开户银行")
    private String bankName;

    @ColumnWidth(25)
    @ExcelProperty("银行卡号")
    private String bankNumber;

    @ColumnWidth(20)
    @ExcelProperty("持卡人姓名")
    private String bankUserName;

    @ColumnWidth(20)
    @ExcelProperty("校园网余额")
    private String balance;

    @ColumnWidth(20)
    @ExcelProperty("一卡通余额")
    private String balanceCard;

}
