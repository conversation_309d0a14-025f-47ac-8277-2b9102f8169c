package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作台广告图信息表(WbAppBanner)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WbAppBanner extends Model<WbAppBanner> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 广告类型。1：智慧学工；2:智慧教工
     */
    private Integer bannerType;
    /**
     * 位置类型
     */
    private Integer locationType;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 合成图片配图
     */
    private Object canvasPicture;
    /**
     * 合并图片拼接文案
     */
    private Object canvasDescription;
    /**
     * 合成图片
     */
    private String canvas;
    /**
     * 链接类型 0：无链接；1：内部跳转；2：应用跳转；3：外部跳转
     */
    private Integer linkType;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 跳转链接
     */
    private String linkUrl;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 标记
     */
    private String mark;
    /**
     * 排序。升序
     */
    private Integer sort;
    /**
     * 是否发布
     */
    private Boolean isRelease;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

