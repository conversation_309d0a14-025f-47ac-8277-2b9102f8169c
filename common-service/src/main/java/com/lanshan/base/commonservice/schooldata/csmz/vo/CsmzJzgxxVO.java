package com.lanshan.base.commonservice.schooldata.csmz.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 教职工信息(CsmzJzgxx)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "教职工信息VO")
@Data
@ToString
public class CsmzJzgxxVO implements Serializable {

    @ApiModelProperty(value = "职工号")
    private String zgh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "性别代码")
    private String xbdm;

    @ApiModelProperty(value = "性别")
    private String xbmz;

    @ApiModelProperty(value = "所在单位代码")
    private String szdwdm;

    @ApiModelProperty(value = "所在单位名称")
    private String szdwmz;

    @ApiModelProperty(value = "当前状态代码")
    private String dqztdm;

    @ApiModelProperty(value = "当前状态名称")
    private String dqztmz;

    @ApiModelProperty(value = "教职工类别码")
    private String jzglbm;

    @ApiModelProperty(value = "手机号码")
    private String yddh;

    @ApiModelProperty(value = "职务名称")
    private String zwmc;
}

