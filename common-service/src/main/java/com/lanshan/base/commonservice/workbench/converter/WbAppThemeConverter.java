package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppTheme;
import com.lanshan.base.commonservice.workbench.vo.WbAppThemeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工作台应用主题信息主表(WbAppTheme)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppThemeConverter {

    WbAppThemeConverter INSTANCE = Mappers.getMapper(WbAppThemeConverter.class);

    WbAppThemeVO toVO(WbAppTheme entity);

    WbAppTheme toEntity(WbAppThemeVO vo);

    List<WbAppThemeVO> toVO(List<WbAppTheme> entityList);

    List<WbAppTheme> toEntity(List<WbAppThemeVO> voList);
}


