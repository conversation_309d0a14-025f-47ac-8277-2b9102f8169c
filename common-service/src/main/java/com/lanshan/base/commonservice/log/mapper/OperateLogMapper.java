package com.lanshan.base.commonservice.log.mapper;

import com.lanshan.base.commonservice.log.entity.OperateLogEntity;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (operate_log)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-21 17:29:56
 */
public interface OperateLogMapper {

    /**
     * 新增数据
     *
     * @param operateLogEntity 实例对象
     * @return 影响行数
     */
    int insert(OperateLogEntity operateLogEntity);






}

