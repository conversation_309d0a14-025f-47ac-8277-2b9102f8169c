package com.lanshan.base.commonservice.group.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "课程群聊详情VO")
@Data
public class MsgCourseGroupChatDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "是否入群 0:否 1:是")
    private Integer isJoin;

    @ApiModelProperty(value = "是否在企微中 0:否 1:是")
    private Integer isInCp;

    @ApiModelProperty(value = "教师名称列表")
    private List<String> teacherNameList;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "教师学工号列表")
    private List<String> teacherGhList;
}

