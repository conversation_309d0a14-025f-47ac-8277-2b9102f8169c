package com.lanshan.base.commonservice.workbench.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 应用访问量排行VO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "应用访问量排行VO", description = "应用访问量排行VO")
@Builder
public class AppAccessRankVO implements Serializable {
    private static final long serialVersionUID = -6051156235630189371L;
    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID")
    private Long appId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 访问量
     */
    @ApiModelProperty(value = "访问量")
    private Long accessCount;
}
