package com.lanshan.base.commonservice.standardapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.standardapp.dao.RefundSettingDao;
import com.lanshan.base.commonservice.standardapp.entity.RefundSetting;
import com.lanshan.base.commonservice.standardapp.qo.RefundSettingQO;
import com.lanshan.base.commonservice.standardapp.service.RefundSettingService;
import com.lanshan.base.commonservice.standardapp.vo.RefundSettingVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 退费设置服务实现类
 */
@Service
public class RefundSettingServiceImpl extends ServiceImpl<RefundSettingDao, RefundSetting>
        implements RefundSettingService {

    @Override
    public List<RefundSettingVO> getRefundSettingList(RefundSettingQO qo) {
        LambdaQueryWrapper<RefundSetting> wrapper = new LambdaQueryWrapper<>();

        if (qo.getYear() != null) {
            wrapper.eq(RefundSetting::getYear, qo.getYear());
        }
        if (qo.getTransferDate() != null) {
            wrapper.eq(RefundSetting::getTransferDate, qo.getTransferDate());
        }
        if (qo.getRefundDate() != null) {
            wrapper.eq(RefundSetting::getRefundDate, qo.getRefundDate());
        }

        wrapper.orderByDesc(RefundSetting::getYear);

        return this.list(wrapper).stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean saveRefundSetting(RefundSettingVO vo) {
        Integer year = vo.getYear();
        LambdaQueryWrapper<RefundSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundSetting::getYear, year);
        RefundSetting yearData = this.getOne(wrapper);

        if (yearData != null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("该年份的退费设置已存在");
        }

        if (vo.getRefundDate() != null && vo.getTransferDate() != null
                && vo.getRefundDate().compareTo(vo.getTransferDate()) <= 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("退费日期必须大于转账日期");
        }

        //disableDate大于转账日期小于退费日期
        if (vo.getDisableDate() != null && vo.getTransferDate() != null
                && vo.getDisableDate().compareTo(vo.getTransferDate()) <= 0
                && vo.getDisableDate().compareTo(vo.getRefundDate()) >= 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("禁止日期必须大于转账日期小于退费日期");
        }

        RefundSetting entity = new RefundSetting();
        BeanUtils.copyProperties(vo, entity);
        return this.save(entity);
    }

    @Override
    public boolean updateRefundSetting(RefundSettingVO vo) {
        // 检查退费日期是否大于转账日期
        if (vo.getRefundDate() != null && vo.getTransferDate() != null
                && vo.getRefundDate().compareTo(vo.getTransferDate()) <= 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("退费日期必须大于转账日期");
        }
        //disableDate大于转账日期小于退费日期
        if (vo.getDisableDate() != null && vo.getTransferDate() != null
                && vo.getDisableDate().compareTo(vo.getTransferDate()) <= 0
                && vo.getDisableDate().compareTo(vo.getRefundDate()) >= 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("禁止日期必须大于转账日期小于退费日期");
        }

        RefundSetting entity = new RefundSetting();
        BeanUtils.copyProperties(vo, entity);
        return this.updateById(entity);
    }

    @Override
    public boolean deleteRefundSetting(Integer id) {
        return this.removeById(id);
    }

    @Override
    public RefundSettingVO getRefundSettingByYear(Integer year) {
        LambdaQueryWrapper<RefundSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundSetting::getYear, year);
        RefundSetting entity = this.getOne(wrapper);
        if (entity == null) {
            return null;
        }
        return convertToVO(entity);
    }

    private RefundSettingVO convertToVO(RefundSetting entity) {
        RefundSettingVO vo = new RefundSettingVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}