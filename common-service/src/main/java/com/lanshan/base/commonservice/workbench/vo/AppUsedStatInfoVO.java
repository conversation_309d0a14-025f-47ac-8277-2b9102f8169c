package com.lanshan.base.commonservice.workbench.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 应用使用情况VO
 */
@Data
@ApiModel(value = "应用使用情况VO")
public class AppUsedStatInfoVO {

    @ApiModelProperty("应用ID")
    private Long appId;

    @ApiModelProperty("已使用人数")
    private Integer usedUser;

    @ApiModelProperty("已使用学生人数")
    private Integer usedStuUser;

    @ApiModelProperty("已使用教师人数")
    private Integer usedTchUser;

    @ApiModelProperty("已使用本科生人数")
    private Integer usedStuUndergraduateUser;

    @ApiModelProperty("已使用研究生人数")
    private Integer usedStuMasterUser;

    @ApiModelProperty("已使用其他人数")
    private Integer usedOtherUser;

    @ApiModelProperty("标签ID")
    private String tagId;

}
