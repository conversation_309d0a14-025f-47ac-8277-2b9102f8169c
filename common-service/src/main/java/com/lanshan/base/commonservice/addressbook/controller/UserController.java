package com.lanshan.base.commonservice.addressbook.controller;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.dto.user.UserDeptIdsTagIdsDto;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.dto.UpdateUserTagsDto;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 * @Description: 成员管理通用接口
 * @Author: GaoJian
 * @Date: 2023/10/27
 */
@RestController
@RequestMapping(value = ServiceConstant.COMMON_ADDRESS_BOOK)
@Validated
@Api(tags = "成员管理通用接口", hidden = true)
public class UserController {

    @Resource
    private UserService userService;

    @RequiresPermissions("work:addressBook:list")
    @ApiOperation("获取所有用户id")
    @PostMapping(value = "selectAllUserid")
    public Result<Set<String>> selectAllUserid(@RequestBody UserInfoPageQo userInfoPageQo) {
        return Result.build(userService.selectAllUserid(userInfoPageQo));
    }

    @ApiOperation("根据用户id列表获取成员用户列表")
    @PostMapping(value = "listUsersByUseridList")
    public Result<List<UserInfoVo>> listUsersByUseridList(@RequestBody @Validated @NotEmpty List<String> useridList) {
        return Result.build(userService.listUsersByUseridList(useridList));
    }

    @ApiOperation("获取部门成员(仅当前部门)")
    @PostMapping(value = "listUsersByDepartmentid")
    public Result<List<UserInfoVo>> listUsersByDepartmentid(@RequestParam Long departmentid) {
        return Result.build(userService.listUsersByDepartmentid(departmentid));
    }

    @ApiOperation("批量获取部门成员(仅当前部门)")
    @PostMapping(value = "listUsersByDeptIds")
    public Result<List<UserInfoPartVO>> listUsersByDeptIds(@RequestBody List<Long> deptIds) {
        return Result.build(userService.listUsersByDeptIds(deptIds));
    }

    @RequiresPermissions("work:addressBook:list")
    @ApiOperation("获取部门成员（包含当前部门及其下全部子部门，路径匹配查询）")
    @PostMapping(value = "listAllUsersByDepartmentid")
    public Result<List<UserInfoVo>> listAllUsersByDepartmentid(@RequestParam Long departmentid) {
        return Result.build(userService.listAllUsersByDepartmentid(departmentid));
    }

    @RequiresPermissions("work:addressBook:list")
    @ApiOperation("分页获取用户详情信息（用于完整信息查询搜索）")
    @PostMapping(value = "pageUserInfo")
    public Result<Page<UserInfoVo>> pageUserInfo(@RequestBody UserInfoPageQo userInfoPageQo) {
        return Result.build(userService.pageUserInfo(userInfoPageQo));
    }

    @RequiresPermissions("work:addressBook:list")
    @ApiOperation("获取用户总数（用于完整信息查询搜索）")
    @PostMapping(value = "getUserInfoCount")
    public Result<Integer> getUserInfoCount(@RequestBody UserInfoPageQo userInfoPageQo) {
        return Result.build(userService.getUserInfoCount(userInfoPageQo));
    }

    @ApiOperation("校验用户是否存在")
    @PostMapping(value = "listNotExistUser")
    public Result<List<String>> listNotExistUser(@RequestBody @NotEmpty List<String> userIds) {
        return Result.build(userService.listNotExistUser(userIds));
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("修改企微通讯录成员的手机号")
    @PostMapping(value = "changeMobile")
    public Result<Object> changeMobile(@RequestBody ChangeMobileQo qo) {
        userService.changeMobile(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:list")
    @ApiOperation("根据用户id获取部门ids、标签ids")
    @GetMapping(value = "listDeptAndTagByUserId")
    public Result<UserDeptIdsTagIdsDto> getDeptIdsAndTagIdsByUserId(@RequestParam String userid) {
        return Result.build(userService.getDeptIdsAndTagIdsByUserId(userid));
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("新增或更新用户（包含标签）")
    @PostMapping(value = "saveOrUpdateUserWithTag")
    public Result<Object> saveOrUpdateUserWithTag(@RequestBody @Validated UserSaveWithTagQo qo) {
        return Result.build(userService.saveUserWithTag(qo));
    }

    @ApiOperation("变更用户标签")
    @PostMapping(value = "updateUserTags")
    public Result<Object> updateUserTags(@RequestBody @Validated UpdateUserTagsDto dto) {
        return Result.build(userService.updateUserTags(dto));
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量新增用户部门")
    @PostMapping(value = "batchUserSaveDept")
    public Result<Object> batchUserSaveDept(@RequestBody @Validated BatchUserDeptQo qo) {
        userService.batchUserSaveDept(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量更新用户部门（会删除成员之前的部门关联）")
    @PostMapping(value = "batchUserUpdateDept")
    public Result<Object> batchUserUpdateDept(@RequestBody @Validated BatchUserDeptQo qo) {
        userService.batchUserUpdateDept(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("导入通讯录")
    @PostMapping(value = "importUser")
    public Result<Object> importUser(@RequestPart(value = "file") MultipartFile file, HttpServletResponse response) throws IOException {
        userService.importUser(file, response);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:export")
    @ApiOperation("导出通讯录")
    @PostMapping(value = "exportUser")
    public void exportUser(@RequestBody UserInfoPageQo userInfoPageQo, HttpServletResponse response) throws IOException {
        userService.exportUser(userInfoPageQo, response);
    }

    @RequiresPermissions("work:addressBook:export")
    @ApiOperation("下载导入通讯录模板")
    @GetMapping(value = "downloadImportUserTemplate")
    public void downloadImportUserTemplate(HttpServletResponse response) {
        userService.downloadImportUserTemplate(response);
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量启用或禁用用户")
    @PostMapping(value = "batchEnableOrForbidUser")
    public Result<Object> batchEnableOrForbidUser(@RequestBody @Validated BatchEnableOrdForbidUserQo qo) {
        userService.batchEnableOrForbidUser(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:remove")
    @ApiOperation("批量删除用户")
    @PostMapping(value = "batchDelUser")
    public Result<Object> batchDelUser(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<String> useridList) {
        userService.batchDelUser(useridList);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("邀请成员")
    @PostMapping(value = "batchInvite")
    public Result<Object> batchInvite(@RequestBody BatchInviteQo qo) {
        userService.batchInvite(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:list")
    @GetMapping("getUserByNameAndMobile")
    @ApiOperation("根据姓名与手机号查询用户")
    public Result<UserInfoVo> getUserByNameAndMobile(@RequestParam String name, @RequestParam String mobile) {
        return Result.build(userService.getUserByNameAndMobile(name, mobile));
    }

    /**
     * 根据姓名查询用户  访客预约小程序使用 通讯录获取不到手机号，所以无法验证。此处改成只根据姓名查询，仅用于访客预约小程序演示使用
     * @param name
     * @return
     */
    @RequiresPermissions("work:addressBook:list")
    @GetMapping("getUserByName")
    @ApiOperation("根据姓名查询用户")
    public Result<UserInfoVo> getUserByName(@RequestParam String name) {
        return Result.build(userService.getUserByName(name));
    }

    @GetMapping("getUserByUserid")
    @ApiOperation("根据用户id查询用户")
    public Result<UserInfoVo> getUserByUserid(@RequestParam String userid) {
        return Result.build(userService.getUserByUserid(userid));
    }

    @GetMapping("listCacheUserByUserids")
    @ApiOperation("根据用户id查询用户")
    public Result<List<UserInfoVo>> listCacheUserByUserids(@RequestParam Collection<String> userid) {
        return Result.build(JacksonUtils.toObj(JacksonUtils.toJson(userService.listCacheUserByUserids(userid)), new TypeReference<>() {
        }));
    }

    @RequiresPermissions("work:addressBook:remove")
    @ApiOperation("批量删除企微用户")
    @PostMapping(value = "batchDelCpUser")
    public Result<Object> batchDelCpUser(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<String> useridList) {
        userService.batchDelCpUser(useridList);
        return Result.build();
    }

    @GetMapping("getCurrentUser")
    @ApiOperation("获取当前用户信息")
    public Result<UserInfoVo> getUserByUserid() {
        String userid = SecurityContextHolder.getUserId();
        return Result.build(userService.getUserByUserid(userid));
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量更新用户部门（会删除成员之前的部门关联）")
    @PostMapping(value = "batchUserUpdateDeptV2")
    public Result<Object> batchUserUpdateDeptV2(@RequestBody @Validated BatchUserDeptQo qo) {
        userService.batchUserUpdateDeptV2(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量启用或禁用用户V2")
    @PostMapping(value = "batchEnableOrForbidUserV2")
    public Result<Object> batchEnableOrForbidUserV2(@RequestBody @Validated BatchEnableOrdForbidUserQo qo) {
        userService.batchEnableOrForbidUserV2(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量新增用户部门V2")
    @PostMapping(value = "batchUserSaveDeptV2")
    public Result<Object> batchUserSaveDeptV2(@RequestBody @Validated BatchUserDeptQo qo) {
        userService.batchUserSaveDeptV2(qo);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:edit")
    @ApiOperation("批量删除用户V2")
    @PostMapping(value = "batchDelUserV2")
    public Result<Object> batchDelUserV2(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<String> useridList) {
        userService.batchDelUserV2(useridList);
        return Result.build();
    }

    @RequiresPermissions("work:addressBook:remove")
    @ApiOperation("异步批量删除用户")
    @PostMapping(value = "batchDelUserSync")
    public Result<Object> batchDelUserSync(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<String> useridList) {
        userService.batchDelUserSync(useridList);
        return Result.build();
    }

    @ApiOperation("提交用户信息并开通企微")
    @PostMapping(value = "submitUserInfo")
    public Result<Object> submitUserInfo(@RequestBody @Validated SubmitUserInfoQo qo) {
        return Result.build(userService.submitUserInfo(qo));
    }

    /**
     * 获取所有已激活的身份为学生的用户
     * <AUTHOR> yang.
     * @since 2025/4/28 11:34
     */
    @PostMapping(value = "getActiveUsers",produces = "application/json;charset=UTF-8")
    public Result<Collection<UserInfoPartVO>> getActiveUsers() {
        return Result.build(userService.getActiveUsers());
    }
}
