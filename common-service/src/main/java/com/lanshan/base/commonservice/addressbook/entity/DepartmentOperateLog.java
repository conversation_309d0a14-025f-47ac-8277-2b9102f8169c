package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * (DepartmentOperateLog)表实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 18:04:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentOperateLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "operate_detail")
    private String operateDetail;
    @TableField(value = "operate_person")
    private String operatePerson;
    @TableField(value = "operate_time")
    private Date operateTime;
    /**
     * 1:新增  2 修改，3 删除
     */
    @TableField(value = "operate_log")
    private Integer operateLog;
    /**
     * 部门path
     */
    @TableField(value = "department_path")
    private String departmentPath;
    /**
     * 1:手动 2自动
     */
    @TableField(value = "operate_type")
    private Integer operateType;

    public DepartmentOperateLog(String operateDetail, String operatePerson, Integer operateLog, String departmentPath, Integer operateType) {
        this.operateDetail = operateDetail;
        this.operatePerson = operatePerson;
        this.operateLog = operateLog;
        this.departmentPath = departmentPath;
        this.operateType = operateType;
    }
}

