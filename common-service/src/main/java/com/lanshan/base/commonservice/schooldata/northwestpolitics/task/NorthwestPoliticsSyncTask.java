package com.lanshan.base.commonservice.schooldata.northwestpolitics.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 西北政法大学数据同步定时任务
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */


@Slf4j
@Component
public class NorthwestPoliticsSyncTask {

    @Resource
    private SdDwxxService sdDwxxService;
    @Resource
    private SdJzgxxService sdJzgxxService;
    @Resource
    private SdXsxjxxService sdXsxjxxService;
    @Resource
    private SdXsxxService sdXsxxService;
    @Resource
    private SdBsyjsxjxxService sdBsyjsxjxxService;
    @Resource
    private SdSsyjsxjxxService sdSsyjsxjxxService;

    @XxlJob("northwestPoliticsSyncData")
    public void northwestPoliticsSyncData() {
        log.info("同步西北政法大学数据开始");
        TimeInterval timer = DateUtil.timer();
        TimeInterval timerTotal = DateUtil.timer();

        sdDwxxService.syncSdDwxx();
        log.info("同步学校机构耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdJzgxxService.syncSdJzgxx();
        log.info("同步教职工信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdXsxjxxService.syncSdXsxjxx();
        log.info("同步学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdXsxxService.syncSdXsx();
        log.info("同步学生信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdBsyjsxjxxService.syncBsyjsxjxx();
        log.info("同步博士研究生学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdSsyjsxjxxService.syncSsyjsxjxx();
        log.info("同步硕士研究生学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        log.info("同步西北政法大学数据结束总耗时：{}", DateUtil.formatBetween(timerTotal.intervalMs()));
    }

}
