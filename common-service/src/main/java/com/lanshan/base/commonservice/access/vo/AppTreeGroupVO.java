package com.lanshan.base.commonservice.access.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "应用分组树形结构")
@Data
public class AppTreeGroupVO implements Serializable {

    private static final long serialVersionUID = -8870485664970338888L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上级节点ID")
    private Long parentId;

    @ApiModelProperty(value = "包含所有上级节点ID的ID")
    private String idPath;

    @ApiModelProperty(value = "包含所有上级节点名称的名称")
    private String namePath;

    @ApiModelProperty(value = "树唯一标识")
    private String treeKey;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "应用数量")
    private Integer appCount;

    @ApiModelProperty(value = "子节点")
    private List<AppTreeGroupVO> children = new ArrayList<>();
}
