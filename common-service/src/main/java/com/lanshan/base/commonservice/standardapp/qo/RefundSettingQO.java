package com.lanshan.base.commonservice.standardapp.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "RefundSettingQO", description = "退费设置查询条件对象")
public class RefundSettingQO extends PageQo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "转换日期")
    private Date transferDate;

    @ApiModelProperty(value = "退费日期")
    private Date refundDate;

    @ApiModelProperty(value = "捐赠说明")
    private String donateRemark;
}