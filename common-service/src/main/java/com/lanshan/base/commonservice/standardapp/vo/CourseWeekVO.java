package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "学期课程表VO")
public class CourseWeekVO implements Serializable {
    private static final long serialVersionUID = -7905742651308927498L;

    @ApiModelProperty(value = "学期的第几周")
    private String weekNo;

    @ApiModelProperty(value = "周课程表")
    private List<CourseDayVO> courseDayList;
}
