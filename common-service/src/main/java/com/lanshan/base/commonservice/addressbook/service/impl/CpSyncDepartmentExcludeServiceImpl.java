package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.qo.user.SyncDepartmentExcludePageQo;
import com.lanshan.base.api.qo.user.SyncDepartmentExcludeQo;
import com.lanshan.base.commonservice.addressbook.converter.CpSyncDepartmentExcludeConverter;
import com.lanshan.base.commonservice.addressbook.dao.CpSyncDepartmentExcludeDao;
import com.lanshan.base.commonservice.addressbook.entity.CpSyncDepartmentExclude;
import com.lanshan.base.commonservice.addressbook.service.CpSyncDepartmentExcludeService;
import com.lanshan.base.commonservice.addressbook.vo.CpSyncDepartmentExcludeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 同步部门排除表(CpSyncDepartmentExclude)表服务实现类
 */
@Service
public class CpSyncDepartmentExcludeServiceImpl extends ServiceImpl<CpSyncDepartmentExcludeDao, CpSyncDepartmentExclude> implements CpSyncDepartmentExcludeService {

    @Override
    public IPage<CpSyncDepartmentExcludeVO> pageSyncDepartmentExclude(SyncDepartmentExcludePageQo pageQo) {
        IPage<CpSyncDepartmentExclude> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        QueryWrapper<CpSyncDepartmentExclude> qw = Wrappers.query(CpSyncDepartmentExclude.class);

        //部门id
        if (pageQo.getDepartmentid() != null) {
            qw.like("departmentid::TEXT", pageQo.getDepartmentid());
        }
        //部门名称
        if (StringUtils.isNotBlank(pageQo.getName())) {
            qw.like("name", pageQo.getName());
        }

        //分页查询
        IPage<CpSyncDepartmentExclude> result = super.page(page, qw);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQo.getPage(), pageQo.getSize());
        }

        //转换vo
        return result.convert(CpSyncDepartmentExcludeConverter.INSTANCE::toVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addSyncDepartmentExclude(List<SyncDepartmentExcludeQo> qoList) {
        //新增数据
        List<CpSyncDepartmentExclude> entityList = CpSyncDepartmentExcludeConverter.INSTANCE.qoToEntity(qoList);
        super.saveOrUpdateBatch(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delSyncDepartmentExclude(Long departmentid) {
        super.removeById(departmentid);
    }
}

