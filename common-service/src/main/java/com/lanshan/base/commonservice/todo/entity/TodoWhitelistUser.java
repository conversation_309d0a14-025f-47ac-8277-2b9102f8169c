package com.lanshan.base.commonservice.todo.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 应用白名单配置表(TodoWhitelistUser)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class TodoWhitelistUser extends Model<TodoWhitelistUser> {
    private Long id;
    private Long appId;
    private String userId;
    private String userName;
    private String userDepartment;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

