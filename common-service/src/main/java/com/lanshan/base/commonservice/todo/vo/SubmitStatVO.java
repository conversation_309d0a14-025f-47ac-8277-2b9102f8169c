package com.lanshan.base.commonservice.todo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 最近已提交VO
 */
@Data
@ApiModel(description = "最近已提交VO")
public class SubmitStatVO implements Serializable {

    private static final long serialVersionUID = -7406447866250940313L;

    @ApiModelProperty("每小时的提交数")
    private Map<String, Integer> hourOfSubmit;

    @ApiModelProperty("每天的提交数")
    private Map<String, Integer> dayOfSubmit;
}
