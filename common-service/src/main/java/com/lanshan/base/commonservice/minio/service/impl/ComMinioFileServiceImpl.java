package com.lanshan.base.commonservice.minio.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.minio.dao.ComMinioFileDao;
import com.lanshan.base.commonservice.minio.entity.ComMinioFile;
import com.lanshan.base.commonservice.minio.service.ComMinioFileService;
import org.springframework.stereotype.Service;

/**
 * 对象文件信息(ComMinioFile)表服务实现类
 *
 * <AUTHOR>
 */
@Service("comMinioFileService")
public class ComMinioFileServiceImpl extends ServiceImpl<ComMinioFileDao, ComMinioFile> implements ComMinioFileService {

}

