package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 学生课程关联信息表(StdCourseStudent)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class StdCourseStudent extends Model<StdCourseStudent> {
    private static final long serialVersionUID = 4072066544853815439L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 课程ID。课程编号
     */
    private String courseId;
    /**
     * 学生ID。学工号
     */
    private String studentId;

    /**
     * 学生类型 教师/学生
     */
    private String studentType;

    /**
     * 教学班编码
     */
    private String teachClassCode;

    /**
     * 教学班名称
     */
    private String teachClassName;

    /**
     * 直播地址
     */
    private String liveUrl;

    /**
     * 直播类型
     */
    private String liveType;

    /**
     * 会议号
     */
    private String meetingNo;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

