package com.lanshan.base.commonservice.standardapp.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "StdCourseScheduleListQo", description = "教学班列表查询条件对象")
public class StdCourseScheduleListQo extends PageQo implements Serializable {

    @ApiModelProperty(value = "学年")
    private String teachYear;

    @ApiModelProperty(value = "学期")
    private String term;

    @ApiModelProperty(value = "班级类型  2：本科生 3：研究生")
    private String courseType;

    @ApiModelProperty(value = "教学班名称")
    private String teachClasses;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "教师ID")
    private String teacherId;

    @ApiModelProperty(value = "教师姓名")
    private String teacherName;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "学号")
    private String studentId;

    @ApiModelProperty(value = "日历状态 true：已建 false：未建")
    private Boolean syncFlag;

    @ApiModelProperty(value = "课程id")
    private String courseId;

}
