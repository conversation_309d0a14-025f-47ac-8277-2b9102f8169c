package com.lanshan.base.commonservice.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.access.dao.AcAppPushOperateTypeMapper;
import com.lanshan.base.commonservice.access.entity.AcAppPushOperateType;
import com.lanshan.base.commonservice.access.service.AcAppPushOperateTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * (AcAppPushOperateType)服务实现类
 */
@Slf4j
@Service
public class AcAppPushOperateTypeServiceImpl extends ServiceImpl<AcAppPushOperateTypeMapper, AcAppPushOperateType> implements AcAppPushOperateTypeService {

    @Override
    public List<AcAppPushOperateType> listOperateTypeByAppId(Long appId) {
        LambdaQueryWrapper<AcAppPushOperateType> queryWrapper = Wrappers.lambdaQuery(AcAppPushOperateType.class);
        queryWrapper.eq(AcAppPushOperateType::getAppId, appId);
        //根据appId查询操作类型
        List<AcAppPushOperateType> list = super.list(queryWrapper);
        if(CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list;
    }
}
