package com.lanshan.base.commonservice.addressbook.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * (UserInfoQrcode)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class UserInfoQrcode extends Model<UserInfoQrcode> {
    private static final long serialVersionUID = 1117873686994731676L;
    private Long id;
    /**
     * 客服名称
     */
    private String title;
    /**
     * 图片地址
     */
    private String img;
    /**
     * 用户类型(0：其他人员 1：教职工 2：本科生 3：研究生)
     */
    private String userType;
    /**
     * 描述
     */
    private String description;
    /**
     * 启用
     */
    private Boolean enable;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

