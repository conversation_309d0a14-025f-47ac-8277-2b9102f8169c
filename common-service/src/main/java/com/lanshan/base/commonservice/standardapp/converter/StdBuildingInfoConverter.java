package com.lanshan.base.commonservice.standardapp.converter;


import java.util.List;

import com.lanshan.base.commonservice.standardapp.entity.StdBuildingInfo;
import com.lanshan.base.commonservice.standardapp.vo.StdBuildingInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 楼栋信息(StdBuildingInfo)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface StdBuildingInfoConverter {

    StdBuildingInfoConverter INSTANCE = Mappers.getMapper(StdBuildingInfoConverter.class);

    StdBuildingInfoVO toVO(StdBuildingInfo entity);

    StdBuildingInfo toEntity(StdBuildingInfoVO vo);
    
    List<StdBuildingInfoVO> toVO(List<StdBuildingInfo> entityList);

    List<StdBuildingInfo> toEntity(List<StdBuildingInfoVO> voList);
}


