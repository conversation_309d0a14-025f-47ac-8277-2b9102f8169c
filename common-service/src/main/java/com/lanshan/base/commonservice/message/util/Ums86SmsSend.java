package com.lanshan.base.commonservice.message.util;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Random;

/**
 * 兰州交大短信发送平台
 */
@Slf4j
public class Ums86SmsSend {
    private final static String CONTENT_TEMPLATE = "您的验证码为%s，您正在使用该手机号进行企业微信绑定，请于5分钟内正确输入。若非本人操作，请勿转发或泄露。";

    /*
     *企业编号
     */
    private static final String SP_CODE = "255784";

    /*
     * 用户名
     */
    private static final String LOGIN_NAME = "qiyewx";

    /*
     * 用户密码
     */
    private static final String PASSWORD = "39d4a769ae7f306e4b1ccfc9ac06ad2f";

    public static void sendPhoneCode(String phone, String code) {
        String messageContent = String.format(CONTENT_TEMPLATE, code);
        try {
            sendPhone(phone, messageContent);
        } catch (IOException e) {
            log.error("短信发送异常！", e);
        }
    }

    public static void sendPhone(String userNumber, String messageContent) throws IOException {
        // 短信按照70个字数计费为一条短信，但当短信内容大于70字时，即为长短信长短信计费按66个字数计费为一条短信，最长不超过500字
        String urlStr = "https://api.ums86.com:9600/sms/Api/Send.do";

        // 返回值字符串
        StringBuilder sTotalString;
        HttpURLConnection connection = getHttpURLConnection(urlStr);
        try (
                OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "gbk");) {
            // 此处getOutputStream会隐含的进行connect(连接) 编码方式是："GB2312"
            String post = "SpCode=" + SP_CODE +
                    "&LoginName=" + LOGIN_NAME +
                    "&Password=" + PASSWORD +
                    "&MessageContent=" + messageContent +
                    "&UserNumber=" + userNumber +
                    "&SerialNumber=" + generateUniqueId();
            // 发送请求参数
            out.write(post);
            // flush输出流的缓冲
            out.flush();

            sTotalString = new StringBuilder();
            InputStream l_urlStream;
            // 获取URL的响应
            l_urlStream = connection.getInputStream();
            // 定义BufferedReader输入流来读取URL的响应
            BufferedReader l_reader = new BufferedReader(new InputStreamReader(l_urlStream, "gbk"));
            // 循环接收返回值，返回值格式根据ReturnXJ的参数对应返回xml或json【均为String格式，需要用相关方法或者包解析使用】
            String sCurrentLine;
            while ((sCurrentLine = l_reader.readLine()) != null) {
                sTotalString.append(sCurrentLine).append("\r\n");
            }
            log.info("短信发送结果：{}", sTotalString);
        }
    }

    private static @NotNull HttpURLConnection getHttpURLConnection(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        // 创建HttpURLConnection对象
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置HttpURLConnection参数
        // 设定请求的方法为"POST"，默认是GET
        connection.setRequestMethod("POST");
        // 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在http正文内，因此需要设为true,
        // 默认情况下是false;
        connection.setDoOutput(true);
        // 设定传送的内容类型是可序列化的java对象
        // (如果不设此项,在传送序列化对象时,当WEB服务默认的不是这种类型时可能抛java.io.EOFException)
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        return connection;
    }

    public static String generateUniqueId() {
        // 获取当前时间的毫秒数，最多13位数字
        long currentTimeMillis = System.currentTimeMillis();

        // 生成一个0到9999999之间的随机数，7位数字
        Random random = new Random();
        int randomPart = random.nextInt(10000000);

        // 将两部分拼接起来形成20位数字
        return currentTimeMillis + String.format("%07d", randomPart);
    }
}
