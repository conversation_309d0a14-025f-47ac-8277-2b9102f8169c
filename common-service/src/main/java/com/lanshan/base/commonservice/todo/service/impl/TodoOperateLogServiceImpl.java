package com.lanshan.base.commonservice.todo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ResultTypeEnum;
import com.lanshan.base.api.utils.file.ExcelUtils;
import com.lanshan.base.commonservice.todo.entity.TodoOperateLog;
import com.lanshan.base.commonservice.todo.enums.TodoApiTypeEnum;
import com.lanshan.base.commonservice.todo.excel.TodoOperateLogExportDto;
import com.lanshan.base.commonservice.todo.mapper.TodoOperateLogMapper;
import com.lanshan.base.commonservice.todo.qo.TodoOperateLogPageQo;
import com.lanshan.base.commonservice.todo.service.TodoOperateLogService;
import com.lanshan.base.commonservice.todo.vo.TodoOperateLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 待办操作记录表(TodoOperateLog)服务实现类
 */
@Slf4j
@Service
public class TodoOperateLogServiceImpl extends ServiceImpl<TodoOperateLogMapper, TodoOperateLog> implements TodoOperateLogService {

    @Override
    public IPage<TodoOperateLogVo> pageOperateLog(TodoOperateLogPageQo pageQo) {
        IPage<TodoOperateLog> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        LambdaQueryWrapper<TodoOperateLog> queryWrapper = Wrappers.lambdaQuery(TodoOperateLog.class);

        //待办定义id
        if (pageQo.getTodoDefId() != null) {
            queryWrapper.eq(TodoOperateLog::getTodoDefId, pageQo.getTodoDefId());
        }
        //开始、结束时间
        if (pageQo.getStartTime() != null && pageQo.getEndTime() != null) {
            queryWrapper.ge(TodoOperateLog::getCreateDate, pageQo.getStartTime());
            queryWrapper.lt(TodoOperateLog::getCreateDate, DateUtil.offsetDay(pageQo.getEndTime(), 1));
        }

        //按照创建时间倒序排序
        queryWrapper.orderByDesc(TodoOperateLog::getCreateDate);

        //分页查询
        IPage<TodoOperateLog> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQo.getPage(), pageQo.getSize());
        }

        //转换vo
        IPage<TodoOperateLogVo> pageVo = result.convert(item -> BeanUtil.copyProperties(item, TodoOperateLogVo.class));
        //填充字段信息
        for (TodoOperateLogVo vo : pageVo.getRecords()) {
            //设置调用接口类型
            vo.setApiTypeDesc(EnumUtil.getFieldBy(TodoApiTypeEnum::getMsg, TodoApiTypeEnum::getCode, vo.getApiType()));
            //设置状态类型
            vo.setResponseStatusDesc(EnumUtil.getFieldBy(ResultTypeEnum::getMsg, ResultTypeEnum::getCode, vo.getResponseStatus()));
        }
        return pageVo;
    }

    @Override
    public void exportOperateLog(TodoOperateLogPageQo pageQo, HttpServletResponse response) throws IOException {
        //不分页查询
        pageQo.setPage(1L);
        pageQo.setSize(Long.MAX_VALUE);
        //查询接口调用日志
        IPage<TodoOperateLogVo> page = pageOperateLog(pageQo);
        List<TodoOperateLogVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<TodoOperateLogExportDto> exportDtoList = BeanUtil.copyToList(records, TodoOperateLogExportDto.class);
        //导出Excel
        ExcelUtils.exportExcel(response, exportDtoList, "操作记录");
    }
}
