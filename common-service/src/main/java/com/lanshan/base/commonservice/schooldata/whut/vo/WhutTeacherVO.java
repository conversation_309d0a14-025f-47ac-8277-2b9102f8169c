package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 教职工基本信息(WhutTeacher)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "教职工基本信息VO")
@Data
@ToString
public class WhutTeacherVO implements Serializable{

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "企业微信用户id")
    private String qywxUserId;

    @ApiModelProperty(value = "唯一标识")
    private String wybs;

    @ApiModelProperty(value = "所在单位编码")
    private String szdwbm;

    @ApiModelProperty(value = "所在单位名称")
    private String szdwmc;

    @ApiModelProperty(value = "所在科室代码")
    private String szksdm;

    @ApiModelProperty(value = "所在科室名称")
    private String szksmc;

    @ApiModelProperty(value = "工号")
    private String gh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "外文姓名")
    private String wwxm;

    @ApiModelProperty(value = "姓名拼音")
    private String xmpy;

    @ApiModelProperty(value = "曾用名")
    private String cym;

    @ApiModelProperty(value = "性别码")
    private String xbm;

    @ApiModelProperty(value = "性别码代码名称")
    private String xbmdmmc;

    @ApiModelProperty(value = "出生日期")
    private String csrq;

    @ApiModelProperty(value = "出生地码")
    private String csdm;

    @ApiModelProperty(value = "籍贯码")
    private String jgm;

    @ApiModelProperty(value = "民族码")
    private String mzm;

    @ApiModelProperty(value = "民族码代码名称")
    private String mzmdmmc;

    @ApiModelProperty(value = "国籍地区码")
    private String gjdqm;

    @ApiModelProperty(value = "身份证件类型码")
    private String sfzjlxm;

    @ApiModelProperty(value = "身份证件类型码代码名称")
    private String sfzjlxmdmmc;

    @ApiModelProperty(value = "身份证件号")
    private String sfzjh;

    @ApiModelProperty(value = "身份证件有效期")
    private String sfzjyxq;

    @ApiModelProperty(value = "婚姻状况码")
    private String hyzkm;

    @ApiModelProperty(value = "港澳台侨外码")
    private String gatqwm;

    @ApiModelProperty(value = "政治面貌码")
    private String zzmmm;

    @ApiModelProperty(value = "健康状况码")
    private String jkzkm;

    @ApiModelProperty(value = "信仰宗教码")
    private String xyzjm;

    @ApiModelProperty(value = "血型码")
    private String xxm;

    @ApiModelProperty(value = "校区名称")
    private String xqmc;

    @ApiModelProperty(value = "参加工作年月")
    private String cjgzny;

    @ApiModelProperty(value = "来校日期")
    private String lxrq;

    @ApiModelProperty(value = "起薪日期")
    private String qxrq;

    @ApiModelProperty(value = "从教年月")
    private String cjny;

    @ApiModelProperty(value = "用工形式")
    private String ygxs;

    @ApiModelProperty(value = "是否在编")
    private String sfzb;

    @ApiModelProperty(value = "编制类别码")
    private String bzlbm;

    @ApiModelProperty(value = "教职工类别码")
    private String jzglbm;

    @ApiModelProperty(value = "教职工类别码国标")
    private String jzglbmgb;

    @ApiModelProperty(value = "教职工来源码")
    private String jzglym;

    @ApiModelProperty(value = "教职工来源码代码名称")
    private String jzglymdmmc;

    @ApiModelProperty(value = "教职工当前状态码")
    private String jzgdqztm;

    @ApiModelProperty(value = "教职工当前状态码代码名称")
    private String jzgdqztmdmmc;

    @ApiModelProperty(value = "教职工当前状态码国标")
    private String jzgdqztmgb;

    @ApiModelProperty(value = "教职工当前状态码原因校标")
    private String jzgdqztyyxb;

    @ApiModelProperty(value = "教职工当前状态码原因校标代码名称")
    private String jzgdqztyyxbdmmc;

    @ApiModelProperty(value = "最高学历码")
    private String zgxlm;

    @ApiModelProperty(value = "最高学历码代码名称")
    private String zgxlmdmmc;

    @ApiModelProperty(value = "最高学位码")
    private String zgxwm;

    @ApiModelProperty(value = "最高学位码代码名称")
    private String zgxwmdmmc;

    @ApiModelProperty(value = "学科类别码")
    private String xklbm;

    @ApiModelProperty(value = "一级学科码")
    private String yjxkm;

    @ApiModelProperty(value = "二级学科码")
    private String ejxkm;

    @ApiModelProperty(value = "现从事专业")
    private String xcszy;

    @ApiModelProperty(value = "通讯地址")
    private String txdz;

    @ApiModelProperty(value = "移动电话")
    private String yddh;

    @ApiModelProperty(value = "电子邮箱")
    private String dzyx;

    @ApiModelProperty(value = "网络地址")
    private String wldz;

    @ApiModelProperty(value = "即时通讯号")
    private String jstxh;

    @ApiModelProperty(value = "工作日期")
    private String gzrq;

    @ApiModelProperty(value = "办公电话")
    private String bgdh;

    @ApiModelProperty(value = "校园卡号")
    private String xykh;

    @ApiModelProperty(value = "是否双肩挑")
    private String sfsjt;

    @ApiModelProperty(value = "双肩挑岗位类别码")
    private String sjtgwlbm;

    @ApiModelProperty(value = "双肩挑岗位类别码代码名称")
    private String sjtgwlbmdmmc;

    @ApiModelProperty(value = "工龄增减月数")
    private String glzjys;

    @ApiModelProperty(value = "增减工龄说明")
    private String zjglsm;

    @ApiModelProperty(value = "原单位")
    private String ydw;

    @ApiModelProperty(value = "护照种类码")
    private String hzzlm;

    @ApiModelProperty(value = "护照号码")
    private String hzhm;

    @ApiModelProperty(value = "时间戳")
    private String tstamp;

    @ApiModelProperty(value = "岗位类别码")
    private String gwlbm;

    @ApiModelProperty(value = "岗位类别码代码名称")
    private String gwlbmdmmc;

    @ApiModelProperty(value = "岗位等级码")
    private String gwdjm;

    @ApiModelProperty(value = "岗位等级码代码名称")
    private String gwdjmdmmc;
}

