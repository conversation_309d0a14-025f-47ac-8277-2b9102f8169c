package com.lanshan.base.commonservice.schooldata.hue.utils;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/5/19 11:43
 */
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "hue")
public class HueConfigProperties {

    /**
     * 密钥
     */
    private String key;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 成功code
     */
    private Integer successCode;

    /**
     * 基础uri
     */
    private String baseUri;

    /**
     * 获取token的uri
     */
    private String accessTokenUri;

    /**
     * 获取借阅记录的uri
     */
    private String bookBorrowRecordUri;


}
