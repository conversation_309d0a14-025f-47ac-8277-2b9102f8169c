package com.lanshan.base.commonservice.schooldata.northwestpolitics.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生信息表
 * <AUTHOR> yang.
 * @since 2025-05-07
 */

@Data
@TableName(value = "sd_xsxx", schema = "school_data",autoResultMap = true)
public class SdXsxx implements Serializable {

	private static final long serialVersionUID = 1L;

    //学号
    private String xh;

    //姓名
    private String xm;

    //性别码
    private String xbm;

    //院系代码
    private String yxdm;

    //院系名称
    private String yxmc;

    //年级
    private String nj;

    //班级代码
    private String bjdm;

    //班级名称
    private String bjmc;

    //专业代码
    private String zydm;

    //专业名称
    private String zymc;

    //专业类别码
    private String zylbm;

    //专业类别名称
    private String zylbmc;

    //培养层次码
    private String pyccm;

    //培养层次
    private String pycc;

    //学制
    private String xz;

    //学生当前状态码
    private String xsdqztm;

    //学生当前状态
    private String xsdqzt;

    //学籍状态码
    private String xjztm;

    //学籍状态
    private String xjzt;

    //人员类别码
    private String rylbm;

    //人员类别
    private String rylb;

    //联系电话
    private String lxdh;

    //身份证件号
    private String sfzjh;

    //身份证件类型
    private String sfzjhlx;

    //准考证号
    private String zkzh;

    //在校状态
    private String zxzt;

    //离校时间
    private String lxsj;

    //校区名称
    private String xqmc;

    //入学日期
    private String rxrq;

    //手机号
    private String sjh;


}
