package com.lanshan.base.commonservice.group.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.group.entity.GroupChatOperation;
import com.lanshan.base.commonservice.group.qo.GroupChatOperationSearchQo;
import com.lanshan.base.commonservice.group.vo.GroupChatOperationVO;

/**
 * 群聊成员操作表(GroupChatOperation)表服务接口
 *
 * <AUTHOR>
 */
public interface GroupChatOperationService extends IService<GroupChatOperation> {

    /**
     * 分页查询操作记录
     * @param qo
     * @return
     */
    IPage<GroupChatOperationVO> pageGroupChatOperation(GroupChatOperationSearchQo qo);
}

