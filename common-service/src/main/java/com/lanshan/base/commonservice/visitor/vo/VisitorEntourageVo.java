package com.lanshan.base.commonservice.visitor.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("随行人员Vo")
@Data
public class VisitorEntourageVo implements Serializable {
    private static final long serialVersionUID = -81179551734219822L;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("证件类型 1：身份证 2：护照")
    private Integer certificateType;

    @ApiModelProperty("证件类型 1：身份证 2：护照")
    private String certificateTypeDesc;

    @ApiModelProperty("证件号码")
    private String certificateNo;
}

