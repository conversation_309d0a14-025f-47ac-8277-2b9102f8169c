package com.lanshan.base.commonservice.access.converter;


import com.lanshan.base.commonservice.access.dto.AcAppPushLogGroupCountDto;
import com.lanshan.base.commonservice.access.entity.AcAppPushLogCount;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 系统开放APP推送日志次数(AcAppPushLogCount)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface AcAppPushLogCountConverter {

    AcAppPushLogCountConverter INSTANCE = Mappers.getMapper(AcAppPushLogCountConverter.class);

    List<AcAppPushLogCount> toEntity(List<AcAppPushLogGroupCountDto> voList);
}


