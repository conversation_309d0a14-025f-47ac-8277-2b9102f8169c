package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.converter.RefundOpLogConverter;
import com.lanshan.base.commonservice.standardapp.dao.RefundOpLogDao;
import com.lanshan.base.commonservice.standardapp.entity.RefundOpLog;
import com.lanshan.base.commonservice.standardapp.enums.RefundLogOpTypeEnum;
import com.lanshan.base.commonservice.standardapp.qo.RefundOpLogQO;
import com.lanshan.base.commonservice.standardapp.service.RefundOpLogService;
import com.lanshan.base.commonservice.standardapp.vo.RefundOpLogVO;
import com.lanshan.base.commonservice.standardapp.vo.RefundUserVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退费操作日志服务实现类
 */
@Service
public class RefundOpLogServiceImpl extends ServiceImpl<RefundOpLogDao, RefundOpLog> implements RefundOpLogService {

    @Override
    public IPage<RefundOpLogVO> getRefundOpLogList(RefundOpLogQO qo) {
        Page<RefundOpLog> page = new Page<>(qo.getPage(), qo.getSize());


        LambdaQueryWrapper<RefundOpLog> wrapper = Wrappers.lambdaQuery(RefundOpLog.class);

        if (qo.getOpTimeStart() != null) {
            wrapper.ge(RefundOpLog::getOpTime, qo.getOpTimeStart());
        }
        if (qo.getOpTimeEnd() != null) {
            wrapper.le(RefundOpLog::getOpTime, qo.getOpTimeEnd());
        }
        if (qo.getOpUser() != null) {
            wrapper.eq(RefundOpLog::getOpUser, qo.getOpUser());
        }
//        if (qo.getOpType() != null) {
//            wrapper.eq(RefundOpLog::getOpType, qo.getOpType());
//        }
        wrapper.eq(RefundOpLog::getOpType, RefundLogOpTypeEnum.IMPORT);
        wrapper.orderByDesc(RefundOpLog::getOpTime);

        //分页查询
        IPage<RefundOpLog> result = super.page(page, wrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(qo.getPage(), qo.getSize());
        }
        //转换VO
        return result.convert(RefundOpLogConverter.INSTANCE::toVO);
    }

    @Override
    public boolean saveRefundOpLog(RefundOpLogVO vo) {
        RefundOpLog entity = new RefundOpLog();
        BeanUtils.copyProperties(vo, entity);
        entity.setOpTime(new Date());
        entity.setOpUser(SecurityContextHolder.getUserName());
        entity.setOpUserId(SecurityContextHolder.getUserId());
        return this.save(entity);
    }

    @Override
    public boolean updateRefundOpLog(RefundOpLogVO vo) {
        RefundOpLog entity = new RefundOpLog();
        BeanUtils.copyProperties(vo, entity);
        return this.updateById(entity);
    }

    @Override
    public boolean deleteRefundOpLog(Long id) {
        return this.removeById(id);
    }

    private RefundOpLogVO convertToVO(RefundOpLog entity) {
        RefundOpLogVO vo = new RefundOpLogVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}