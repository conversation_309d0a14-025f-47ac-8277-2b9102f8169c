package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.entity.StdGrantIssue;
import com.lanshan.base.commonservice.standardapp.vo.StdGrantIssueVO;

import java.util.List;

/**
 * 学生奖助金发放查询表(StdGrantIssue)表服务接口
 *
 * <AUTHOR>
 */
public interface StdGrantIssueService extends IService<StdGrantIssue> {

    /**
     * 通过工号查询单条数据
     *
     * @return
     */
    List<StdGrantIssueVO> getStdGrantIssue();
}

