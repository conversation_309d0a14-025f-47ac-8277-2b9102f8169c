package com.lanshan.base.commonservice.identify.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import org.apache.ibatis.annotations.Param;

/**
 * (StaffIdentifyRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-10 14:06:25
 */
public interface StaffIdentifyRelationMapper extends BaseMapper<StaffIdentifyRelation> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<StaffIdentifyRelation> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<StaffIdentifyRelation> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<StaffIdentifyRelation> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<StaffIdentifyRelation> entities);

    List<StaffIdentifyRelation> getByStaffId(@Param("staffId") String staffId);
    StaffIdentifyRelation getByNo(@Param("staffNo") String staffNo);

    List<StaffIdentifyRelation> selectListByStaffId(@Param("lanshanUserId") String lanshanUserId);

    StaffIdentifyRelation selectCurrentIdentify(@Param("lanshanUserId") String lanshanUserId);

    StaffIdentifyRelation selectIdentifyByStaffIdAndIdentifyId(@Param("lanshanUserId") String lanshanUserId, @Param("identifyId") Long identifyId);

    void insertByMe(@Param("entity")StaffIdentifyRelation staffIdentifyRelation);

    StaffIdentifyRelation selectIdentifyByActive(@Param("lanshanUserId") String lanshanUserId);

    List<StaffIdentifyRelation> selectAllIdentifyAfterEndTime();

    void updateByIds(@Param("staffIds") List<String> staffIds);

    List<StaffIdentifyRelation> getIdentifysByFilter(@Param("identifyTypes") List<String> distinctIdentify, @Param("identifyStatus") List<String> distinctIdentifyStatus);

    void removeIdentify(@Param("staffIds") List<String> staffIds,@Param("identifyIds") List<Long> newIdentifyIds, @Param("toIdentifyType") String toIdentifyType);

    List<StaffIdentifyRelation> getIdentifysByF(@Param("sClassIdentify") String fromIdentifyType,@Param("identifyStatus") String fromIdentifyStatus);


    List<StaffIdentifyRelation> selectByIdentifyStaus();


    void updateByStaffAndIdentify(@Param("staffId") String staffId,@Param("identifyId") Long identifyId);

    List<StaffIdentifyRelation> selectByIdentifyByStatus(@Param("statusNameList") List<String> statusNameList);

    void updateByStatus(@Param("entity") StaffIdentifyRelation entity);

    List<StaffIdentifyRelation> getNewIdentify(@Param("staffIds") List<String> staffIds);
}

