package com.lanshan.base.commonservice.addressbook.converter;


import java.util.List;

import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 用户信息列表(UserInfo)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface UserInfoConverter {

    UserInfoConverter INSTANCE = Mappers.getMapper(UserInfoConverter.class);

    UserInfoVO toVO(UserInfo entity);

    UserInfo toEntity(UserInfoVO vo);

    List<UserInfoVO> toVO(List<UserInfo> entityList);

    List<UserInfo> toEntity(List<UserInfoVO> voList);
}


