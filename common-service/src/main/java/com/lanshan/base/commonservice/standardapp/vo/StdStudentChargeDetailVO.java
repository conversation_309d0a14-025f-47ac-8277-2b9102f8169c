package com.lanshan.base.commonservice.standardapp.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 学生收费详情查询表(StdStudentChargeDetail)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "学生收费详情查询表VO")
@Data
@ToString
public class StdStudentChargeDetailVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String userId;

    @ApiModelProperty(value = "类型 1:欠费 2:已缴纳")
    private Integer type;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目区间")
    private String projectSection;

    @ApiModelProperty(value = "收费时间")
    private String chargeTime;

    @ApiModelProperty(value = "应交")
    private String payableNum;

    @ApiModelProperty(value = "实缴")
    private String paidNum;

    @ApiModelProperty(value = "减免总计")
    private String derateNum;

    @ApiModelProperty(value = "退费总计")
    private String returnPremiumNum;

    @ApiModelProperty(value = "欠费总计")
    private String arrearageNum;

    @ApiModelProperty(value = "年度")
    private Integer year;
}

