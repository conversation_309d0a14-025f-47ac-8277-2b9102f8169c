package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作台应用主题信息主表(WbAppTheme)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WbAppTheme extends Model<WbAppTheme> {
    private static final long serialVersionUID = -6091196709751786760L;
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 企业微信应用编号
     */
    private Integer agentId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 默认主题
     */
    private String themeDefault;
    /**
     * 当前主题名称
     */
    private String themeCurrentName;
    /**
     * 主题切换状态
     */
    private String switchStatus;
    /**
     * 主题切换时间
     */
    private Date switchTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

