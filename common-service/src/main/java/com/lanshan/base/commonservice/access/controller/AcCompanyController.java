package com.lanshan.base.commonservice.access.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.qo.AcCompanyPageQO;
import com.lanshan.base.commonservice.access.qo.AcCompanyQO;
import com.lanshan.base.commonservice.access.qo.BatchChangeStatusQO;
import com.lanshan.base.commonservice.access.service.AcCompanyService;
import com.lanshan.base.commonservice.access.vo.AcCompanyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.simpleframework.xml.core.Validate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 接入方(AcCompany)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("acCompany")
@Api(tags = "接入方控制层", hidden = true)
public class AcCompanyController {
    /**
     * 服务对象
     */
    @Resource
    private AcCompanyService acCompanyService;
    @RequiresPermissions("api:acCompany:list")
    @GetMapping("pageCompany")
    @ApiOperation("分页查询接入方")
    public Result<IPage<AcCompanyVO>> pageCompany(AcCompanyPageQO pageQO) {
        return Result.build(acCompanyService.pageCompany(pageQO));
    }

    /**
     * 新增数据
     *
     * @param qo 实体对象
     * @return 新增结果
     */
    @RequiresPermissions("api:acCompany:add")
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> save(@RequestBody AcCompanyQO qo) {
        acCompanyService.save(qo);
        return Result.build();
    }

    /**
     * 修改数据
     *
     * @param qo 实体对象
     * @return 修改结果
     */
    @RequiresPermissions("api:acCompany:edit")
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody AcCompanyQO qo) {
        acCompanyService.update(qo);
        return Result.build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @RequiresPermissions("api:acCompany:remove")
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Object> delete(@RequestBody List<Long> idList) {
        this.acCompanyService.batchRemove(idList);
        return Result.build();
    }
    @RequiresPermissions("api:acCompany:edit")
    @PostMapping("batchChangeStatus")
    @ApiOperation("批量改变状态")
    public Result<Object> batchChangeStatus(@RequestBody @Validate BatchChangeStatusQO qo) {
        acCompanyService.batchChangeStatus(qo);
        return Result.build();
    }

}

