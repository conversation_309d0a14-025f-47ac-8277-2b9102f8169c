package com.lanshan.base.commonservice.todo.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 白名单配置表(TodoWhitelistConfig)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class TodoWhitelistConfig extends Model<TodoWhitelistConfig> {
    private Long id;
    /**
     *应用id
     */
    private Long appId;
    /**
     *测试状态：1：启用  0 未启用
     */
    private Integer testStatus;
    /**
     *默认状态 ：1 正常  0 已删除
     */
    private Integer status;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新时间
     */
    private Date lastUpdateTime;
    /**
     *应用id
     */
    private Integer agentId;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

