package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 退费操作日志视图对象
 */
@Data
@ApiModel(value = "RefundOpLogVO", description = "退费操作日志视图对象")
public class RefundOpLogVO implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "操作时间")
    private Date opTime;

    @ApiModelProperty(value = "操作人")
    private String opUser;

    @ApiModelProperty(value = "附件名称")
    private String fileName;

    @ApiModelProperty(value = "附件URL")
    private String fileUrl;

    @ApiModelProperty(value = "操作类型（添加银行卡、更换银行卡、捐赠支出、导入数据表等）")
    private String opType;

    @ApiModelProperty(value = "操作内容（添加中国建设银行卡 (尾号5678)、确认捐赠全部余额）")
    private String opContent;

    @ApiModelProperty(value = "导入成功记录数")
    private Integer successCount;

    @ApiModelProperty(value = "导入失败记录数")
    private Integer failCount;

    @ApiModelProperty(value = "失败详情记录（JSON格式）")
    private String failDetails;

    @ApiModelProperty(value = "状态")
    private String status;
}