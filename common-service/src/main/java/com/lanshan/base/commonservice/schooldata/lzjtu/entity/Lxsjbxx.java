package com.lanshan.base.commonservice.schooldata.lzjtu.entity;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;


/**
 * 留学生信息(Lxsjbxx)表实体类
 */
@Data
public class Lxsjbxx implements Serializable{

    @ApiModelProperty(value = "学号")
    @JsonProperty("XH")
    private String xh;

    @ApiModelProperty(value = "姓名")
    @JsonProperty("XM")
    private String xm;

    @ApiModelProperty(value = "英文姓名")
    @JsonProperty("YWXM")
    private String ywxm;

    @ApiModelProperty(value = "性别")
    @JsonProperty("XBMC")
    private String xbmc;

    @ApiModelProperty(value = "留学生类别")
    @JsonProperty("LXSLBMC")
    private String lxslbmc;

    @ApiModelProperty(value = "学生当前状态")
    @JsonProperty("XSDQZTMC")
    private String xsdqztmc;

    @ApiModelProperty(value = "院系（单位）代码")
    @JsonProperty("DWH")
    private String dwh;

    @ApiModelProperty(value = "院系（单位）名称")
    @JsonProperty("DWMC")
    private String dwmc;

    @ApiModelProperty(value = "专业代码")
    @JsonProperty("ZYDM")
    private String zydm;

    @ApiModelProperty(value = "专业名称")
    @JsonProperty("ZYMC")
    private String zymc;

    @ApiModelProperty(value = "班级代码")
    @JsonProperty("BJDM")
    private String bjdm;

    @ApiModelProperty(value = "班级名称")
    @JsonProperty("BJMC")
    private String bjmc;

    @ApiModelProperty(value = "学生类别")
    @JsonProperty("XSLBMC")
    private String xslbmc;

    @ApiModelProperty(value = "学籍状态")
    @JsonProperty("XJZTMC")
    private String xjztmc;

    @ApiModelProperty(value = "数据来源")
    @JsonProperty("SJLY")
    private String sjly;

    @ApiModelProperty(value = "现在年级")
    @JsonProperty("XZNJ")
    private String xznj;

    @ApiModelProperty(value = "移动电话")
    @JsonProperty("DH")
    private String yddh;
}

