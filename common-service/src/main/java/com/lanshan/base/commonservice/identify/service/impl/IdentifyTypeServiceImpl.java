package com.lanshan.base.commonservice.identify.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.identify.entity.IdentifyTypeEntity;
import com.lanshan.base.commonservice.identify.mapper.IdentifyTypeMapper;
import com.lanshan.base.commonservice.identify.service.IdentifyTypeService;
import org.springframework.stereotype.Service;

/**
 * (IdentifyType)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-10 16:37:58
 */
@Service("identifyTypeService")
public class IdentifyTypeServiceImpl extends ServiceImpl<IdentifyTypeMapper, IdentifyTypeEntity> implements IdentifyTypeService {

}

