package com.lanshan.base.commonservice.todo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 待办抄送用户表(TodoFlowCopyUser)实体
 */
@Data
public class TodoFlowCopyUser implements Serializable {
    private static final long serialVersionUID = -56750516633025539L;

    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("流程id")
    private Long flowId;

    @ApiModelProperty("节点id")
    private Long nodeId;

    @ApiModelProperty("用户id")
    private String userid;

    @ApiModelProperty("创建时间")
    private Date createDate;
}

