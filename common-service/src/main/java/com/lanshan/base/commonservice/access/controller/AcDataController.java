package com.lanshan.base.commonservice.access.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.converter.AcDataConverter;
import com.lanshan.base.commonservice.access.converter.AcDataFieldConverter;
import com.lanshan.base.commonservice.access.qo.AcDataPageQO;
import com.lanshan.base.commonservice.access.service.AcDataFieldService;
import com.lanshan.base.commonservice.access.service.AcDataService;
import com.lanshan.base.commonservice.access.vo.AcDataFieldVO;
import com.lanshan.base.commonservice.access.vo.AcDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权限数据集(AcData)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("acData")
@Api(tags = "数据源控制层", hidden = true)
public class AcDataController {
    /**
     * 服务对象
     */
    @Resource
    private AcDataService acDataService;

    @Resource
    private AcDataFieldService acDataFieldService;
    @RequiresPermissions("api:acData:list")
    @GetMapping("pageAcData")
    @ApiOperation("分页查询权限数据集")
    public Result<IPage<AcDataVO>> pageAcData(AcDataPageQO pageQO) {
        return Result.build(acDataService.pageAcData(pageQO));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody AcDataVO vo) {
        return Result.build(this.acDataService.updateById(AcDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.acDataService.removeByIds(idList));
    }
    @RequiresPermissions("api:acData:list")
    @ApiOperation("通过唯一标识查询数据源")
    @GetMapping("getByDataKey")
    public Result<AcDataVO> getByDataKey(@RequestParam("dataKey") String dataKey) {
        return Result.build(acDataService.getByDataKey(dataKey));
    }
    @RequiresPermissions("api:acData:list")
    @ApiOperation("通过唯一标识查询数据源字段")
    @GetMapping("getDataFiledByDataKey")
    public Result<List<AcDataFieldVO>> getDataFiledByDataKey(@RequestParam("dataKey") String dataKey) {
        return Result.build(AcDataFieldConverter.INSTANCE.toVO(acDataFieldService.listByDataKey(dataKey)));
    }
}

