package com.lanshan.base.commonservice.system.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 角色和部门关联表(SysRoleDept)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:26:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SysRoleDept extends Model<SysRoleDept> {
    private static final long serialVersionUID = 6987460589552220686L;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 部门ID
     */
    private Long deptId;
}

