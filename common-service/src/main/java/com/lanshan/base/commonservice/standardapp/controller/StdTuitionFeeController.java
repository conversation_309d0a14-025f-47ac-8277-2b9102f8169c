package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.converter.StdTuitionFeeConverter;
import com.lanshan.base.commonservice.standardapp.entity.StdTuitionFee;
import com.lanshan.base.commonservice.standardapp.service.StdTuitionFeeService;
import com.lanshan.base.commonservice.standardapp.vo.StdTuitionFeeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * 学费查询表(StdTuitionFee)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("stdTuitionFee")
@Api(tags = "学费查询表(StdTuitionFee)控制层", hidden = true)
public class StdTuitionFeeController {
    /**
     * 服务对象
     */
    @Resource
    private StdTuitionFeeService stdTuitionFeeService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<StdTuitionFeeVO>> selectAll(Page<StdTuitionFeeVO> page, StdTuitionFeeVO vo) {
        QueryWrapper<StdTuitionFee> queryWrapper = new QueryWrapper<>(StdTuitionFeeConverter.INSTANCE.toEntity(vo));
        IPage<StdTuitionFee> pageData = this.stdTuitionFeeService.page(page.convert(StdTuitionFeeConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(StdTuitionFeeConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<StdTuitionFeeVO> selectOne(@PathVariable Serializable id) {
        return Result.build(StdTuitionFeeConverter.INSTANCE.toVO(this.stdTuitionFeeService.getById(id)));
    }

    /**
     * 通过工号查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation("通过工号查询单条数据")
    @GetMapping("/getStdTuitionFee")
    public Result<StdTuitionFeeVO> getStdTuitionFee() {
        return Result.build(this.stdTuitionFeeService.getStdTuitionFee());
    }
}

