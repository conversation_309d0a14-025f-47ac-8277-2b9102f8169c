package com.lanshan.base.commonservice.group.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.UserStatusEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.qo.user.BatchInviteQo;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.enums.GroupAppSettingEnum;
import com.lanshan.base.commonservice.enums.GroupStudentTypeEnum;
import com.lanshan.base.commonservice.enums.InterStuClassMappingEnum;
import com.lanshan.base.commonservice.enums.WhutGroupType;
import com.lanshan.base.commonservice.group.constant.GroupConstant;
import com.lanshan.base.commonservice.group.dao.MsgClassGroupChatDao;
import com.lanshan.base.commonservice.group.dao.MsgCourseGroupChatDao;
import com.lanshan.base.commonservice.group.dao.MsgDeptGroupChatDao;
import com.lanshan.base.commonservice.group.dto.MsgClassChatUpdateUserDTO;
import com.lanshan.base.commonservice.group.dto.MsgClassGroupChatCountDTO;
import com.lanshan.base.commonservice.group.dto.MsgCourseChatUpdateUserDTO;
import com.lanshan.base.commonservice.group.entity.*;
import com.lanshan.base.commonservice.group.enums.GroupExportHistoryStatusEnum;
import com.lanshan.base.commonservice.group.handler.GroupChatHandler;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.group.service.MsgClassGroupChatService;
import com.lanshan.base.commonservice.group.service.MsgCourseGroupChatService;
import com.lanshan.base.commonservice.group.service.MsgGroupExportHistoryService;
import com.lanshan.base.commonservice.group.service.impl.MsgDeptGroupChatServiceImpl;
import com.lanshan.base.commonservice.group.vo.*;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutInterEduCollegeStudentDao;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutMasterCounselorClassDao;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutTeacherDao;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateStatusDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.*;
import com.lanshan.base.commonservice.schooldata.whut.service.*;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpChatService;
import me.chanjar.weixin.cp.bean.WxCpChat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 武汉理工群聊处理器
 */
@Slf4j
@Component
public class WhutGroupChatHandler extends AbstractGroupChatHandler implements GroupChatHandler {

    private static final String GET_CHAT_ERROR = "获取群聊信息失败";

    @Resource
    private MsgClassGroupChatDao msgClassGroupChatDao;

    @Resource
    private WhutUndergraduateStatusDao whutUndergraduateStatusDao;

    @Resource
    private WhutMasterCounselorClassDao whutMasterCounselorClassDao;

    @Resource
    private WhutUndergraduateStatusService whutUndergraduateStatusService;

    @Resource
    private WhutMasterCounselorClassService whutMasterCounselorClassService;

    @Resource
    private WhutUndergraduateCounselorClassService whutUndergraduateCounselorClassService;

    @Resource
    private MsgCourseGroupChatDao msgCourseGroupChatDao;

    @Resource
    private WhutUndergraduateCourseStudentService whutUndergraduateCourseStudentService;

    @Resource
    private WhutMasterStatusService whutMasterStatusService;

    @Resource
    private WhutUndergraduateCourseTeacherService whutUndergraduateCourseTeacherService;

    @Resource
    private WhutMasterCourseTeacherService whutMasterCourseTeacherService;

    @Resource
    private WhutMasterCourseStudentService whutMasterCourseStudentService;

    @Resource
    private GroupChatService groupChatService;

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private UserService userService;

    @Resource
    private MsgClassGroupChatService msgClassGroupChatService;

    @Resource
    private MsgCourseGroupChatService msgCourseGroupChatService;

    @Resource
    private MsgDeptGroupChatDao msgDeptGroupChatDao;

    @Resource
    private WhutTeacherDao whutTeacherDao;

    @Resource
    private MsgDeptGroupChatServiceImpl msgDeptGroupChatService;

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @Resource
    private MsgGroupExportHistoryService msgGroupExportHistoryService;

    @Resource
    private WhutInterEduCollegeStudentDao whutInterEduCollegeStudentDao;

    @Resource
    private WhutInterEduCollegeStudentService whutInterEduCollegeStudentService;

    @Resource
    private RedisService redisService;


    @Override
    public void pageClassGroupChat(IPage<MsgClassGroupChatVO> page, MsgClassGroupChatPageQo pageQo) {

        //查询班级群聊列表
        List<MsgClassGroupChatVO> list = msgClassGroupChatDao.pageClassGroupChatWhut(page, pageQo);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        //填充班级教师信息
        fillClassTeacherInfo(list);
        //填充班级数量信息
        fillTheStudentPopulation(list);

        page.setRecords(list);
    }

    /**
     * 填充学生人数
     *
     * @param list
     */
    private void fillTheStudentPopulation(List<MsgClassGroupChatVO> list) {
        //按学生类型分组
        Map<Integer, List<MsgClassGroupChatVO>> studentTypeMap = list.stream().collect(Collectors.groupingBy(MsgClassGroupChatVO::getStudentType));
        //本科生列表
        List<MsgClassGroupChatVO> undergraduateList = studentTypeMap.get(GroupStudentTypeEnum.UNDERGRADUATE.getCode());
        if (CollUtil.isNotEmpty(undergraduateList)) {
            //查询本科生班级对应人数
            List<String> classCodeList = undergraduateList.stream().map(MsgClassGroupChatVO::getClassCode).collect(Collectors.toList());
            List<MsgClassGroupChatCountDTO> undergraduateCountList = whutUndergraduateStatusDao.listCountByClassCode(classCodeList);
            //转为map
            Map<String, Integer> undergraduateCountMap = CollUtil.toMap(undergraduateCountList, null, MsgClassGroupChatCountDTO::getClassCode, MsgClassGroupChatCountDTO::getClassUserCount);

            //设置本科生人数
            for (MsgClassGroupChatVO undergraduate : undergraduateList) {
                String classCode = undergraduate.getClassCode();
                Integer userCount = 0;
                if (undergraduateCountMap.get(classCode) != null) {
                    userCount = undergraduateCountMap.get(classCode);
                }
                undergraduate.setClassUserCount(userCount);
            }
        }

        //研究生列表
        List<MsgClassGroupChatVO> masterList = studentTypeMap.get(GroupStudentTypeEnum.MASTER.getCode());
        if (CollUtil.isNotEmpty(masterList)) {
            //查询研究生专业对应人数
            List<String> majorCodeList = masterList.stream().map(MsgClassGroupChatVO::getMajorCode).collect(Collectors.toList());
            List<MsgClassGroupChatCountDTO> masterCountList = whutMasterCounselorClassDao.listCountByMajorCode(majorCodeList);
            //转为map
            Map<String, Integer> masterCountMap = CollUtil.toMap(masterCountList, null, item ->
                    item.getGrade() + "-" + item.getInstituteCode() + "-" + item.getMajorCode(), MsgClassGroupChatCountDTO::getClassUserCount);
            //设置研究生人数
            for (MsgClassGroupChatVO master : masterList) {
                String grade = master.getGrade();
                String instituteCode = master.getInstituteCode();
                String majorCode = master.getMajorCode();
                Integer userCount = 0;
                if (masterCountMap.get(grade + "-" + instituteCode + "-" + majorCode) != null) {
                    userCount = masterCountMap.get(grade + "-" + instituteCode + "-" + majorCode);
                }
                master.setClassUserCount(userCount);
            }
        }

        List<MsgClassGroupChatVO> interEduStuList = studentTypeMap.get(GroupStudentTypeEnum.INTERNATIONAL.getCode());
        if (CollUtil.isNotEmpty(interEduStuList)) {
            //查询国际生班级对应人数
            List<String> classCodeList = interEduStuList.stream().map(MsgClassGroupChatVO::getClassCode).collect(Collectors.toList());
            List<MsgClassGroupChatCountDTO> internationalCountList = whutInterEduCollegeStudentDao.listCountByBjmc(classCodeList);
            //转为map
            Map<String, Integer> interEduMap = CollUtil.toMap(internationalCountList, null, MsgClassGroupChatCountDTO::getClassCode, MsgClassGroupChatCountDTO::getClassUserCount);
            for (MsgClassGroupChatVO interEduStu : interEduStuList) {
                Integer userCount = interEduMap.get(interEduStu.getClassCode());
                if (Objects.isNull(userCount)) {
                    userCount = 0;
                }
                //替换名称
                String className = Arrays.stream(InterStuClassMappingEnum.values())
                        .filter(mapping -> StringUtils.startsWithIgnoreCase(interEduStu.getClassCode(), mapping.name()))
                        .map(mapping -> StringUtils.replace(interEduStu.getClassCode(), mapping.name(), mapping.getMapping()))
                        .collect(Collectors.joining());
                if (StringUtils.isNotBlank(className)) {
                    interEduStu.setClassName(className);
                } else {
                    interEduStu.setClassName(interEduStu.getClassCode());
                }
                interEduStu.setClassUserCount(userCount);
            }
        }
    }

    @Override
    public List<MsgClassGroupChatDetailVO> getMsgClassGroupChatDetailVO(MsgClassGroupChatDetailQo qo) {
        //入群用户列表默认为空
        List<String> joinUserList = Collections.emptyList();
        //查询班级群聊
        if (qo.getClassGroupChatId() != null) {
            MsgClassGroupChat groupChat = msgClassGroupChatService.getById(qo.getClassGroupChatId());
            //入群用户列表
            joinUserList = groupChat.getJoinUserList();
        }

        List<MsgClassGroupChatDetailVO> voList = new ArrayList<>();
        //查询本科生
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == qo.getStudentType()) {
            getUndergClassGroupChatDetailVOS(qo, voList, joinUserList);
        } else if (GroupStudentTypeEnum.MASTER.getCode() == qo.getStudentType()) {
            getMasterClassGroupChatDetailVOS(qo, voList, joinUserList);
        } else if (GroupStudentTypeEnum.INTERNATIONAL.getCode() == qo.getStudentType()) {
            getInterEduClassGroupChatDetailVOS(qo, voList, joinUserList);
        }
        //填充头像和是否在企微中
        fillInAvatarAndIsInCp(voList);
        return voList;
    }

    private void fillInAvatarAndIsInCp(List<MsgClassGroupChatDetailVO> voList) {
        for (MsgClassGroupChatDetailVO vo : voList) {
            CpUser user = userService.getCacheUserByUserid(vo.getUserid());
            if (Objects.nonNull(user)) {
                vo.setAvatar(user.getAvatar());
            }
            //判断是否在企微中
            if (user != null && (user.getStatus() == UserStatusEnum.ACTIVATED.getCode() || user.getStatus() == UserStatusEnum.FORBIDDEN.getCode())) {
                vo.setIsInCp(YnEnum.YES.getValue());
            } else {
                vo.setIsInCp(YnEnum.NO.getValue());
            }
        }
    }

    private void getInterEduClassGroupChatDetailVOS(MsgClassGroupChatDetailQo qo, List<MsgClassGroupChatDetailVO> voList, List<String> joinUserList) {
        List<WhutInterEduCollegeStudent> list = whutInterEduCollegeStudentService.listByCode(qo.getClassCode());
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (WhutInterEduCollegeStudent whutInterEduCollegeStudent : list) {
            MsgClassGroupChatDetailVO vo = new MsgClassGroupChatDetailVO();
            vo.setUserid(whutInterEduCollegeStudent.getXh());
            vo.setName(whutInterEduCollegeStudent.getXm());
            //如果用户已经加入群聊，则设置为已入群
            if (joinUserList.contains(whutInterEduCollegeStudent.getXh())) {
                vo.setIsJoin(YnEnum.YES.getValue());
            } else {
                vo.setIsJoin(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }
    }


    private void getMasterClassGroupChatDetailVOS(MsgClassGroupChatDetailQo qo, List<MsgClassGroupChatDetailVO> voList, List<String> joinUserList) {
        //查询研究生
        LambdaQueryWrapper<WhutMasterCounselorClass> mcQw = Wrappers.lambdaQuery(WhutMasterCounselorClass.class);
        mcQw.eq(WhutMasterCounselorClass::getNj, qo.getGrade());
        mcQw.eq(WhutMasterCounselorClass::getXybm, qo.getInstituteCode());
        mcQw.eq(WhutMasterCounselorClass::getZybm, qo.getMajorCode());
        mcQw.eq(WhutMasterCounselorClass::getXsdqztm, "01");
        mcQw.eq(WhutMasterCounselorClass::getXjztm, "1");
        List<WhutMasterCounselorClass> list = whutMasterCounselorClassService.list(mcQw);
        //未找到数据直接返回
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (WhutMasterCounselorClass count : list) {
            MsgClassGroupChatDetailVO vo = new MsgClassGroupChatDetailVO();
            vo.setUserid(count.getXh());
            vo.setName(count.getXm());
            //如果用户已经加入群聊，则设置为已入群
            if (joinUserList.contains(count.getXh())) {
                vo.setIsJoin(YnEnum.YES.getValue());
            } else {
                vo.setIsJoin(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }
    }

    private void getUndergClassGroupChatDetailVOS(MsgClassGroupChatDetailQo qo, List<MsgClassGroupChatDetailVO> voList, List<String> joinUserList) {
        LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
        usQw.eq(WhutUndergraduateStatus::getBjbm, qo.getClassCode());
        usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
        usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
        List<WhutUndergraduateStatus> list = whutUndergraduateStatusService.list(usQw);

        //未找到数据直接返回
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (WhutUndergraduateStatus undergraduateStatus : list) {
            MsgClassGroupChatDetailVO vo = new MsgClassGroupChatDetailVO();
            vo.setUserid(undergraduateStatus.getXh());
            vo.setName(undergraduateStatus.getXm());
            //如果用户已经加入群聊，则设置为已入群
            if (joinUserList.contains(undergraduateStatus.getXh())) {
                vo.setIsJoin(YnEnum.YES.getValue());
            } else {
                vo.setIsJoin(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void creatClassGroupChat(MsgClassGroupChatSaveQo saveQo, List<GroupChatScope> userList) throws WxErrorException {

        //查询企微用户
        List<String> useridList = userList.stream().map(GroupChatScope::getDataId).collect(Collectors.toList());
        List<CpUser> cpUserList = userService.listByIds(useridList);
        //已经加入企微的用户
        List<String> joinUseridList = cpUserList.stream().map(CpUser::getUserid).collect(Collectors.toList());

        //获取当前用户设置为管理员
        String userId = SecurityContextHolder.getUserId();
        CpUser user = userService.getCacheUserByUserid(userId);
        String userName = user.getName();

        ChatInfo chatInfo = new ChatInfo();
        chatInfo.setAgentId(agentProperties.getClassGroupAgentId());
        chatInfo.setDepartmentList(Collections.emptyList());
        chatInfo.setTagList(Collections.emptyList());
        chatInfo.setUserList(userList);
        //设置群聊信息
        GroupChat groupChat = new GroupChat();
        groupChat.setOwner(userId);
        groupChat.setOwnerName(userName);
        groupChat.setName(saveQo.getChatName());
        groupChat.setOwnerType(0);
        groupChat.setType(GroupConstant.GROUP_CHAT_CLASS);
        //建群消息 如：欢迎加入软件工程2401【班级群】！
        groupChat.setMessage("欢迎加入" + saveQo.getChatName() + "！");
        chatInfo.setChat(groupChat);
        //创建企微群聊
        groupChatService.createGroup(chatInfo);

        //新增班级群聊
        MsgClassGroupChat msgClassGroupChat = new MsgClassGroupChat();
        msgClassGroupChat.setChatId(groupChat.getChatid());
        msgClassGroupChat.setChatName(saveQo.getChatName());
        msgClassGroupChat.setGrade(saveQo.getGrade());
        msgClassGroupChat.setInstituteCode(saveQo.getInstituteCode());
        msgClassGroupChat.setMajorCode(saveQo.getMajorCode());
        msgClassGroupChat.setClassCode(saveQo.getClassCode());
        msgClassGroupChat.setStudentType(saveQo.getStudentType());
        msgClassGroupChat.setManagerUserid(userId);
        msgClassGroupChat.setManagerName(userName);
        //加入用户去除辅导员
        joinUseridList.removeIf(userid -> saveQo.getCounselorList().contains(userid));
        msgClassGroupChat.setJoinUserList(joinUseridList);
        msgClassGroupChat.setJoinCount(joinUseridList.size());
        msgClassGroupChatService.save(msgClassGroupChat);
    }


    @Override
    public List<GroupChatScope> listClassGroupChatUser(MsgClassGroupChatSaveQo saveQo) {
        //查询班级群聊列表
        List<GroupChatScope> userList = new ArrayList<>();
        //查询本科生
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == saveQo.getStudentType()) {
            //处理本科生用户
            processClassUndergraduateUser(saveQo, userList);

        } else if (GroupStudentTypeEnum.MASTER.getCode() == saveQo.getStudentType()) {
            //处理研究生
            processClassMasterUser(saveQo, userList);

        } else if (GroupStudentTypeEnum.INTERNATIONAL.getCode() == saveQo.getStudentType()) {
            //处理国教生
            processClassInterUser(saveQo, userList);
        } else {
            throw ExceptionCodeEnum.GROUP_CHAT_STUDENT_TYPE_NOT_EXIST.toServiceException();
        }

        if (CollUtil.isEmpty(userList)) {
            throw ExceptionCodeEnum.GROUP_CHAT_CLASS_STUDENT_IS_EMPTY.toServiceException();
        }
        return userList;
    }

    private void processClassInterUser(MsgClassGroupChatSaveQo saveQo, List<GroupChatScope> userList) {
        List<WhutInterEduCollegeStudent> list = whutInterEduCollegeStudentService.listByCode(saveQo.getClassCode());
        //构建群聊入参
        for (WhutInterEduCollegeStudent interEduCollegeStudent : list) {
            GroupChatScope groupChatScope = new GroupChatScope();
            groupChatScope.setType(2);
            groupChatScope.setDataId(interEduCollegeStudent.getXh());
            groupChatScope.setDataName(interEduCollegeStudent.getXm());
            userList.add(groupChatScope);
        }
        //查询辅导员
        String fdyGhStrByCode = msgClassGroupChatDao.getInterStuFdyGhStrByCode(saveQo.getClassCode());
        String[] bzrUserIds = fdyGhStrByCode.split(",");
        for (String userId : bzrUserIds) {
            GroupChatScope groupChatScope = new GroupChatScope();
            groupChatScope.setType(2);
            groupChatScope.setDataId(userId);
            CpUser user = userService.getCacheUserByUserid(userId);
            groupChatScope.setDataName(user.getName());
            userList.add(groupChatScope);
        }
        //设置辅导员数量
        saveQo.setCounselorList(List.of(bzrUserIds));
    }

    @Override
    public void pageCourseGroupChat(IPage<MsgCourseGroupChatVO> page, MsgCourseGroupChatPageQo pageQo) {
        //查询课程群聊列表
        List<MsgCourseGroupChatVO> list = msgCourseGroupChatDao.pageCourseGroupChat(page, pageQo);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        fillCourseStuPopulation(list);

        page.setRecords(list);
    }

    /**
     * 填充课程群聊学生人数
     *
     * @param list
     */
    private void fillCourseStuPopulation(List<MsgCourseGroupChatVO> list) {
        //按学生类型分组
        Map<Integer, List<MsgCourseGroupChatVO>> studentTypeMap = list.stream().collect(Collectors.groupingBy(MsgCourseGroupChatVO::getStudentType));

        //本科生课程人数设置
        processUndergraduateCount(studentTypeMap);

        //研究生课程人数设置
        processMasterCount(studentTypeMap);
    }

    /**
     * 研究生课程人数设置
     */
    private void processMasterCount(Map<Integer, List<MsgCourseGroupChatVO>> studentTypeMap) {
        List<MsgCourseGroupChatVO> masterList = studentTypeMap.get(GroupStudentTypeEnum.MASTER.getCode());
        if (CollUtil.isNotEmpty(masterList)) {
            //查询研究生课表编码对应人数
            List<String> codeList = masterList.stream().map(MsgCourseGroupChatVO::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<WhutMasterCourseStudent> mcsQw = Wrappers.lambdaQuery(WhutMasterCourseStudent.class);
            mcsQw.in(WhutMasterCourseStudent::getKbbh, codeList);
            List<WhutMasterCourseStudent> studentList = whutMasterCourseStudentService.list(mcsQw);
            //未找到数据直接返回
            if (CollUtil.isEmpty(studentList)) {
                return;
            }

            //查询研究生状态 获取有效的研究生
            LambdaQueryWrapper<WhutMasterStatus> msQw = Wrappers.lambdaQuery(WhutMasterStatus.class);
            msQw.in(WhutMasterStatus::getXh, studentList.stream().map(WhutMasterCourseStudent::getXh).collect(Collectors.toList()));
            msQw.eq(WhutMasterStatus::getXsdqztm, "01");
            msQw.eq(WhutMasterStatus::getXjztm, "1");
            List<WhutMasterStatus> statusList = whutMasterStatusService.list(msQw);

            //过滤出有效的研究生
            studentList.removeIf(item -> statusList.stream().noneMatch(status -> status.getXh().equals(item.getXh())));
            if (CollUtil.isNotEmpty(studentList)) {
                //按课表编码分组对应人数
                Map<String, Long> studentCountMap = studentList.stream().collect(Collectors.groupingBy(WhutMasterCourseStudent::getKbbh, Collectors.counting()));
                //设置研究生人数
                for (MsgCourseGroupChatVO master : masterList) {
                    String code = master.getCode();
                    Long userCount = 0L;
                    if (studentCountMap.get(code) != null) {
                        userCount = studentCountMap.get(code);
                    }
                    master.setCourseUserCount(Math.toIntExact(userCount));
                }
            }
        }
    }

    /**
     * 本科生课程人数设置
     */
    private void processUndergraduateCount(Map<Integer, List<MsgCourseGroupChatVO>> studentTypeMap) {
        //本科生列表
        List<MsgCourseGroupChatVO> undergraduateList = studentTypeMap.get(GroupStudentTypeEnum.UNDERGRADUATE.getCode());
        if (CollUtil.isNotEmpty(undergraduateList)) {
            //查询本科生课表编码对应人数
            List<String> codeList = undergraduateList.stream().map(MsgCourseGroupChatVO::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<WhutUndergraduateCourseStudent> ucsQw = Wrappers.lambdaQuery(WhutUndergraduateCourseStudent.class);
            ucsQw.in(WhutUndergraduateCourseStudent::getXkkh, codeList);
            List<WhutUndergraduateCourseStudent> studentList = whutUndergraduateCourseStudentService.list(ucsQw);
            //未找到数据直接返回
            if (CollUtil.isEmpty(studentList)) {
                return;
            }
            //查询本科生状态 获取有效的本科生
            LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
            usQw.in(WhutUndergraduateStatus::getXh, studentList.stream().map(WhutUndergraduateCourseStudent::getXh).collect(Collectors.toList()));
            usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
            usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
            List<WhutUndergraduateStatus> statusList = whutUndergraduateStatusService.list(usQw);

            //过滤出有效的本科生
            studentList.removeIf(item -> statusList.stream().noneMatch(status -> status.getXh().equals(item.getXh())));
            if (CollUtil.isNotEmpty(studentList)) {
                //按课表编码分组对应人数
                Map<String, Long> studentCountMap = studentList.stream().collect(Collectors.groupingBy(WhutUndergraduateCourseStudent::getXkkh, Collectors.counting()));
                //设置本科生人数
                for (MsgCourseGroupChatVO undergraduate : undergraduateList) {
                    String code = undergraduate.getCode();
                    Long userCount = 0L;
                    if (studentCountMap.get(code) != null) {
                        userCount = studentCountMap.get(code);
                    }
                    undergraduate.setCourseUserCount(Math.toIntExact(userCount));
                }
            }
        }
    }

    @Override
    public List<MsgCourseGroupChatDetailVO> getMsgCourseGroupChatDetailVO(MsgCourseGroupChatDetailQo qo) {
        //入群用户列表默认为空
        List<String> joinUserList = Collections.emptyList();
        //查询课程群聊
        if (qo.getCourseGroupChatId() != null) {
            MsgCourseGroupChat groupChat = msgCourseGroupChatService.getById(qo.getCourseGroupChatId());
            //入群用户列表
            joinUserList = groupChat.getJoinUserList();
        }
        List<MsgCourseGroupChatDetailVO> voList = new ArrayList<>();
        //查询本科生
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == qo.getStudentType()) {
            LambdaQueryWrapper<WhutUndergraduateCourseStudent> ucsQw = Wrappers.lambdaQuery(WhutUndergraduateCourseStudent.class);
            ucsQw.eq(WhutUndergraduateCourseStudent::getXkkh, qo.getCode());
            List<WhutUndergraduateCourseStudent> studentList = whutUndergraduateCourseStudentService.list(ucsQw);
            List<String> useridList = studentList.stream().map(WhutUndergraduateCourseStudent::getXh).collect(Collectors.toList());

            //未找到数据直接返回
            if (CollUtil.isEmpty(studentList)) {
                return voList;
            }
            //查询本科生状态 获取有效的本科生
            LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
            usQw.in(WhutUndergraduateStatus::getXh, useridList);
            usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
            usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
            List<WhutUndergraduateStatus> list = whutUndergraduateStatusService.list(usQw);
            if (CollUtil.isEmpty(list)) {
                return voList;
            }
            //处理课程群聊本科生明细
            processCourseChatDetailUndergraduate(qo, joinUserList, list, voList);

        } else if (GroupStudentTypeEnum.MASTER.getCode() == qo.getStudentType()) {
            //查询研究生
            LambdaQueryWrapper<WhutMasterCourseStudent> mcsQw = Wrappers.lambdaQuery(WhutMasterCourseStudent.class);
            mcsQw.eq(WhutMasterCourseStudent::getKbbh, qo.getCode());
            List<WhutMasterCourseStudent> studentList = whutMasterCourseStudentService.list(mcsQw);
            //未找到数据直接返回
            if (CollUtil.isEmpty(studentList)) {
                return voList;
            }
            //转为map
            Map<String, String> nameMap = CollUtil.toMap(studentList, MapUtil.newHashMap(), WhutMasterCourseStudent::getXh, WhutMasterCourseStudent::getXm);
            List<String> useridList = studentList.stream().map(WhutMasterCourseStudent::getXh).collect(Collectors.toList());

            //查询研究生状态 获取有效的研究生
            LambdaQueryWrapper<WhutMasterStatus> msQw = Wrappers.lambdaQuery(WhutMasterStatus.class);
            msQw.in(WhutMasterStatus::getXh, useridList);
            msQw.eq(WhutMasterStatus::getXsdqztm, "01");
            msQw.eq(WhutMasterStatus::getXjztm, "1");
            List<WhutMasterStatus> list = whutMasterStatusService.list(msQw);
            List<String> validUseridList = list.stream().map(WhutMasterStatus::getXh).collect(Collectors.toList());

            if (CollUtil.isEmpty(validUseridList)) {
                return voList;
            }

            //处理课程群研究生明细
            processCourseChatDetailMaster(qo, joinUserList, validUseridList, nameMap, voList);
        }

        for (MsgCourseGroupChatDetailVO vo : voList) {
            //设置是否在企微中
            vo.setIsInCp(YnEnum.NO.getValue());
            CpUser user = userService.getCacheUserByUserid(vo.getUserid());
            if (Objects.nonNull(user)) {
                vo.setAvatar(user.getAvatar());
            }
            //判断是否在企微中
            if (user != null && (user.getStatus() == UserStatusEnum.ACTIVATED.getCode() || user.getStatus() == UserStatusEnum.FORBIDDEN.getCode())) {
                vo.setIsInCp(YnEnum.YES.getValue());
            } else {
                vo.setIsInCp(YnEnum.NO.getValue());
            }
        }
        return voList;
    }

    /**
     * 处理课程群研究生明细
     */
    private void processCourseChatDetailMaster(MsgCourseGroupChatDetailQo qo, List<String> joinUserList, List<String> validUseridList, Map<String, String> nameMap, List<MsgCourseGroupChatDetailVO> voList) {
        for (String userid : validUseridList) {
            MsgCourseGroupChatDetailVO vo = new MsgCourseGroupChatDetailVO();
            vo.setUserid(userid);
            vo.setName(nameMap.get(userid));
            //如果用户已经加入群聊，则设置为已入群
            if (joinUserList.contains(userid)) {
                vo.setIsJoin(YnEnum.YES.getValue());
            } else {
                vo.setIsJoin(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }

        //查询教师
        LambdaQueryWrapper<WhutMasterCourseTeacher> mctQw = Wrappers.lambdaQuery(WhutMasterCourseTeacher.class);
        mctQw.eq(WhutMasterCourseTeacher::getKbbh, qo.getCode());
        List<WhutMasterCourseTeacher> teacherList = whutMasterCourseTeacherService.list(mctQw);
        voList.get(0).setTeacherNameList(teacherList.stream().map(WhutMasterCourseTeacher::getRkjsmc).collect(Collectors.toList()));
        voList.get(0).setTeacherGhList(teacherList.stream().map(WhutMasterCourseTeacher::getGh).collect(Collectors.toList()));
    }

    /**
     * 处理课程群聊本科生明细
     */
    private void processCourseChatDetailUndergraduate(MsgCourseGroupChatDetailQo qo, List<String> joinUserList, List<WhutUndergraduateStatus> list, List<MsgCourseGroupChatDetailVO> voList) {
        for (WhutUndergraduateStatus undergraduateStatus : list) {
            MsgCourseGroupChatDetailVO vo = new MsgCourseGroupChatDetailVO();
            vo.setUserid(undergraduateStatus.getXh());
            vo.setName(undergraduateStatus.getXm());
            //如果用户已经加入群聊，则设置为已入群
            if (joinUserList.contains(undergraduateStatus.getXh())) {
                vo.setIsJoin(YnEnum.YES.getValue());
            } else {
                vo.setIsJoin(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }

        //查询教师
        LambdaQueryWrapper<WhutUndergraduateCourseTeacher> uctQw = Wrappers.lambdaQuery(WhutUndergraduateCourseTeacher.class);
        uctQw.eq(WhutUndergraduateCourseTeacher::getXkkh, qo.getCode());
        List<WhutUndergraduateCourseTeacher> teacherList = whutUndergraduateCourseTeacherService.list(uctQw);
        voList.get(0).setTeacherNameList(teacherList.stream().map(WhutUndergraduateCourseTeacher::getSkjsxm).collect(Collectors.toList()));
        voList.get(0).setTeacherGhList(teacherList.stream().map(WhutUndergraduateCourseTeacher::getSkjsgh).collect(Collectors.toList()));
    }

    @Override
    public List<GroupChatScope> listCourseGroupChatUser(MsgCourseGroupChatSaveQo saveQo) {
        //查询课程群聊列表
        List<GroupChatScope> userList = new ArrayList<>();
        //查询本科生
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == saveQo.getStudentType()) {
            //处理本科生用户
            processCourseUndergraduateUser(saveQo, userList);

        } else if (GroupStudentTypeEnum.MASTER.getCode() == saveQo.getStudentType()) {
            //处理研究生
            processCourseMasterUser(saveQo, userList);

        } else {
            throw ExceptionCodeEnum.GROUP_CHAT_STUDENT_TYPE_NOT_EXIST.toServiceException();
        }

        if (CollUtil.isEmpty(userList)) {
            throw ExceptionCodeEnum.GROUP_CHAT_CLASS_STUDENT_IS_EMPTY.toServiceException();
        }
        return userList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void creatCourseGroupChat(MsgCourseGroupChatSaveQo saveQo, List<GroupChatScope> userList) throws WxErrorException {
        //查询企微用户
        List<String> useridList = userList.stream().map(GroupChatScope::getDataId).collect(Collectors.toList());
        List<CpUser> cpUserList = userService.listByIds(useridList);
        //已经加入企微的用户
        List<String> joinUseridList = cpUserList.stream().map(CpUser::getUserid).collect(Collectors.toList());

        //获取当前用户设置为管理员
        String userId = SecurityContextHolder.getUserId();
        String userName = SecurityContextHolder.getUserName();

        ChatInfo chatInfo = new ChatInfo();
        chatInfo.setAgentId(agentProperties.getCourseGroupAgentId());
        chatInfo.setDepartmentList(Collections.emptyList());
        chatInfo.setTagList(Collections.emptyList());
        chatInfo.setUserList(userList);
        //设置群聊信息
        GroupChat groupChat = new GroupChat();
        groupChat.setOwner(userId);
        groupChat.setOwnerName(userName);
        groupChat.setName(saveQo.getChatName());
        groupChat.setOwnerType(0);
        groupChat.setType(GroupConstant.GROUP_CHAT_COURSE);
        //建群消息 如：欢迎加入高等数学|车辆gj1801-03|2023-2024-1【课程群】！
        groupChat.setMessage("欢迎加入" + saveQo.getChatName() + "！");
        chatInfo.setChat(groupChat);
        //创建企微群聊
        groupChatService.createGroup(chatInfo);

        //新增课程群聊
        MsgCourseGroupChat msgCourseGroupChat = new MsgCourseGroupChat();
        msgCourseGroupChat.setChatId(groupChat.getChatid());
        msgCourseGroupChat.setChatName(saveQo.getChatName());
        msgCourseGroupChat.setCode(saveQo.getCode());
        msgCourseGroupChat.setSemester(saveQo.getSemester());
        msgCourseGroupChat.setCourseCode(saveQo.getCourseCode());
        msgCourseGroupChat.setCourseName(saveQo.getCourseName());
        msgCourseGroupChat.setTeachingClass(saveQo.getTeachingClass());
        msgCourseGroupChat.setStudentType(saveQo.getStudentType());
        msgCourseGroupChat.setManagerUserid(userId);
        msgCourseGroupChat.setManagerName(userName);
        //加入用户去除辅导员
        joinUseridList.removeIf(userid -> saveQo.getTeacherList().contains(userid));
        msgCourseGroupChat.setJoinUserList(joinUseridList);
        msgCourseGroupChat.setJoinCount(joinUseridList.size());
        msgCourseGroupChat.setTeacherList(saveQo.getTeacherList());
        msgCourseGroupChatService.save(msgCourseGroupChat);
    }

    /**
     * 处理课程群研究生
     */
    private void processCourseMasterUser(MsgCourseGroupChatSaveQo saveQo, List<GroupChatScope> userList) {
        //查询研究生
        LambdaQueryWrapper<WhutMasterCourseStudent> ucsQw = Wrappers.lambdaQuery(WhutMasterCourseStudent.class);
        ucsQw.eq(WhutMasterCourseStudent::getKbbh, saveQo.getCode());
        List<WhutMasterCourseStudent> studentList = whutMasterCourseStudentService.list(ucsQw);
        List<String> useridList = studentList.stream().map(WhutMasterCourseStudent::getXh).collect(Collectors.toList());

        //查询研究生状态
        LambdaQueryWrapper<WhutMasterStatus> msQw = Wrappers.lambdaQuery(WhutMasterStatus.class);
        msQw.in(WhutMasterStatus::getXh, useridList);
        msQw.eq(WhutMasterStatus::getXsdqztm, "01");
        msQw.eq(WhutMasterStatus::getXjztm, "1");
        List<WhutMasterStatus> list = whutMasterStatusService.list(msQw);
        List<String> validUseridList = list.stream().map(WhutMasterStatus::getXh).collect(Collectors.toList());

        //查询课程教师信息
        LambdaQueryWrapper<WhutMasterCourseTeacher> wuctQw = Wrappers.lambdaQuery(WhutMasterCourseTeacher.class);
        wuctQw.eq(WhutMasterCourseTeacher::getKbbh, saveQo.getCode());
        List<WhutMasterCourseTeacher> wuctList = whutMasterCourseTeacherService.list(wuctQw);

        if (CollUtil.isNotEmpty(validUseridList)) {
            //构建群聊入参
            for (String userid : validUseridList) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(userid);
                userList.add(groupChatScope);
            }
            for (WhutMasterCourseTeacher teacher : wuctList) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(teacher.getGh());
                groupChatScope.setDataName(teacher.getRkjsmc());
                userList.add(groupChatScope);
            }
            //设置教师
            saveQo.setTeacherList(wuctList.stream().map(WhutMasterCourseTeacher::getGh).collect(Collectors.toList()));
        }
    }

    /**
     * 处理课程群本科生
     */
    private void processCourseUndergraduateUser(MsgCourseGroupChatSaveQo saveQo, List<GroupChatScope> userList) {
        //查询本科生
        LambdaQueryWrapper<WhutUndergraduateCourseStudent> ucsQw = Wrappers.lambdaQuery(WhutUndergraduateCourseStudent.class);
        ucsQw.eq(WhutUndergraduateCourseStudent::getXkkh, saveQo.getCode());
        List<WhutUndergraduateCourseStudent> studentList = whutUndergraduateCourseStudentService.list(ucsQw);
        List<String> useridList = studentList.stream().map(WhutUndergraduateCourseStudent::getXh).collect(Collectors.toList());

        //查询本科生状态
        LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
        usQw.in(WhutUndergraduateStatus::getXh, useridList);
        usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
        usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
        List<WhutUndergraduateStatus> list = whutUndergraduateStatusService.list(usQw);

        //查询课程教师信息
        LambdaQueryWrapper<WhutUndergraduateCourseTeacher> wuctQw = Wrappers.lambdaQuery(WhutUndergraduateCourseTeacher.class);
        wuctQw.eq(WhutUndergraduateCourseTeacher::getXkkh, saveQo.getCode());
        List<WhutUndergraduateCourseTeacher> wuctList = whutUndergraduateCourseTeacherService.list(wuctQw);

        if (CollUtil.isNotEmpty(list)) {
            //构建群聊入参
            for (WhutUndergraduateStatus undergraduate : list) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(undergraduate.getXh());
                groupChatScope.setDataName(undergraduate.getXm());
                userList.add(groupChatScope);
            }
            for (WhutUndergraduateCourseTeacher teacher : wuctList) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(teacher.getSkjsgh());
                groupChatScope.setDataName(teacher.getSkjsxm());
                userList.add(groupChatScope);
            }
            //设置教师数量
            saveQo.setTeacherList(wuctList.stream().map(WhutUndergraduateCourseTeacher::getSkjsgh).collect(Collectors.toList()));
        }
    }

    /**
     * 处理班级群研究生
     */
    private void processClassMasterUser(MsgClassGroupChatSaveQo saveQo, List<GroupChatScope> userList) {
        //查询研究生
        LambdaQueryWrapper<WhutMasterCounselorClass> mcQw = Wrappers.lambdaQuery(WhutMasterCounselorClass.class);
        mcQw.eq(WhutMasterCounselorClass::getNj, saveQo.getGrade());
        mcQw.eq(WhutMasterCounselorClass::getXybm, saveQo.getInstituteCode());
        mcQw.eq(WhutMasterCounselorClass::getZybm, saveQo.getMajorCode());
        mcQw.eq(WhutMasterCounselorClass::getXsdqztm, "01");
        mcQw.eq(WhutMasterCounselorClass::getXjztm, "1");
        List<WhutMasterCounselorClass> list = whutMasterCounselorClassService.list(mcQw);
        if (CollUtil.isNotEmpty(list)) {
            //构建群聊入参
            for (WhutMasterCounselorClass whutMasterCounselorClass : list) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(whutMasterCounselorClass.getXh());
                groupChatScope.setDataName(whutMasterCounselorClass.getXm());
                userList.add(groupChatScope);
            }
            //根据辅导员工号去重
            ArrayList<WhutMasterCounselorClass> counselorList = list.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(WhutMasterCounselorClass::getFdygh))), ArrayList::new));
            //将辅导员加入群聊
            for (WhutMasterCounselorClass counselor : counselorList) {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(2);
                groupChatScope.setDataId(counselor.getFdygh());
                groupChatScope.setDataName(counselor.getFdyxm());
                userList.add(groupChatScope);
            }
            //设置辅导员
            List<String> counselorUseridList = counselorList.stream().map(WhutMasterCounselorClass::getFdygh).collect(Collectors.toList());
            saveQo.setCounselorList(counselorUseridList);
        }
    }

    /**
     * 处理班级群本科生
     */
    private void processClassUndergraduateUser(MsgClassGroupChatSaveQo saveQo, List<GroupChatScope> userList) {
        LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
        usQw.eq(WhutUndergraduateStatus::getBjbm, saveQo.getClassCode());
        usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
        usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
        List<WhutUndergraduateStatus> list = whutUndergraduateStatusService.list(usQw);
        //构建群聊入参
        for (WhutUndergraduateStatus whutUndergraduateStatus : list) {
            GroupChatScope groupChatScope = new GroupChatScope();
            groupChatScope.setType(2);
            groupChatScope.setDataId(whutUndergraduateStatus.getXh());
            groupChatScope.setDataName(whutUndergraduateStatus.getXm());
            userList.add(groupChatScope);
        }
        //查询辅导员
        LambdaQueryWrapper<WhutUndergraduateCounselorClass> uccQw = Wrappers.lambdaQuery(WhutUndergraduateCounselorClass.class);
        uccQw.eq(WhutUndergraduateCounselorClass::getBjbm, saveQo.getClassCode());
        List<WhutUndergraduateCounselorClass> uccList = whutUndergraduateCounselorClassService.list(uccQw);
        for (WhutUndergraduateCounselorClass counselorClass : uccList) {
            GroupChatScope groupChatScope = new GroupChatScope();
            groupChatScope.setType(2);
            groupChatScope.setDataId(counselorClass.getFdylxdh());
            groupChatScope.setDataName(counselorClass.getFdyxm());
            userList.add(groupChatScope);
        }
        //设置辅导员数量
        saveQo.setCounselorList(uccList.stream().map(WhutUndergraduateCounselorClass::getFdylxdh).collect(Collectors.toList()));
    }

    @Override
    public List<MsgDeptGroupChatVO> listDeptGroupChat(String userId) {
        List<MsgDeptGroupChatVO> msgDeptGroupChatVOS = msgDeptGroupChatDao.listDeptGroupChat(userId);
        fillDeptPopulation(msgDeptGroupChatVOS);
        return msgDeptGroupChatVOS;
    }

    /**
     * 填充部门人数
     *
     * @param msgDeptGroupChatVOS
     */
    private void fillDeptPopulation(List<MsgDeptGroupChatVO> msgDeptGroupChatVOS) {
        for (MsgDeptGroupChatVO msgDeptGroupChatVO : msgDeptGroupChatVOS) {
            List<MsgDeptGroupChatDetailVO> msgDeptMemberDTOS = whutTeacherDao.listMemberByDwbm(msgDeptGroupChatVO.getDeptCode());
            msgDeptGroupChatVO.setTotalGroupMember(msgDeptMemberDTOS.size());
        }
    }

    @Override
    public List<MsgDeptGroupChatDetailVO> listMsgDeptGroupChatDetail(String deptCode) {
        //根据单位编码查询群聊信息
        MsgDeptGroupChat msgDeptGroupChat = msgDeptGroupChatDao.getByDeptCode(deptCode);
        //根据单位编码查询单位下的人员
        List<MsgDeptGroupChatDetailVO> msgDeptMemberDTOS = whutTeacherDao.listMemberByDwbm(deptCode);
        //如果部门群聊有数据
        if (Objects.nonNull(msgDeptGroupChat) && CollUtil.isNotEmpty(msgDeptGroupChat.getJoinUserList())) {
            List<String> joinUserList = msgDeptGroupChat.getJoinUserList();
            //判断用户是否有加入群聊
            for (MsgDeptGroupChatDetailVO msgDeptMemberDTO : msgDeptMemberDTOS) {
                msgDeptMemberDTO.setHasJoinGroup(joinUserList.contains(msgDeptMemberDTO.getUserid()));
                //判断用户是否绑定了企微，如果有绑定，但是没有加入企业微信的，也是未加入企微
                CpUser user = userService.getCacheUserByUserid(msgDeptMemberDTO.getUserid());
                if (Objects.nonNull(user)) {
                    msgDeptMemberDTO.setAvatar(user.getAvatar());
                }
                msgDeptMemberDTO.setHasJoinQywx(Objects.nonNull(user) && (user.getStatus() == UserStatusEnum.ACTIVATED.getCode() || user.getStatus() == UserStatusEnum.FORBIDDEN.getCode()));
            }
        }
        return msgDeptMemberDTOS;
    }

    @Override
    public void creatDeptGroupChat(MsgDeptGroupChatSaveQo saveQo) throws WxErrorException {
        List<MsgDeptGroupChatDetailVO> msgDeptGroupChatDetailVOS = whutTeacherDao.listMemberByDwbm(saveQo.getDeptCode());
        List<String> useridList = msgDeptGroupChatDetailVOS.stream().filter(MsgDeptGroupChatDetailVO::isHasJoinQywx).map(MsgDeptGroupChatDetailVO::getUserid).collect(Collectors.toList());
        //查询企微用户
        List<CpUser> cpUserList = userService.listByIds(useridList);

        List<GroupChatScope> userList = cpUserList.stream().map(cpUser -> {
            GroupChatScope groupChatScope = new GroupChatScope();
            groupChatScope.setType(2);
            groupChatScope.setDataId(cpUser.getUserid());
            groupChatScope.setDataName(cpUser.getName());
            return groupChatScope;
        }).collect(Collectors.toList());


        //获取当前用户设置为管理员
        String userId = SecurityContextHolder.getUserId();
        String userName = SecurityContextHolder.getUserName();

        ChatInfo chatInfo = new ChatInfo();
        chatInfo.setAgentId(agentProperties.getDeptGroupAgentId());
        chatInfo.setDepartmentList(Collections.emptyList());
        chatInfo.setTagList(Collections.emptyList());
        chatInfo.setUserList(userList);
        //设置群聊信息
        GroupChat groupChat = new GroupChat();
        groupChat.setOwner(userId);
        groupChat.setOwnerName(userName);
        groupChat.setName(saveQo.getChatName());
        groupChat.setOwnerType(0);
        groupChat.setType(GroupConstant.GROUP_CHAT_DEPT);
        //建群消息 如：欢迎加入 xxx 部门群！
        groupChat.setMessage("欢迎加入" + saveQo.getChatName() + "！");
        chatInfo.setChat(groupChat);
        //创建企微群聊
        groupChatService.createGroup(chatInfo);

        //新增部门群聊
        MsgDeptGroupChat msgDeptGroupChat = new MsgDeptGroupChat();
        msgDeptGroupChat.setChatId(groupChat.getChatid());
        msgDeptGroupChat.setChatName(saveQo.getChatName());
        msgDeptGroupChat.setDeptCode(saveQo.getDeptCode());
        msgDeptGroupChat.setDeptName(saveQo.getDeptName());
        msgDeptGroupChat.setManagerUserid(userId);
        msgDeptGroupChat.setManagerName(userName);
        msgDeptGroupChat.setJoinCount(useridList.size());
        msgDeptGroupChat.setJoinUserList(useridList);
        msgDeptGroupChatService.save(msgDeptGroupChat);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateClassGroupChatUser(List<MsgClassGroupChat> list) {
        //根据学生类型分组
        Map<Integer, List<MsgClassGroupChat>> collect = list.stream().collect(Collectors.groupingBy(MsgClassGroupChat::getStudentType));

        List<MsgClassChatUpdateUserDTO> updateUserDTOList = new ArrayList<>();
        for (Map.Entry<Integer, List<MsgClassGroupChat>> entry : collect.entrySet()) {
            Integer studentType = entry.getKey();
            List<MsgClassGroupChat> chatList = entry.getValue();

            //处理本科生
            if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == studentType) {
                updateUndergraduateClassChat(chatList, updateUserDTOList);
            }

            //处理研究生
            if (GroupStudentTypeEnum.MASTER.getCode() == studentType) {
                updateMasterClassChat(chatList, updateUserDTOList);
            }

        }

        //批量更新群聊成员
        if (CollUtil.isNotEmpty(updateUserDTOList)) {
            List<MsgClassGroupChat> updateChatList = new ArrayList<>();
            for (MsgClassChatUpdateUserDTO dto : updateUserDTOList) {
                //更新企微群聊
                WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), agentProperties.getClassGroupAgentId()).getChatService();
                try {
                    wxCpChatService.update(dto.getChat().getChatId(), null, null, dto.getAddUserList(), dto.getDelUserList());
                    updateChatList.add(dto.getChat());
                } catch (WxErrorException e) {
                    log.error("更新班级群聊成员失败：{}", e.getMessage(), e);
                }
            }

            if (CollUtil.isNotEmpty(updateChatList)) {
                //更新群聊信息
                msgClassGroupChatService.updateBatchById(updateChatList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCourseGroupChatUser(List<MsgCourseGroupChat> list) {
        //根据学生类型分组
        Map<Integer, List<MsgCourseGroupChat>> collect = list.stream().collect(Collectors.groupingBy(MsgCourseGroupChat::getStudentType));

        List<MsgCourseChatUpdateUserDTO> updateUserDTOList = new ArrayList<>();
        for (Map.Entry<Integer, List<MsgCourseGroupChat>> entry : collect.entrySet()) {
            Integer studentType = entry.getKey();
            List<MsgCourseGroupChat> chatList = entry.getValue();

            //处理本科生
            if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == studentType) {
                updateUndergraduateCourseChat(chatList, updateUserDTOList);
            }

            //处理研究生
            if (GroupStudentTypeEnum.MASTER.getCode() == studentType) {
                updateMasterCourseChat(chatList, updateUserDTOList);
            }
        }

        //批量更新群聊成员
        if (CollUtil.isNotEmpty(updateUserDTOList)) {
            List<MsgCourseGroupChat> updateChatList = new ArrayList<>();
            for (MsgCourseChatUpdateUserDTO dto : updateUserDTOList) {
                //更新企微群聊
                WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), agentProperties.getCourseGroupAgentId()).getChatService();
                try {
                    wxCpChatService.update(dto.getChat().getChatId(), null, null, dto.getAddUserList(), dto.getDelUserList());
                    updateChatList.add(dto.getChat());
                } catch (WxErrorException e) {
                    log.error("更新课程群聊成员失败：{}", e.getMessage(), e);
                }
            }

            if (CollUtil.isNotEmpty(updateChatList)) {
                //更新群聊信息
                msgCourseGroupChatService.updateBatchById(updateChatList);
            }
        }
    }

    @Override
    public void updateDeptGroupChatUser(List<MsgDeptGroupChat> list) {
        //更新的部门群聊
        List<MsgDeptGroupChat> updateGroupChatList = new ArrayList<>();
        for (MsgDeptGroupChat msgDeptGroupChat : list) {
            List<MsgDeptGroupChatDetailVO> msgDeptMemberDTOS = whutTeacherDao.listMemberByDwbm(msgDeptGroupChat.getDeptCode());
            if (CollUtil.isEmpty(msgDeptMemberDTOS)) {
                continue;
            }
            //当前单位的所有人员
            Set<String> useridList = msgDeptMemberDTOS.stream().map(MsgDeptGroupChatDetailVO::getUserid).collect(Collectors.toSet());
            //已经加入的人员
            Set<String> joinUserList = new HashSet<>(msgDeptGroupChat.getJoinUserList());
            //需要更新的
            List<String> updateUserList = new ArrayList<>(Sets.difference(useridList, joinUserList));

            //更新企微群聊
            if (CollUtil.isNotEmpty(updateUserList)) {
                WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), agentProperties.getDeptGroupAgentId()).getChatService();
                try {
                    wxCpChatService.update(msgDeptGroupChat.getChatId(), null, null, updateUserList, null);
                    msgDeptGroupChat.setJoinUserList(new ArrayList<>(useridList));
                    updateGroupChatList.add(msgDeptGroupChat);
                } catch (WxErrorException e) {
                    log.error("更新部门群聊成员失败：{}", e.getMessage(), e);
                }
            }
        }
        if (CollUtil.isNotEmpty(updateGroupChatList)) {
            //更新群聊信息
            msgDeptGroupChatService.updateBatchById(updateGroupChatList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateGroupChatName(UpdateMsgGroupChatNameQO qo) throws WxErrorException {
        WxCpChatService chatService = wxCpServiceFactory.get(agentProperties.getCorpId(), qo.getAgentId()).getChatService();

        //创建群聊更新对象
        switch (Objects.requireNonNull(WhutGroupType.getByCode(qo.getType()))) {
            case CLASS:
                //根据 id 查询群聊
                MsgClassGroupChat groupChat = new MsgClassGroupChat();
                //更新群聊
                groupChat.setId(qo.getId());
                groupChat.setChatName(qo.getChatName());
                msgClassGroupChatService.updateById(groupChat);
                try {
                    chatService.get(qo.getChatId());
                } catch (WxErrorException e) {
                    log.error(GET_CHAT_ERROR, e);
                    msgClassGroupChatService.removeById(qo.getId());
                    return false;
                }
                break;
            case COURSE:
                //根据 id 查询群聊
                MsgCourseGroupChat courseGroupChat = new MsgCourseGroupChat();
                //更新群聊
                courseGroupChat.setId(qo.getId());
                courseGroupChat.setChatName(qo.getChatName());
                msgCourseGroupChatService.updateById(courseGroupChat);
                try {
                    chatService.get(qo.getChatId());
                } catch (WxErrorException e) {
                    log.error(GET_CHAT_ERROR, e);
                    msgCourseGroupChatService.removeById(qo.getId());
                    return false;
                }
                break;
            case DEPT:
                //根据 id 查询群聊
                MsgDeptGroupChat deptGroupChat = new MsgDeptGroupChat();
                //更新群聊
                deptGroupChat.setId(qo.getId());
                deptGroupChat.setChatName(qo.getChatName());
                msgDeptGroupChatService.updateById(deptGroupChat);
                try {
                    chatService.get(qo.getChatId());
                } catch (WxErrorException e) {
                    log.error(GET_CHAT_ERROR, e);
                    msgDeptGroupChatService.removeById(qo.getId());
                    return false;
                }
                break;
            default:
                throw ExceptionCodeEnum.GROUP_CHAT_TYPE_NOT_SUPPORT.toServiceException();
        }
        wxCpServiceFactory.get(agentProperties.getCorpId(), qo.getAgentId()).getChatService().update(qo.getChatId(), qo.getChatName(), null, null, null);
        return true;
    }

    @Override
    public void inviteGroupUser(InviteGroupUserQO qo) {
        switch (Objects.requireNonNull(WhutGroupType.getByCode(qo.getType()))) {
            case CLASS:
                doInviteClassGroupUser(qo);
                break;
            case COURSE:
                doInviteCourseGroupUser(qo);
                break;
            case DEPT:
                doInviteDeptGroupUser(qo);
                break;
            default:
                throw ExceptionCodeEnum.GROUP_CHAT_TYPE_NOT_SUPPORT.toServiceException();
        }
    }

    @Override
    public GroupCommonInfoVO syncGroupChat(SyncGroupChatQO qo) throws WxErrorException {
        GroupCommonInfoVO groupCommonInfo = null;
        switch (Objects.requireNonNull(WhutGroupType.getByCode(qo.getType()))) {
            case CLASS:
                groupCommonInfo = doSyncClassGroupChatFormCp(qo);
                break;
            case COURSE:
                groupCommonInfo = doSyncCourseGroupChatFormCp(qo);
                break;
            case DEPT:
                groupCommonInfo = doSyncDeptGroupChat(qo);
                break;
            default:
                throw ExceptionCodeEnum.GROUP_CHAT_TYPE_NOT_SUPPORT.toServiceException();
        }
        return groupCommonInfo;
    }

    @Override
    public IPage<MsgDeptGroupChatVO> deptPageGroupChat(MsgDeptGroupChatQO qo) {
        IPage<MsgDeptGroupChatVO> page = new Page<>(qo.getPage(), qo.getSize());
        page.setRecords(deptGrouplistByParam(qo, page));
        return page;
    }

    private List<MsgDeptGroupChatVO> deptGrouplistByParam(MsgDeptGroupChatQO qo, IPage<MsgDeptGroupChatVO> page) {
        List<MsgDeptGroupChatVO> msgDeptGroupChatVOS = msgDeptGroupChatDao.pageDeptGroupChat(page, qo);
        fillDeptPopulation(msgDeptGroupChatVOS);
        return msgDeptGroupChatVOS;
    }

    @Override
    public void deptGroupChatExport(MsgDeptGroupChatQO qo, HttpServletResponse response) {
        String sb = "部门列表" +
                DateUtil.now();
        final String[] fileName = {sb};
        MsgGroupExportHistory history = new MsgGroupExportHistory();
        history.setFileName(fileName[0]);
        history.setCreateTime(DateUtil.date());
        msgGroupExportHistoryService.save(history);
        //异步导出
        try {
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // 这里URLEncoder.encode可以防止中文乱码
            fileName[0] = URLEncoder.encode(fileName[0], StandardCharsets.UTF_8);
            //查询需要导出的数据
            List<MsgDeptGroupChatVO> msgDeptGroupChatVOS = deptGrouplistByParam(qo, null);

            List<DeptExportVO> exportVOList = msgDeptGroupChatVOS.stream().map(deptVO -> {
                DeptExportVO exportVO = new DeptExportVO();
                BeanUtils.copyProperties(deptVO, exportVO);
                return exportVO;
            }).collect(Collectors.toList());
            EasyExcelFactory.write(response.getOutputStream(), DeptExportVO.class).sheet("部门群").doWrite(exportVOList);
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
        } catch (IOException e) {
            log.error("导出部门群列表失败", e);
            history.setStatus(GroupExportHistoryStatusEnum.EXPORT_FAIL.getCode());
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
            response.setContentType("application/json;charset=UTF-8");
            ServletUtil.write(response, JSONObject.toJSONString(Result.build().error()), "application/json");
        }
    }

    @Override
    public IPage<MsgClassGroupChatVO> classGroupPageParam(MsgClassGroupChatQO qo) {
        IPage<MsgClassGroupChatVO> page = new Page<>(qo.getPage(), qo.getSize());
        page.setRecords(classGroupListByParam(qo, page));
        return page;
    }

    @Override
    public void classGroupExport(MsgClassGroupChatQO qo, HttpServletResponse response) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(qo.getStudentType()) && qo.getStudentType() == GroupStudentTypeEnum.UNDERGRADUATE.getCode()) {
            sb.append("本科生");
        } else if (qo.getStudentType() == GroupStudentTypeEnum.MASTER.getCode()) {
            sb.append("研究生");
        }
        sb.append("班级群列表");
        sb.append(DateUtil.now());
        final String[] fileName = {sb.toString()};
        MsgGroupExportHistory history = new MsgGroupExportHistory();
        history.setFileName(fileName[0]);
        history.setCreateTime(DateUtil.date());
        msgGroupExportHistoryService.save(history);
        //异步导出
        try {
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // 这里URLEncoder.encode可以防止中文乱码
            fileName[0] = URLEncoder.encode(fileName[0], StandardCharsets.UTF_8);
            //查询需要导出的数据
            List<MsgClassGroupChatVO> courseGroupChatVOS = classGroupListByParam(qo, null);
            //根据类型选择不同导出
            if (Objects.nonNull(qo.getStudentType()) && qo.getStudentType() == GroupStudentTypeEnum.UNDERGRADUATE.getCode()) {
                classUndergraduateExport(response, courseGroupChatVOS);
            } else if (qo.getStudentType() == GroupStudentTypeEnum.MASTER.getCode()) {
                classMasterExport(response, courseGroupChatVOS);
            }
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
        } catch (IOException e) {
            log.error("导出课程群列表失败", e);
            history.setStatus(GroupExportHistoryStatusEnum.EXPORT_FAIL.getCode());
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
        }
    }

    @Override
    public IPage<MsgCourseGroupChatVO> courseGroupPageByParam(MsgCourseGroupChatQO qo) {
        IPage<MsgCourseGroupChatVO> page = new Page<>(qo.getPage(), qo.getSize());
        page.setRecords(courseGroupListByParam(qo, page));
        return page;
    }

    @Override
    public void courseGroupExport(MsgCourseGroupChatQO qo, HttpServletResponse response) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(qo.getStudentType()) && qo.getStudentType() == 1) {
            sb.append("本科生");
        } else if (Objects.nonNull(qo.getStudentType()) && qo.getStudentType() == 2) {
            sb.append("研究生");
        }
        sb.append("课程群列表");
        sb.append(DateUtil.now());
        final String[] fileName = {sb.toString()};
        MsgGroupExportHistory history = new MsgGroupExportHistory();
        history.setFileName(fileName[0]);
        history.setCreateTime(DateUtil.date());
        msgGroupExportHistoryService.save(history);
        //异步导出
        try {
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // 这里URLEncoder.encode可以防止中文乱码
            fileName[0] = URLEncoder.encode(fileName[0], StandardCharsets.UTF_8);
            //查询需要导出的数据
            List<MsgCourseGroupChatVO> courseGroupChatVOS = courseGroupListByParam(qo, null);
            //装换成导出 VO
            List<CourseGroupExportVO> exportVOList = courseGroupChatVOS.stream().map(course -> {
                CourseGroupExportVO exportVO = new CourseGroupExportVO();
                BeanUtils.copyProperties(course, exportVO);
                return exportVO;
            }).collect(Collectors.toList());
            EasyExcelFactory.write(response.getOutputStream(), CourseGroupExportVO.class).sheet("课程群").doWrite(exportVOList);
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
        } catch (IOException e) {
            log.error("导出课程群列表失败", e);
            history.setStatus(GroupExportHistoryStatusEnum.EXPORT_FAIL.getCode());
            history.setUpdateTime(DateUtil.date());
            msgGroupExportHistoryService.updateById(history);
        }
    }

    @Override
    public GroupCommonInfoVO getGroupCommonInfo(GetGroupCommonInfoQO qo) {
        GroupCommonInfoVO commonInfo;
        switch (Objects.requireNonNull(WhutGroupType.getByCode(qo.getType()))) {
            case CLASS:
                commonInfo = getClassGroupCommonInfo(qo);
                break;
            case COURSE:
                commonInfo = getCourseGroupCommonInfo(qo);
                break;
            case DEPT:
                commonInfo = getDeptGroupCommonInfo(qo);
                break;
            default:
                throw ExceptionCodeEnum.GROUP_CHAT_TYPE_NOT_SUPPORT.toServiceException();
        }
        return commonInfo;
    }

    @Override
    public CheckGroupCreateVO checkCanCreate(String userId) {
        String dwbm = redisService.getCacheMapValue(CommonServiceRedisKeys.GROUP_CHAT_DEPT_HEAD_MAP, userId);
        CheckGroupCreateVO vo = new CheckGroupCreateVO();
        //判断是否有创建部门群聊权限
        vo.setCanCreate(StringUtils.isNotBlank(dwbm));
        Set<String> settingUseridSet = redisService.getCacheMapValue(CommonServiceRedisKeys.GROUP_CHAT_APP_SETTING_MAP, GroupAppSettingEnum.GROUP_APP_SETTING_DEPT.getConfigKey());
        vo.setHasSetting(CollUtil.isNotEmpty(settingUseridSet) && settingUseridSet.contains(userId));
        return vo;
    }

    @Override
    public CheckGroupCreateVO checkCourseGroupCanCreate(String userId) {
        //是否是课程教师
        List<String> teacherList = redisService.getCacheList(CommonServiceRedisKeys.GROUP_CHAT_COURSE_TEACHER);
        CheckGroupCreateVO vo = new CheckGroupCreateVO();
        //判断是否有创建课程群聊权限
        vo.setCanCreate(teacherList.contains(userId));
        Set<String> settingUseridSet = redisService.getCacheMapValue(CommonServiceRedisKeys.GROUP_CHAT_APP_SETTING_MAP, GroupAppSettingEnum.GROUP_APP_SETTING_COURSE.getConfigKey());
        vo.setHasSetting(CollUtil.isNotEmpty(settingUseridSet) && settingUseridSet.contains(userId));
        return vo;
    }

    @Override
    public CheckGroupCreateVO checkClassGroupCanCreate(String userId) {
        //是否是辅导员
        List<String> undergraduateCounselorList = redisService.getCacheList(CommonServiceRedisKeys.GROUP_CHAT_COUNSELOR);
        CheckGroupCreateVO vo = new CheckGroupCreateVO();
        //判断是否有创建班级群聊权限
        vo.setCanCreate(undergraduateCounselorList.contains(userId));
        Set<String> settingUseridSet = redisService.getCacheMapValue(CommonServiceRedisKeys.GROUP_CHAT_APP_SETTING_MAP, GroupAppSettingEnum.GROUP_APP_SETTING_CLASS.getConfigKey());
        vo.setHasSetting(CollUtil.isNotEmpty(settingUseridSet) && settingUseridSet.contains(userId));
        return vo;
    }

    private GroupCommonInfoVO getDeptGroupCommonInfo(GetGroupCommonInfoQO qo) {
        GroupCommonInfoVO groupCommonInfoVO = new GroupCommonInfoVO();
        //如果有群聊id，查询群聊信息
        if (Objects.nonNull(qo.getId())) {
            MsgDeptGroupChat deptGroupChat = msgDeptGroupChatService.getById(qo.getId());
            BeanUtils.copyProperties(deptGroupChat, groupCommonInfoVO);
        }
        List<MsgDeptGroupChatDetailVO> msgDeptGroupChatDetailVOS = whutTeacherDao.listMemberByDwbm(qo.getCode());
        groupCommonInfoVO.setCount(CollUtil.isEmpty(msgDeptGroupChatDetailVOS) ? 0 : msgDeptGroupChatDetailVOS.size());
        return groupCommonInfoVO;
    }

    /**
     * 获取课程群聊公共信息
     *
     * @param qo 查询对象
     * @return 群聊公共信息
     */
    private GroupCommonInfoVO getCourseGroupCommonInfo(GetGroupCommonInfoQO qo) {
        GroupCommonInfoVO groupCommonInfoVO = new GroupCommonInfoVO();
        //如果有群聊id，查询群聊信息
        if (Objects.nonNull(qo.getId())) {
            MsgCourseGroupChat courseGroupChat = msgCourseGroupChatService.getById(qo.getId());
            BeanUtils.copyProperties(courseGroupChat, groupCommonInfoVO);
        }
        String fdyGhStrByCode = null;
        Integer count = 0;
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == qo.getStudentType()) {
            //根据课程编码查询任课教师
            fdyGhStrByCode = msgCourseGroupChatDao.getUndergRkjsGhStrByCode(qo.getCode());
            count = msgCourseGroupChatDao.getUndergStuCount(qo.getCode());
        }
        if (GroupStudentTypeEnum.MASTER.getCode() == qo.getStudentType()) {
            //根据课程编码查询任课教师
            fdyGhStrByCode = msgCourseGroupChatDao.getMasterRkjsGhStrByCode(qo.getCode());
            count = msgCourseGroupChatDao.getMasterStuCount(qo.getCode());
        }
        //填充可创建群聊工号
        fillInCanCreate(groupCommonInfoVO, fdyGhStrByCode);
        groupCommonInfoVO.setCount(count);
        return groupCommonInfoVO;
    }

    /**
     * 班级建群获取群公共信息
     *
     * @param qo 查询对象
     * @return 群聊公共信息
     */
    private GroupCommonInfoVO getClassGroupCommonInfo(GetGroupCommonInfoQO qo) {
        GroupCommonInfoVO groupCommonInfoVO = new GroupCommonInfoVO();
        //如果有群聊id，查询群聊信息
        if (Objects.nonNull(qo.getId())) {
            MsgClassGroupChat classGroupChat = msgClassGroupChatService.getById(qo.getId());
            BeanUtils.copyProperties(classGroupChat, groupCommonInfoVO);
        }
        String fdyGhStrByCode = null;
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == qo.getStudentType()) {
            //根据班级编码查询班级信息
            fdyGhStrByCode = msgClassGroupChatDao.getUndergFdyGhStrByCode(qo.getCode());
            List<MsgClassGroupChatCountDTO> undergraduateCountList = whutUndergraduateStatusDao.listCountByClassCode(Collections.singletonList(qo.getCode()));
            groupCommonInfoVO.setCount(undergraduateCountList.get(0).getClassUserCount());
        }
        if (GroupStudentTypeEnum.MASTER.getCode() == qo.getStudentType()) {
            String[] codes = qo.getCode().split("_");
            fdyGhStrByCode = msgClassGroupChatDao.getMasterFdyGhStrByCode(codes[0], codes[1], codes[2]);
            List<MsgClassGroupChatCountDTO> masterCountList = whutMasterCounselorClassDao.listCountByMajorCode(Collections.singletonList(codes[2]));
            //转为map
            Map<String, Integer> masterCountMap = CollUtil.toMap(masterCountList, null, item ->
                    item.getGrade() + "-" + item.getInstituteCode() + "-" + item.getMajorCode(), MsgClassGroupChatCountDTO::getClassUserCount);
            groupCommonInfoVO.setCount(masterCountMap.get(codes[0] + "-" + codes[1] + "-" + codes[2]));
        }
        if (GroupStudentTypeEnum.INTERNATIONAL.getCode() == qo.getStudentType()) {
            fdyGhStrByCode = msgClassGroupChatDao.getInterStuFdyGhStrByCode(qo.getCode());
            List<MsgClassGroupChatCountDTO> internationalCountList = whutInterEduCollegeStudentDao.listCountByBjmc(Collections.singletonList(qo.getCode()));
            groupCommonInfoVO.setCount(internationalCountList.get(0).getClassUserCount());
        }
        //填充可以创建群聊人信息
        fillInCanCreate(groupCommonInfoVO, fdyGhStrByCode);
        return groupCommonInfoVO;
    }

    /**
     * 群聊公共信息填充（教师工号或学生信息）
     *
     * @param groupCommonInfoVO 群聊公共信息
     * @param teacherGhStr      教师工号字符串多个用逗号隔开
     */
    private void fillInCanCreate(GroupCommonInfoVO groupCommonInfoVO, String teacherGhStr) {
        groupCommonInfoVO.setCanCreateGh(teacherGhStr);
        if (StringUtils.isNotBlank(teacherGhStr)) {
            List<CpUser> cpUsers = userService.listCacheUserByUserids(List.of(teacherGhStr.split(",")));
            String canCreateNames = cpUsers.stream().map(CpUser::getName).collect(Collectors.joining(","));
            groupCommonInfoVO.setCanCreateName(canCreateNames);
        }
    }

    private List<MsgCourseGroupChatVO> courseGroupListByParam(MsgCourseGroupChatQO qo, IPage<MsgCourseGroupChatVO> page) {
        List<MsgCourseGroupChatVO> msgCourseGroupChatVOS = msgCourseGroupChatDao.pageCourseGroupChatByParam(page, qo);
        //判断是否使用名称或者工号查询
        if (StringUtils.isNotBlank(qo.getCourseTeacher()) || StringUtils.isNotBlank(qo.getCourseTeacherGh())) {
            //填充教师信息
            fillCourseTeacherInfo(msgCourseGroupChatVOS);
        }
        //填充学生数量
        fillCourseStuPopulation(msgCourseGroupChatVOS);
        return msgCourseGroupChatVOS;
    }

    /**
     * 填充课程教师信息
     *
     * @param msgCourseGroupChatVOS 课程群聊对象
     */
    private void fillCourseTeacherInfo(List<MsgCourseGroupChatVO> msgCourseGroupChatVOS) {
        List<CourseCodeSkjsInfoVO> undergGroupInfoVOS = msgCourseGroupChatDao.undergGroupByCode();
        List<CourseCodeSkjsInfoVO> masterGroupInfoVOS = msgCourseGroupChatDao.masterGroupByCode();
        Map<String, CourseCodeSkjsInfoVO> undergGroupInfoVOMap = undergGroupInfoVOS.stream().collect(Collectors.toMap(CourseCodeSkjsInfoVO::getCourseCode, o -> o));
        Map<String, CourseCodeSkjsInfoVO> masterGroupInfoVOMap = masterGroupInfoVOS.stream().collect(Collectors.toMap(CourseCodeSkjsInfoVO::getCourseCode, o -> o));
        msgCourseGroupChatVOS.forEach(course -> {
            if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == course.getStudentType()) {
                CourseCodeSkjsInfoVO courseCodeSkjsInfoVO = undergGroupInfoVOMap.get(course.getCode());
                course.setCanCreateName(courseCodeSkjsInfoVO.getCanCreateName());
                course.setCanCreateGh(courseCodeSkjsInfoVO.getCanCreateGh());
            }
            if (GroupStudentTypeEnum.MASTER.getCode() == course.getStudentType()) {
                CourseCodeSkjsInfoVO courseCodeSkjsInfoVO = masterGroupInfoVOMap.get(course.getCode());
                course.setCanCreateName(courseCodeSkjsInfoVO.getCanCreateName());
                course.setCanCreateGh(courseCodeSkjsInfoVO.getCanCreateGh());
            }
        });
    }

    private static void classMasterExport(HttpServletResponse response, List<MsgClassGroupChatVO> courseGroupChatVOS) throws IOException {
        List<MasterClassExportVO> exportVOList = courseGroupChatVOS.stream().map(course -> {
            MasterClassExportVO exportVO = new MasterClassExportVO();
            BeanUtils.copyProperties(course, exportVO);
            exportVO.setTeacherGh(course.getCanCreateGh());
            exportVO.setTeacherName(course.getCanCreateName());
            return exportVO;
        }).collect(Collectors.toList());
        EasyExcelFactory.write(response.getOutputStream(), MasterClassExportVO.class).sheet("班级群").doWrite(exportVOList);
    }

    private static void classUndergraduateExport(HttpServletResponse response, List<MsgClassGroupChatVO> courseGroupChatVOS) throws IOException {
        List<UndergraduateClassExportVO> exportVOList = courseGroupChatVOS.stream().map(course -> {
            UndergraduateClassExportVO exportVO = new UndergraduateClassExportVO();
            BeanUtils.copyProperties(course, exportVO);
            exportVO.setTeacherGh(course.getCanCreateGh());
            exportVO.setTeacherName(course.getCanCreateName());
            return exportVO;
        }).collect(Collectors.toList());
        EasyExcelFactory.write(response.getOutputStream(), UndergraduateClassExportVO.class).sheet("班级群").doWrite(exportVOList);
    }

    private List<MsgClassGroupChatVO> classGroupListByParam(MsgClassGroupChatQO qo, IPage<MsgClassGroupChatVO> page) {
        List<MsgClassGroupChatVO> list;
        Integer studentType = qo.getStudentType();
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == studentType) {
            list = msgClassGroupChatDao.pageUndergraduateByParam(page, qo);
        } else if (GroupStudentTypeEnum.MASTER.getCode() == studentType) {
            list = msgClassGroupChatDao.pageMasterByParam(page, qo);
        } else {
            list = msgClassGroupChatDao.pageInternationalByParam(page, qo);
        }
        //填充教师信息
        fillClassTeacherInfo(list);
        //填充学生人数
        fillTheStudentPopulation(list);
        return list;
    }

    private void fillClassTeacherInfo(List<MsgClassGroupChatVO> list) {
        //本科生班级分组教师信息
        List<ClassCodeFdyInfoVO> undergGroupInfoVOS = msgClassGroupChatDao.undergGroupByCode(null);
        //研究生班级分组教师信息
        List<ClassCodeFdyInfoVO> masterGroupInfoVOS = msgClassGroupChatDao.masterGroupByCode(null);
        //国教生班级分组教师信息
        List<ClassCodeFdyInfoVO> interStuGroupInfoVOS = msgClassGroupChatDao.interStuGroupByCode(null);
        Map<String, ClassCodeFdyInfoVO> undergMap = undergGroupInfoVOS.stream().collect(Collectors.toMap(ClassCodeFdyInfoVO::getClassCode, o -> o));
        Map<String, ClassCodeFdyInfoVO> masterMap = masterGroupInfoVOS.stream()
                .collect(Collectors.toMap(classCodeFdyInfoVO -> classCodeFdyInfoVO.getGrade() + classCodeFdyInfoVO.getInstituteCode() + classCodeFdyInfoVO.getMajorCode(), o -> o));
        Map<String, ClassCodeFdyInfoVO> interStuMap = interStuGroupInfoVOS.stream().collect(Collectors.toMap(ClassCodeFdyInfoVO::getClassCode, o -> o));
        //填充人员
        for (MsgClassGroupChatVO classGroupChatVO : list) {
            if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == classGroupChatVO.getStudentType()) {
                ClassCodeFdyInfoVO classCodeFdyInfoVO = undergMap.get(classGroupChatVO.getClassCode());
                String canCreateGh = classCodeFdyInfoVO.getCanCreateGh();
                if (StringUtils.isNotBlank(canCreateGh)) {
                    List<CpUser> cpUsers = userService.listCacheUserByUserids(List.of(canCreateGh.split(",")));
                    classGroupChatVO.setCanCreateGh(canCreateGh);
                    classGroupChatVO.setCanCreateName(cpUsers.stream().filter(Objects::nonNull).map(CpUser::getName).collect(Collectors.joining(",")));
                }
            } else if (GroupStudentTypeEnum.MASTER.getCode() == classGroupChatVO.getStudentType()) {
                ClassCodeFdyInfoVO classCodeFdyInfoVO = masterMap.get(classGroupChatVO.getGrade() + classGroupChatVO.getInstituteCode() + classGroupChatVO.getMajorCode());
                String canCreateGh = classCodeFdyInfoVO.getCanCreateGh();
                if (StringUtils.isNotBlank(canCreateGh)) {
                    List<CpUser> cpUsers = userService.listCacheUserByUserids(List.of(canCreateGh.split(",")));
                    classGroupChatVO.setCanCreateGh(canCreateGh);
                    classGroupChatVO.setCanCreateName(cpUsers.stream().filter(Objects::nonNull).map(CpUser::getName).collect(Collectors.joining(",")));
                }
            } else if (GroupStudentTypeEnum.INTERNATIONAL.getCode() == classGroupChatVO.getStudentType()) {
                ClassCodeFdyInfoVO classCodeFdyInfoVO = interStuMap.get(classGroupChatVO.getClassCode());
                String canCreateGh = classCodeFdyInfoVO.getCanCreateGh();
                if (StringUtils.isNotBlank(canCreateGh)) {
                    List<CpUser> cpUsers = userService.listCacheUserByUserids(List.of(canCreateGh.split(",")));
                    classGroupChatVO.setCanCreateGh(canCreateGh);
                    classGroupChatVO.setCanCreateName(cpUsers.stream().filter(Objects::nonNull).map(CpUser::getName).collect(Collectors.joining(",")));
                }
            }
        }
    }

    private GroupCommonInfoVO doSyncClassGroupChatFormCp(SyncGroupChatQO qo) throws WxErrorException {
        MsgClassGroupChat classGroupChat = msgClassGroupChatService.getById(qo.getId());
        if (Objects.isNull(classGroupChat)) {
            throw ExceptionCodeEnum.GROUP_CHAT_NOT_EXIST.toServiceException();
        }
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), qo.getAgentId()).getChatService();
        WxCpChat wxCpChat = null;
        String chatId = classGroupChat.getChatId();
        try {
            wxCpChat = wxCpChatService.get(chatId);
        } catch (WxErrorException e) {
            log.error(GET_CHAT_ERROR, e);
            msgClassGroupChatService.removeById(qo.getId());
            return null;
        }

        MsgClassGroupChatDetailQo classGroupChatDetailQo = new MsgClassGroupChatDetailQo();
        BeanUtils.copyProperties(classGroupChat, classGroupChatDetailQo);
        classGroupChatDetailQo.setClassGroupChatId(classGroupChat.getId());

        List<MsgClassGroupChatDetailVO> classGroupChatDetailVO = this.getMsgClassGroupChatDetailVO(classGroupChatDetailQo);
        Set<String> joinCpUseridSet = classGroupChatDetailVO.stream()
                .filter(item -> YnEnum.YES.getValue() == item.getIsInCp())
                .map(MsgClassGroupChatDetailVO::getUserid)
                .collect(Collectors.toSet());
        //本地已经加入群聊的用户
        Set<String> localJoinUserSet = new HashSet<>(classGroupChat.getJoinUserList());
        //远程已经加入群聊的用户
        Set<String> remoteJoinUserSet = new HashSet<>(wxCpChat.getUsers());
        //需要更新的
        List<String> updateUserList = new ArrayList<>(Sets.difference(joinCpUseridSet, remoteJoinUserSet));
        //需要删除的
        List<String> deleteUserList = new ArrayList<>(Sets.difference(localJoinUserSet, joinCpUseridSet));
        //重新设置加入企微的数量，和具体加入企微的人员
        classGroupChat.setJoinCount(joinCpUseridSet.size());
        classGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        //获取班级对应的教师信息
        String fdyGhStr = "";
        if (GroupStudentTypeEnum.UNDERGRADUATE.getCode() == classGroupChat.getStudentType()) {
            fdyGhStr = msgClassGroupChatDao.getUndergFdyGhStrByCode(classGroupChat.getClassCode());
        } else if (GroupStudentTypeEnum.MASTER.getCode() == classGroupChat.getStudentType()) {
            fdyGhStr = msgClassGroupChatDao.getMasterFdyGhStrByCode(classGroupChat.getGrade(), classGroupChat.getInstituteCode(), classGroupChat.getMajorCode());
        } else if (GroupStudentTypeEnum.INTERNATIONAL.getCode() == classGroupChat.getStudentType()) {
            fdyGhStr = msgClassGroupChatDao.getInterStuFdyGhStrByCode(classGroupChat.getClassCode());
        }
        updateUserList.addAll(List.of(fdyGhStr.split(",")));
        //将企微返回的群主信息更新到本地
        CpUser user = userService.getCacheUserByUserid(wxCpChat.getOwner());
        classGroupChat.setManagerUserid(user.getUserid());
        classGroupChat.setManagerName(user.getName());
        try {
            wxCpChatService.update(chatId, classGroupChat.getChatName(), null, updateUserList, deleteUserList);
            classGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        } catch (WxErrorException e) {
            log.error("更新班级群聊成员失败：{}", e.getMessage(), e);
        }
        //更新群聊信息
        msgClassGroupChatService.updateById(classGroupChat);
        return new GroupCommonInfoVO()
                .setChatName(classGroupChat.getChatName())
                .setCount(classGroupChatDetailVO.size())
                .setManagerUserid(classGroupChat.getManagerUserid())
                .setManagerName(classGroupChat.getManagerName());
    }

    private GroupCommonInfoVO doSyncCourseGroupChatFormCp(SyncGroupChatQO qo) throws WxErrorException {
        MsgCourseGroupChat courseGroupChat = msgCourseGroupChatService.getById(qo.getId());
        if (Objects.isNull(courseGroupChat)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("群聊不存在").toServiceException();
        }
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), qo.getAgentId()).getChatService();
        String chatId = courseGroupChat.getChatId();
        WxCpChat wxCpChat = null;
        try {
            wxCpChat = wxCpChatService.get(chatId);
        } catch (WxErrorException e) {
            log.error(GET_CHAT_ERROR, e);
        }
        //如果无法获取到群聊信息，标识群聊已被删除，将群聊信息删除
        if (Objects.isNull(wxCpChat)) {
            msgCourseGroupChatService.removeById(qo.getId());
            return null;
        }
        MsgCourseGroupChatDetailQo courseGroupChatDetailQo = new MsgCourseGroupChatDetailQo();
        courseGroupChatDetailQo.setCode(courseGroupChat.getCode());
        courseGroupChatDetailQo.setStudentType(courseGroupChat.getStudentType());
        courseGroupChatDetailQo.setCourseGroupChatId(courseGroupChat.getId());
        List<MsgCourseGroupChatDetailVO> courseGroupChatDetailVOList = this.getMsgCourseGroupChatDetailVO(courseGroupChatDetailQo);
        Set<String> joinCpUseridSet = courseGroupChatDetailVOList.stream()
                .filter(item -> YnEnum.YES.getValue() == item.getIsInCp())
                .map(MsgCourseGroupChatDetailVO::getUserid)
                .collect(Collectors.toSet());
        //本地已经加入群聊的用户
        Set<String> localJoinUserSet = new HashSet<>(courseGroupChat.getJoinUserList());
        //远程已经加入群聊的用户
        Set<String> remoteJoinUserSet = new HashSet<>(wxCpChat.getUsers());
        //需要更新的
        List<String> updateUserList = new ArrayList<>(Sets.difference(joinCpUseridSet, remoteJoinUserSet));
        //需要删除的
        List<String> deleteUserList = new ArrayList<>(Sets.difference(localJoinUserSet, joinCpUseridSet));
        //重新设置加入企微的数量，和具体加入企微的人员
        courseGroupChat.setJoinCount(joinCpUseridSet.size());
        courseGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        //获取课程对应的教师信息
        List<String> teacherGhList = courseGroupChatDetailVOList.get(0).getTeacherGhList();
        updateUserList.addAll(teacherGhList);
        //将企微返回的群主信息更新到本地
        CpUser user = userService.getCacheUserByUserid(wxCpChat.getOwner());
        courseGroupChat.setManagerUserid(user.getUserid());
        courseGroupChat.setManagerName(user.getName());
        try {
            wxCpChatService.update(chatId, courseGroupChat.getChatName(), null, updateUserList, deleteUserList);
            courseGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        } catch (WxErrorException e) {
            log.error("更新课程群聊成员失败：{}", e.getMessage(), e);
        }
        //更新群聊信息
        msgCourseGroupChatService.updateById(courseGroupChat);
        return new GroupCommonInfoVO()
                .setChatName(courseGroupChat.getChatName())
                .setCount(courseGroupChatDetailVOList.size())
                .setManagerUserid(courseGroupChat.getManagerUserid())
                .setManagerName(courseGroupChat.getManagerName());
    }


    private GroupCommonInfoVO doSyncDeptGroupChat(SyncGroupChatQO qo) throws WxErrorException {
        MsgDeptGroupChat deptGroupChat = msgDeptGroupChatService.getById(qo.getId());
        if (Objects.isNull(deptGroupChat)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("群聊不存在").toServiceException();
        }
        //初始化企微信息
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(agentProperties.getCorpId(), qo.getAgentId()).getChatService();
        String chatId = deptGroupChat.getChatId();
        WxCpChat wxCpChat = null;
        try {
            wxCpChat = wxCpChatService.get(chatId);
        } catch (WxErrorException e) {
            log.error(GET_CHAT_ERROR, e);
        }
        //如果无法获取到群聊信息，标识群聊已被删除，将群聊信息删除
        if (Objects.isNull(wxCpChat)) {
            msgDeptGroupChatService.removeById(qo.getId());
            return null;
        }
        //查询群聊详情
        List<MsgDeptGroupChatDetailVO> deptGroupChatDetailVOList = this.listMsgDeptGroupChatDetail(deptGroupChat.getDeptCode());
        //过滤出已经加入企微的用户
        Set<String> joinCpUseridSet = deptGroupChatDetailVOList.stream()
                .filter(MsgDeptGroupChatDetailVO::isHasJoinQywx)
                .map(MsgDeptGroupChatDetailVO::getUserid)
                .collect(Collectors.toSet());
        //远程已经加入群聊的用户
        Set<String> remoteJoinUserSet = new HashSet<>(wxCpChat.getUsers());
        //需要更新的
        List<String> updateUserList = new ArrayList<>(Sets.difference(joinCpUseridSet, remoteJoinUserSet));
        //重新设置加入企微的数量，和具体加入企微的人员
        deptGroupChat.setJoinCount(joinCpUseridSet.size());
        deptGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        //将企微返回的群主信息更新到本地
        CpUser user = userService.getCacheUserByUserid(wxCpChat.getOwner());
        deptGroupChat.setManagerUserid(user.getUserid());
        deptGroupChat.setManagerName(user.getName());
        try {
            wxCpChatService.update(chatId, deptGroupChat.getChatName(), null, updateUserList, null);
            deptGroupChat.setJoinUserList(new ArrayList<>(joinCpUseridSet));
        } catch (WxErrorException e) {
            log.error("更新部门群聊成员失败：{}", e.getMessage(), e);
        }
        //更新群聊信息
        msgDeptGroupChatService.updateById(deptGroupChat);
        return new GroupCommonInfoVO()
                .setChatName(deptGroupChat.getChatName())
                .setCount(deptGroupChatDetailVOList.size())
                .setManagerUserid(deptGroupChat.getManagerUserid())
                .setManagerName(deptGroupChat.getManagerName());
    }

    /**
     * 邀请班级群聊成员
     *
     * @param qo 邀请条件对象
     */
    private void doInviteClassGroupUser(InviteGroupUserQO qo) {
        MsgClassGroupChatDetailQo classGroupChatDetailQo = new MsgClassGroupChatDetailQo();
        BeanUtils.copyProperties(qo, classGroupChatDetailQo);
        //获取群聊信息
        classGroupChatDetailQo.setClassGroupChatId(qo.getId());
        classGroupChatDetailQo.setClassCode(qo.getCode());

        List<MsgClassGroupChatDetailVO> msgClassGroupChatDetailVO = this.getMsgClassGroupChatDetailVO(classGroupChatDetailQo);
        //过滤掉已经加入企业微信的成员，并且根据是否有企微区分出是否绑定的用户群体
        Map<Boolean, List<String>> hasBindMap = msgClassGroupChatDetailVO.stream()
                .filter(item -> YnEnum.NO.getValue() == item.getIsInCp())
                .collect(Collectors.groupingBy(item -> StringUtils.isNotBlank(item.getUserid()), Collectors.mapping(MsgClassGroupChatDetailVO::getUserid, Collectors.toList())));
        //获取已经绑定的，其中用户在企微的状态可能为 4 未加入企微 5 已退出企微
        inviteUserJoinCp(hasBindMap.get(true));
    }

    /**
     * 邀请班级群聊成员
     *
     * @param qo 邀请条件对象
     */
    private void doInviteCourseGroupUser(InviteGroupUserQO qo) {
        MsgCourseGroupChatDetailQo courseGroupChatDetailQo = new MsgCourseGroupChatDetailQo();
        BeanUtils.copyProperties(qo, courseGroupChatDetailQo);
        //获取群聊信息
        courseGroupChatDetailQo.setCourseGroupChatId(qo.getId());
        List<MsgCourseGroupChatDetailVO> msgCourseGroupChatDetailVO = this.getMsgCourseGroupChatDetailVO(courseGroupChatDetailQo);
        //过滤掉已经加入企业微信的成员，并且根据是否有企微区分出是否绑定的用户群体
        Map<Boolean, List<String>> hasBindMap = msgCourseGroupChatDetailVO.stream()
                .filter(item -> YnEnum.NO.getValue() == item.getIsInCp())
                .collect(Collectors.groupingBy(item -> StringUtils.isNotBlank(item.getUserid()), Collectors.mapping(MsgCourseGroupChatDetailVO::getUserid, Collectors.toList())));
        //获取已经绑定的，其中用户在企微的状态可能为 4 未加入企微 5 已退出企微
        inviteUserJoinCp(hasBindMap.get(true));

    }

    /**
     * 部门邀请成员
     *
     * @param qo 邀请条件对象
     */
    private void doInviteDeptGroupUser(InviteGroupUserQO qo) {
        //获取群聊信息
        MsgDeptGroupChat deptGroupChat = msgDeptGroupChatService.getById(qo.getId());
        if (Objects.nonNull(deptGroupChat)) {
            qo.setCode(deptGroupChat.getDeptCode());
        }
        List<MsgDeptGroupChatDetailVO> deptGroupChatDetailVOList = this.listMsgDeptGroupChatDetail(qo.getCode());
        //过滤掉已经加入企业微信的成员，并且根据是否有企微区分出是否绑定的用户群体
        Map<Boolean, List<String>> hasBindMap = deptGroupChatDetailVOList.stream()
                .filter(item -> !item.isHasJoinQywx())
                .collect(Collectors.groupingBy(item -> StringUtils.isNotBlank(item.getUserid()), Collectors.mapping(MsgDeptGroupChatDetailVO::getUserid, Collectors.toList())));
        //获取已经绑定的，其中用户在企微的状态可能为 4 未加入企微 5 已退出企微
        inviteUserJoinCp(hasBindMap.get(true));
    }

    private void inviteUserJoinCp(List<String> bindUseridList) {
        if (CollUtil.isEmpty(bindUseridList)) {
            return;
        }
        //根据用户状态获取用户状态是未加入企微的用户
        List<String> notJoinCp = bindUseridList.stream().filter(userid -> {
            CpUser user = userService.getCacheUserByUserid(userid);
            if (Objects.isNull(user)) {
                return false;
            }
            return user.getStatus() == UserStatusEnum.INACTIVE.getCode();
        }).collect(Collectors.toList());

        if (notJoinCp.isEmpty()) {
            return;
        }

        //邀请加入企微
        BatchInviteQo batchInviteQo = new BatchInviteQo();
        batchInviteQo.setUser(notJoinCp);
        userService.batchInvite(batchInviteQo);
    }

    /**
     * 更新研究生班级群聊
     */
    private void updateMasterClassChat(List<MsgClassGroupChat> chatList, List<MsgClassChatUpdateUserDTO> updateUserDTOList) {
        //查询辅导员带生信息
        LambdaQueryWrapper<WhutMasterCounselorClass> mcQw = Wrappers.lambdaQuery(WhutMasterCounselorClass.class);
        mcQw.eq(WhutMasterCounselorClass::getXsdqztm, "01");
        mcQw.eq(WhutMasterCounselorClass::getXjztm, "1");
        List<WhutMasterCounselorClass> mcList = whutMasterCounselorClassService.list(mcQw);
        //按年级、专业分组
        Map<String, List<WhutMasterCounselorClass>> gradeMap = mcList.stream().collect(Collectors.groupingBy(item -> item.getNj() + "-" + item.getZybm(), Collectors.toList()));

        for (MsgClassGroupChat chat : chatList) {
            List<String> addUserList = new ArrayList<>();

            //年级+专业 组成班级编码
            String classCode = chat.getGrade() + "-" + chat.getMajorCode();
            //已入群的群聊成员列表
            List<String> joinUserList = chat.getJoinUserList();

            //取出有效的班级成员
            List<WhutMasterCounselorClass> statusList = gradeMap.get(classCode);
            //需要加入群聊的成员
            List<String> addStudentList = statusList.stream().map(WhutMasterCounselorClass::getXh).filter(xh -> !joinUserList.contains(xh)).distinct().collect(Collectors.toList());
            //取出在企微的成员
            addStudentList = addStudentList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addStudentList)) {
                joinUserList.addAll(addStudentList);
                addUserList.addAll(addStudentList);
            }

            //需要踢出群聊的成员
            List<String> delUserList = joinUserList.stream().filter(xh -> !statusList.stream().map(WhutMasterCounselorClass::getXh).collect(Collectors.toList()).contains(xh)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delUserList)) {
                joinUserList.removeAll(delUserList);
            }

            List<String> counselorList = chat.getCounselorList();
            //查询辅导员
            List<WhutMasterCounselorClass> counselorClassList = gradeMap.get(classCode);
            //辅导员工号去重
            List<String> newCounselorList = counselorClassList.stream().map(WhutMasterCounselorClass::getFdygh).distinct().collect(Collectors.toList());

            //需要加入的辅导员
            List<String> addCounselorList = newCounselorList.stream().filter(xh -> !counselorList.contains(xh)).collect(Collectors.toList());
            //取出在企微的辅导员
            addCounselorList = addCounselorList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addCounselorList)) {
                counselorList.addAll(addCounselorList);
                addUserList.addAll(addCounselorList);
            }

            //如果人员有变动则更新群聊
            if (CollUtil.isNotEmpty(addUserList) || CollUtil.isNotEmpty(delUserList) || CollUtil.isNotEmpty(addCounselorList)) {
                MsgClassChatUpdateUserDTO updateUserDTO = new MsgClassChatUpdateUserDTO();
                updateUserDTO.setChat(chat);
                updateUserDTO.setAddUserList(addUserList);
                updateUserDTO.setDelUserList(delUserList);
                updateUserDTOList.add(updateUserDTO);
            }
        }
    }

    /**
     * 更新本科生班级群聊
     */
    private void updateUndergraduateClassChat(List<MsgClassGroupChat> chatList, List<MsgClassChatUpdateUserDTO> updateUserDTOList) {

        //查询本科生辅导员带班信息
        LambdaQueryWrapper<WhutUndergraduateCounselorClass> uccQw = Wrappers.lambdaQuery(WhutUndergraduateCounselorClass.class);
        List<WhutUndergraduateCounselorClass> uccList = whutUndergraduateCounselorClassService.list(uccQw);
        //按班级分组
        Map<String, List<WhutUndergraduateCounselorClass>> classMap = uccList.stream().collect(Collectors.groupingBy(WhutUndergraduateCounselorClass::getBjbm, Collectors.toList()));

        //查询本科生状态
        LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
        usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
        usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
        List<WhutUndergraduateStatus> usList = whutUndergraduateStatusService.list(usQw);
        //按班级分组
        Map<String, List<WhutUndergraduateStatus>> classStatusMap = usList.stream().collect(Collectors.groupingBy(WhutUndergraduateStatus::getBjbm, Collectors.toList()));

        for (MsgClassGroupChat chat : chatList) {
            List<String> addUserList = new ArrayList<>();

            String classCode = chat.getClassCode();
            //已入群的群聊成员列表
            List<String> joinUserList = chat.getJoinUserList();

            //取出有效的班级成员
            List<WhutUndergraduateStatus> statusList = classStatusMap.get(classCode);
            //需要加入群聊的成员
            List<String> addStudentList = statusList.stream().map(WhutUndergraduateStatus::getXh).filter(xh -> !joinUserList.contains(xh)).collect(Collectors.toList());
            //取出在企微的成员
            addStudentList = addStudentList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addStudentList)) {
                joinUserList.addAll(addStudentList);
                addUserList.addAll(addStudentList);
            }

            //需要踢出群聊的成员
            List<String> delUserList = joinUserList.stream().filter(xh -> !statusList.stream().map(WhutUndergraduateStatus::getXh).collect(Collectors.toList()).contains(xh)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delUserList)) {
                joinUserList.removeAll(delUserList);
            }

            List<String> counselorList = chat.getCounselorList();
            //查询辅导员
            List<WhutUndergraduateCounselorClass> counselorClassList = classMap.get(classCode);
            List<String> newCounselorList = counselorClassList.stream().map(WhutUndergraduateCounselorClass::getFdyxgh).collect(Collectors.toList());
            //需要加入的辅导员
            List<String> addCounselorList = newCounselorList.stream().filter(xh -> !counselorList.contains(xh)).collect(Collectors.toList());
            //取出在企微的辅导员
            addCounselorList = addCounselorList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addCounselorList)) {
                counselorList.addAll(addCounselorList);
                addUserList.addAll(addCounselorList);
            }

            //如果人员有变动则更新群聊
            if (CollUtil.isNotEmpty(addUserList) || CollUtil.isNotEmpty(delUserList) || CollUtil.isNotEmpty(addCounselorList)) {
                MsgClassChatUpdateUserDTO updateUserDTO = new MsgClassChatUpdateUserDTO();
                updateUserDTO.setChat(chat);
                updateUserDTO.setAddUserList(addUserList);
                updateUserDTO.setDelUserList(delUserList);
                updateUserDTOList.add(updateUserDTO);
            }
        }
    }

    /**
     * 更新本科生课程群聊
     */
    private void updateUndergraduateCourseChat(List<MsgCourseGroupChat> chatList, List<MsgCourseChatUpdateUserDTO> updateUserDTOList) {
        //查询本科生
        List<WhutUndergraduateCourseStudent> ucsList = whutUndergraduateCourseStudentService.list();
        //按课表编码分组
        Map<String, List<WhutUndergraduateCourseStudent>> studentMap = ucsList.stream().collect(Collectors.groupingBy(WhutUndergraduateCourseStudent::getXkkh, Collectors.toList()));

        //查询教师
        List<WhutUndergraduateCourseTeacher> uctList = whutUndergraduateCourseTeacherService.list();
        //按课表编码分组
        Map<String, List<WhutUndergraduateCourseTeacher>> teacherMap = uctList.stream().collect(Collectors.groupingBy(WhutUndergraduateCourseTeacher::getXkkh, Collectors.toList()));

        //查询本科生状态
        LambdaQueryWrapper<WhutUndergraduateStatus> usQw = Wrappers.lambdaQuery(WhutUndergraduateStatus.class);
        usQw.eq(WhutUndergraduateStatus::getSfzj, "1");
        usQw.eq(WhutUndergraduateStatus::getSfzx, "1");
        List<WhutUndergraduateStatus> usList = whutUndergraduateStatusService.list(usQw);
        //有效学生列表
        List<String> studentStatusList = usList.stream().map(WhutUndergraduateStatus::getXh).collect(Collectors.toList());

        for (MsgCourseGroupChat chat : chatList) {
            List<String> addUserList = new ArrayList<>();

            //课表编码
            String code = chat.getCode();
            //已入群的群聊成员列表
            List<String> joinUserList = chat.getJoinUserList();

            //取出有效的课程成员
            List<WhutUndergraduateCourseStudent> studentList = studentMap.get(code);
            studentList = studentList.stream().filter(item -> studentStatusList.contains(item.getXh())).collect(Collectors.toList());
            //需要加入群聊的成员
            List<String> addStudentList = studentList.stream().map(WhutUndergraduateCourseStudent::getXh).filter(xh -> !joinUserList.contains(xh)).collect(Collectors.toList());
            //取出在企微的成员
            addStudentList = addStudentList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addStudentList)) {
                joinUserList.addAll(addStudentList);
                addUserList.addAll(addStudentList);
            }

            //需要踢出群聊的成员
            List<WhutUndergraduateCourseStudent> finalStudentList = studentList;
            List<String> delUserList = joinUserList.stream().filter(xh -> !finalStudentList.stream().map(WhutUndergraduateCourseStudent::getXh).collect(Collectors.toList()).contains(xh)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delUserList)) {
                joinUserList.removeAll(delUserList);
            }

            List<String> teacherList = chat.getTeacherList();
            //查询教师
            List<WhutUndergraduateCourseTeacher> teacherListList = teacherMap.get(code);
            List<String> newCounselorList = teacherListList.stream().map(WhutUndergraduateCourseTeacher::getSkjsgh).collect(Collectors.toList());
            //需要加入的辅导员
            List<String> addCounselorList = newCounselorList.stream().filter(xh -> !teacherList.contains(xh)).collect(Collectors.toList());
            //取出在企微的辅导员
            addCounselorList = addCounselorList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addCounselorList)) {
                teacherList.addAll(addCounselorList);
                addUserList.addAll(addCounselorList);
            }

            //如果人员有变动则更新群聊
            if (CollUtil.isNotEmpty(addUserList) || CollUtil.isNotEmpty(delUserList) || CollUtil.isNotEmpty(addCounselorList)) {
                MsgCourseChatUpdateUserDTO updateUserDTO = new MsgCourseChatUpdateUserDTO();
                updateUserDTO.setChat(chat);
                updateUserDTO.setAddUserList(addUserList);
                updateUserDTO.setDelUserList(delUserList);
                updateUserDTOList.add(updateUserDTO);
            }
        }
    }

    /**
     * 更新研究生课程群聊
     */
    private void updateMasterCourseChat(List<MsgCourseGroupChat> chatList, List<MsgCourseChatUpdateUserDTO> updateUserDTOList) {
        //查询研究生
        List<WhutMasterCourseStudent> mcsList = whutMasterCourseStudentService.list();
        //按课表编码分组
        Map<String, List<WhutMasterCourseStudent>> studentMap = mcsList.stream().collect(Collectors.groupingBy(WhutMasterCourseStudent::getKbbh, Collectors.toList()));

        //查询教师
        List<WhutMasterCourseTeacher> mctList = whutMasterCourseTeacherService.list();
        //按课表编码分组
        Map<String, List<WhutMasterCourseTeacher>> teacherMap = mctList.stream().collect(Collectors.groupingBy(WhutMasterCourseTeacher::getKbbh, Collectors.toList()));

        //查询研究生状态
        LambdaQueryWrapper<WhutMasterStatus> msQw = Wrappers.lambdaQuery(WhutMasterStatus.class);
        msQw.eq(WhutMasterStatus::getXsdqztm, "01");
        msQw.eq(WhutMasterStatus::getXjztm, "1");
        List<WhutMasterStatus> msList = whutMasterStatusService.list(msQw);
        //有效学生列表
        List<String> studentStatusList = msList.stream().map(WhutMasterStatus::getXh).collect(Collectors.toList());

        for (MsgCourseGroupChat chat : chatList) {
            List<String> addUserList = new ArrayList<>();

            //课程号
            String code = chat.getCode();
            //已入群的群聊成员列表
            List<String> joinUserList = chat.getJoinUserList();

            //取出有效的课程成员
            List<WhutMasterCourseStudent> studentList = studentMap.get(code);
            studentList = studentList.stream().filter(item -> studentStatusList.contains(item.getXh())).collect(Collectors.toList());
            //需要加入群聊的成员
            List<String> addStudentList = studentList.stream().map(WhutMasterCourseStudent::getXh).filter(xh -> !joinUserList.contains(xh)).collect(Collectors.toList());
            //取出在企微的成员
            addStudentList = addStudentList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addStudentList)) {
                joinUserList.addAll(addStudentList);
                addUserList.addAll(addStudentList);
            }

            //需要踢出群聊的成员
            List<WhutMasterCourseStudent> finalStudentList = studentList;
            List<String> delUserList = joinUserList.stream().filter(xh -> !finalStudentList.stream().map(WhutMasterCourseStudent::getXh).collect(Collectors.toList()).contains(xh)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delUserList)) {
                joinUserList.removeAll(delUserList);
            }

            List<String> teacherList = chat.getTeacherList();
            //查询教师
            List<WhutMasterCourseTeacher> teacherListList = teacherMap.get(code);
            List<String> newTeacherList = teacherListList.stream().map(WhutMasterCourseTeacher::getGh).collect(Collectors.toList());
            //需要加入的教师
            List<String> addTeacherList = newTeacherList.stream().filter(xh -> !teacherList.contains(xh)).collect(Collectors.toList());
            //取出在企微的教师
            addTeacherList = addTeacherList.stream().filter(s -> userService.getCacheUserByUserid(s) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addTeacherList)) {
                teacherList.addAll(addTeacherList);
                addUserList.addAll(addTeacherList);
            }

            //如果人员有变动则更新群聊
            if (CollUtil.isNotEmpty(addUserList) || CollUtil.isNotEmpty(delUserList) || CollUtil.isNotEmpty(addTeacherList)) {
                MsgCourseChatUpdateUserDTO updateUserDTO = new MsgCourseChatUpdateUserDTO();
                updateUserDTO.setChat(chat);
                updateUserDTO.setAddUserList(addUserList);
                updateUserDTO.setDelUserList(delUserList);
                updateUserDTOList.add(updateUserDTO);
            }
        }
    }
}
