package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 课程周信息
 */
@Data
@ApiModel(value = "CourseListWeekVO", description = "课程周信息")
public class CourseListWeekVO implements Serializable {

    private static final long serialVersionUID = -147607356455297478L;

    @ApiModelProperty(value = "星期几")
    private String dayOfWeek;

    @ApiModelProperty(value = "课程节次")
    private String lessonNo;

    @ApiModelProperty(value = "授课教室名称")
    private String classroomName;
}
