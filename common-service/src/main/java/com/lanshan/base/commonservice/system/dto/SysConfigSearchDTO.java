package com.lanshan.base.commonservice.system.dto;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@ApiModel("系统配置查询DTO")
@Data
public class SysConfigSearchDTO extends PageQo implements Serializable {

    private static final long serialVersionUID = -357893839794893857L;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**
     * 参数键名
     */
    @ApiModelProperty(value = "配置key")
    private String configKey;

    /**
     * 参数键值
     */
    @ApiModelProperty(value = "配置值")
    private String configValue;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型")
    private String configType;

}
