package com.lanshan.base.commonservice.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.workbench.entity.WbAppFavorite;

import java.util.List;

/**
 * 用户收藏应用信息表(WbAppFavorite)表服务接口
 *
 * <AUTHOR>
 */
public interface WbAppFavoriteService extends IService<WbAppFavorite> {

    /**
     * 排序收藏的应用
     * @param appIdSort 已经排序的appId
     * @return 是否成功
     */
    Boolean sortFavoriteApp(List<Long> appIdSort);
}

