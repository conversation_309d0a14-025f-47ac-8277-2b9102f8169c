package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 部门表操作表(CpDepartmentOperate)实体
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpDepartmentOperate implements Serializable {
    private static final long serialVersionUID = 352822361385172405L;

    @TableId(type = IdType.AUTO)
    private Long tId;

    @ApiModelProperty("部门id")
    private Long id;

    @ApiModelProperty("部门名称，代开发自建应用需要管理员授权才返回；第三方不可获取，需要通过通讯录展示组件来展示部门名称")
    private String name;

    @ApiModelProperty("部门英文名称，代开发自建应用需要管理员授权才返回；第三方不可获取，需要通过通讯录展示组件来展示部门名称")
    @JsonProperty("name_en")
    private String nameEn;

    @ApiModelProperty("父部门id。根部门为1")
    private Long parentid;

    @ApiModelProperty("在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)")
    @TableField(value = "\"order\"")
    private Long order;

    @ApiModelProperty("部门负责人的UserID，返回在应用可见范围内的部门负责人列表；第三方仅通讯录应用或者授权了“组织架构信息-应用可获取企业的部门组织架构信息-部门负责人”的第三方应用可获取")
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonProperty("department_leader")
    private List<String> departmentLeader;

    @ApiModelProperty("操作状态 0：未处理 1：成功 2：失败")
    private Integer operateStatus;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    @TableField(exist = false)
    private int needToProcess;
}

