package com.lanshan.base.commonservice.workbench.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "专区及应用VO")
public class ZoneAndAppVO implements Serializable {
    private static final long serialVersionUID = 1595904630927470792L;

    @ApiModelProperty(value = "专区id")
    private Long zoneId;

    @ApiModelProperty(value = "专区名称")
    private String zoneName;

    @ApiModelProperty(value = "应用列表")
    private List<AppTagVO> appList;
}
