package com.lanshan.apigateway.config.security;

import com.lanshan.base.api.constant.SecurityConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.security.web.server.context.ServerSecurityContextRepository;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * 存储认证授权的相关信息
 *
 * <AUTHOR>
 */
@Component
public class DefaultSecurityContextRepository implements ServerSecurityContextRepository {

    @Resource
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Override
    public Mono<Void> save(ServerWebExchange exchange, SecurityContext context) {
        return Mono.empty();
    }

    @Override
    public Mono<SecurityContext> load(ServerWebExchange exchange) {

        // 从请求头获取token 信息
        String token = exchange.getRequest()
                .getHeaders()
                .getFirst(SecurityConstant.TOKEN_HEADER);

        if (StringUtils.isEmpty(token)) {
            return Mono.empty();
        }

        if (token.startsWith(SecurityConstant.BEARER)) {
            token = token.substring(SecurityConstant.BEARER.length());
        }

        return tokenAuthenticationManager
                .authenticate(new UsernamePasswordAuthenticationToken(token, null))
                .map(SecurityContextImpl::new);
    }
}
