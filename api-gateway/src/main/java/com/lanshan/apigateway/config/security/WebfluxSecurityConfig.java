package com.lanshan.apigateway.config.security;

import com.lanshan.apigateway.config.properties.SecurityBeanConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

import javax.annotation.Resource;

/**
 * webflux security核心配置类
 *
 * <AUTHOR>
 */
@EnableWebFluxSecurity
public class WebfluxSecurityConfig {

    @Resource
    private DefaultSecurityContextRepository defaultSecurityContextRepository;

    @Resource
    private DefaultAuthorizationManager defaultAuthorizationManager;

    @Resource
    private UserDetailsServiceImpl userDetailsServiceImpl;

    @Resource
    private DefaultAuthenticationSuccessHandler defaultAuthenticationSuccessHandler;

    @Resource
    private DefaultAuthenticationFailureHandler defaultAuthenticationFailureHandler;

    @Resource
    private DefaultAuthenticationEntryPoint defaultAuthenticationEntryPoint;

    @Resource
    private DefaultAccessDeniedHandler defaultAccessDeniedHandler;

    @Resource
    private SecurityBeanConfig securityBeanConfig;

//    @Bean
//    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity httpSecurity) {
//
//        httpSecurity
//                // 登录认证处理
//                .securityContextRepository(defaultSecurityContextRepository)
//                // 请求权限拦截处理
//                .authorizeExchange(exchange -> exchange
//                        .pathMatchers(securityBeanConfig.getIgnoreUrls()).permitAll()
//                        .pathMatchers(HttpMethod.OPTIONS).permitAll()
//                        .anyExchange().access(defaultAuthorizationManager)
//                )
//                .formLogin()
//                .authenticationManager(new UserDetailsRepositoryReactiveAuthenticationManager(userDetailsServiceImpl))
//                // 自定义处理
//                .authenticationSuccessHandler(defaultAuthenticationSuccessHandler)
//                .authenticationFailureHandler(defaultAuthenticationFailureHandler)
//                .and()
//                .exceptionHandling()
//                .authenticationEntryPoint(defaultAuthenticationEntryPoint)
//                .and()
//                .exceptionHandling()
//                .accessDeniedHandler(defaultAccessDeniedHandler)
//                .and()
//                .csrf().disable();
//        return httpSecurity.build();
//    }

    @Bean
    public SecurityWebFilterChain securityWebFilterChainTemp(ServerHttpSecurity httpSecurity) {

        httpSecurity
                // 登录认证处理
                .securityContextRepository(defaultSecurityContextRepository)
                // 请求权限拦截处理
                .authorizeExchange(exchange ->
                        exchange.anyExchange().permitAll()
                                .and().csrf().disable()
                );
        return httpSecurity.build();
    }

}
