package com.lanshan.monitor.daemon.service;

import com.lanshan.monitor.daemon.enums.OperateEnum;
import com.lanshan.monitor.daemon.model.vo.AppServerInfoVO;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 监控守护接口类
 */
public interface MonitorDaemonService {

    /**
     * 服务心跳监控
     *
     * @return 应用的服务器信息
     */
    AppServerInfoVO healthCheck(String serviceName);

    /**
     * 执行服务
     *
     * @param extParam    扩展参数
     * @param operateEnum 操作类型
     */
    void execOperate(String extParam, OperateEnum operateEnum);

}
