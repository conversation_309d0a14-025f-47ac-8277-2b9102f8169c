package com.lanshan.monitor.daemon.config;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 主机配置
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "daemon.host")
public class HostConfig {

    private static final String AES_KEY = "V/2@]G}rw9Or(h>h";

    /**
     * ip 地址最好是内网的
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port = 22;

    /**
     * 用户名 默认为 root
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否是本地
     */
    private boolean local = false;

    public String getPassword() {
        if (CharSequenceUtil.isBlank(password)) {
            return password;
        }
        return SecureUtil.aes(AES_KEY.getBytes(StandardCharsets.UTF_8)).decryptStr(password, StandardCharsets.UTF_8);
    }

}
