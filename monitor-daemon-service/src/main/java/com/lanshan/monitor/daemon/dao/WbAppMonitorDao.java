package com.lanshan.monitor.daemon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.monitor.daemon.entity.WbAppMonitor;
import com.lanshan.monitor.daemon.model.dto.AppMonitorSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用监控(WbAppMonitor)表数据库访问层
 *
 * <AUTHOR>
 */
public interface WbAppMonitorDao extends BaseMapper<WbAppMonitor> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WbAppMonitor> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WbAppMonitor> entities);

    /**
     * 根据条件分页查询应用监控
     *
     * @param page      分页对象
     * @param searchDTO 查询参数
     * @return IPage<WbAppMonitor> 分页对象
     */
    IPage<WbAppMonitor> pageByParam(@Param("page") IPage<WbAppMonitor> page, @Param("param") AppMonitorSearchDTO searchDTO);
}

