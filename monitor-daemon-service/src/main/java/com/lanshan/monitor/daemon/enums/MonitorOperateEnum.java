package com.lanshan.monitor.daemon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2024/1/23 15:55
 */
@AllArgsConstructor
@Getter
public enum MonitorOperateEnum {

    START(1, "启动"),
    RESTART(2, "重启"),
    STOP(3, "停止"),
    ;

    private final int code;

    private final String desc;

    public static MonitorOperateEnum getEnum(int code) {
        for (MonitorOperateEnum e : MonitorOperateEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }

}
