<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.monitor.daemon.dao.WbAppMonitorDao">

    <resultMap type="com.lanshan.monitor.daemon.entity.WbAppMonitor" id="WbAppMonitorMap">
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="healthyUrl" column="healthy_url" jdbcType="VARCHAR"/>
        <result property="restartUrl" column="restart_url" jdbcType="VARCHAR"/>
        <result property="startUrl" column="start_url" jdbcType="VARCHAR"/>
        <result property="stopUrl" column="stop_url" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="extParam" column="ext_param" jdbcType="VARCHAR"/>
        <result property="supportType" column="support_type" jdbcType="VARCHAR"/>
        <result property="host" column="host" jdbcType="VARCHAR"/>
        <result property="userList" column="user_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="departmentList" column="department_list" typeHandler="com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler"/>
        <result property="tagList" column="tag_list" typeHandler="com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_monitor(app_id, app_name, healthy_url, restart_url, start_url, stop_url, start_time, end_time, ext_param, support_type, host, user_list, department_list, tag_list)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId} , #{entity.appName} , #{entity.healthyUrl} , #{entity.restartUrl} , #{entity.startUrl} ,
             #{entity.stopUrl} , #{entity.startTime} , #{entity.endTime} , #{entity.extParam} , #{entity.supportType},
             #{entity.host} , #{entity.userList} , #{entity.departmentList} , #{entity.tagList})
        </foreach>
    </insert>
    <select id="pageByParam" resultType="com.lanshan.monitor.daemon.entity.WbAppMonitor">
        SELECT
            wa.id AS appId,
            wa.app_name AS appName,
            wbm.healthy_url AS healthyUrl,
            wbm.restart_url AS restartUrl,
            wbm.start_url AS startUrl,
            wbm.stop_url AS stopUrl,
            wbm.start_time AS startTime,
            wbm.end_time AS endTime,
            wbm.support_type AS supportType,
            wbm.host AS host,
            wbm.user_list AS userList,
            wbm.department_list AS departmentList,
            wbm.tag_list AS tagList
        FROM wb_app_monitor wbm LEFT JOIN wb_app wa ON wa.id = wbm.app_id
        <where>
            <if test="param.appName != null and param.appName != ''">
                AND wa.app_name LIKE '%'|#{param.appName}|'%'
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND wbm.start_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND wbm.end_time &lt;= #{param.endTime}
            </if>
        </where>
        ORDER BY wa.id DESC
    </select>

</mapper>

