<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>
    <property name="LOG_HOME" value="./data/app/logs"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{25}:%line %msg%n</pattern>
            <!--            <pattern>%yellow(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level)  |%green(%logger:%line) |%black(%msg%n)</pattern>-->
            <pattern>%yellow(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) |%highlight(%-5level) |%boldMagenta(%logger{36}) |:%gray(%msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{25}:%line %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="asyncFile" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="rollingFile"/>
    </appender>

    <!-- MyBatis log configure -->
    <logger name="com.apache.ibatis" level="INFO"/>
    <logger name="org.mybatis.spring" level="DEBUG"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <logger name="com.lanshan" level="DEBUG"/>
    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="stdout"/>
            <appender-ref ref="rollingFile"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="info">
            <appender-ref ref="asyncFile"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="info">
            <appender-ref ref="asyncFile"/>
        </root>
    </springProfile>

</configuration>