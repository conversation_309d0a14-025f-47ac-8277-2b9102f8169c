package com.lanshan.base.auth.controller;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 身份认证Web服务控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ServiceConstant.BASE_AUTHENTICATION)
@Api(tags = "授权API服务", hidden = true)
public class AuthController {

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @ApiOperation("获取访问用户身份")
    @GetMapping("/authGetUserInfo")
    public Result<WxCpOauth2UserInfo> authGetUserInfo(@RequestParam String corpId,
                                                      @RequestParam String agentId,
                                                      @RequestParam String code) throws WxErrorException {
        //TODO SDK中的URL地址错误，需要重新打包SDK
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getOauth2Service().getUserInfo(code));
    }

    @ApiOperation("获取访问用户敏感信息")
    @PostMapping(value = "/authGetUserDetail")
    public Result<WxCpUserDetail> authGetUserDetail(@RequestParam String corpId,
                                                    @RequestParam String agentId,
                                                    @RequestParam String userTicket) throws WxErrorException {
        //TODO SDK中的URL地址错误，需要重新打包SDK
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getOauth2Service().getUserDetail(userTicket));
    }

    @ApiOperation("获取企业的jsapi_ticket")
    @GetMapping(value = "getJsapiTicket")
    public Result<String> getJsapiTicket(@RequestParam String corpId,
                                         @RequestParam String agentId) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getJsapiTicket());
    }

    @ApiOperation("获取应用的jsapi_ticket")
    @GetMapping(value = "getTicket")
    public Result<String> getTicket(@RequestParam String corpId,
                                    @RequestParam String agentId) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getAgentJsapiTicket());
    }

    @ApiOperation("获取微信授权url")
    @PostMapping(value = "getAuthorizationUrl")
    public Result<String> getAuthorizationUrl(String corpId, String agentId, String url, String state, String scope) {
        // 调用企业微信接口，获取用户信息
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getOauth2Service().buildAuthorizationUrl(url, state, scope));
    }
}
