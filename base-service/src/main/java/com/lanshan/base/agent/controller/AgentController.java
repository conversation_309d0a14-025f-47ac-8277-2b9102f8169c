package com.lanshan.base.agent.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 应用Web服务控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ServiceConstant.BASE_APPLICATION)
@Api(tags = "应用管理接口", hidden = true)
@RequiredArgsConstructor
public class AgentController {

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @ApiOperation("获取应用基本信息")
    @GetMapping("/getAppInfo")
    public Result<WxCpAgent> getAppInfo(@RequestParam String corpId,
                                        @RequestParam String agentId) throws WxErrorException {

        if (!StringUtils.isNumeric(agentId)) {
            Result.build().error("不支持此类型的应用查询！");
        }
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return Result.build(wxCpService.getAgentService().get(Integer.valueOf(agentId)));
    }

    @ApiOperation("设置应用")
    @PostMapping("/setAgent")
    public Result<String> setAgent(@RequestParam String corpId,
                                   @RequestParam String agentId,
                                   @RequestBody WxCpAgent agent) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        wxCpService.getAgentService().set(agent);
        return Result.build();
    }

    @ApiOperation("获取应用的AccessToken")
    @PostMapping("/getAccessToken")
    public Result<String> getAccessToken(@RequestParam String corpId,
                                   @RequestParam String agentId) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        return new Result<String>().setData(wxCpService.getAccessToken());
    }

    @ApiOperation("设置应用展示模板")
    @PostMapping("/setAgentTemplate")
    public Result<String> setAgentTemplate(@RequestParam String corpId,
                                   @RequestParam String agentId,
                                   @RequestBody WxCpAgentWorkBench agentWorkBench) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        wxCpService.getWorkBenchService().setWorkBenchTemplate(agentWorkBench);
        return Result.build();
    }

    @ApiOperation("获取应用展示模板")
    @PostMapping("/getAgentTemplate")
    public Result<JSONObject> getAgentTemplate(@RequestParam String corpId,
                                               @RequestParam String agentId) throws WxErrorException {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        String workBenchTemplate = wxCpService.getWorkBenchService().getWorkBenchTemplate(Long.valueOf(agentId));
        return Result.build(JSON.parseObject(workBenchTemplate));
    }
}
