server:
  port: 8210
  servlet:
    context-path: /
  shutdown: graceful #开启优雅停机

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: base-service
  lifecycle:
    timeout-per-shutdown-phase: 20s #优雅停机设置缓冲时间 默认30s(在规定时间内如果线程无法执行完毕则会被强制停机)
  #环境 dev|test|prod|demo
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages_common
  cloud:
    nacos:
      discovery:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:************}:${Nacos_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:9e1db6d0-61cf-40d2-9cc3-978a519f0ecf}
        group: ${NACOS_GROUP:DEV_GROUP}
        service: ${spring.application.name}
        #        ip: @nacos.discovery.ip@
        metadata:
          spring.profiles.active: ${spring.profiles.active}
      config:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:************}:${Nacos_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:9e1db6d0-61cf-40d2-9cc3-978a519f0ecf}
        group: ${NACOS_GROUP:DEV_GROUP}
        file-extension: yaml
        # 共享配置
        shared-configs:
          - data-id: base-start-config.yaml
            group: ${spring.cloud.nacos.config.group}
            refresh: true
#          - data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
#            group: ${spring.cloud.nacos.config.group}
#            refresh: true
#          - data-id: xxl-job-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
#            group: ${spring.cloud.nacos.config.group}
        # 私有配置
#        extension-configs:
#          - data-id: base-start-config
#            group: ${spring.cloud.nacos.config.group}
#            refresh: true
tencent:
  cloud:
    secret-id: ${TENCENT_CLOUD_SECRET_ID:AKIDfYGm4Hgx3xw31scBsfTBpHYfbR7cyAFA}
    secret-key: ${TENCENT_CLOUD_SECRET_KEY:oQtEbt0ykIbt30jFcrrHXdWF1hFdt174}
    region: ${TENCENT_CLOUD_REGION:ap-shanghai}
    merchant-id: ${TENCENT_CLOUD_MERCHANT_ID:0NSJ2407080949183942}
    private-key-hex: ${TENCENT_CLOUD_PRIVATE_KEY_HEX:be8fd7d765c0d86ea7f1496c5a048d4a77e051e363f99edee304e6501b8a0974}
