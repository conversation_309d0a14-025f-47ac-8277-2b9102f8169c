<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lanshan</groupId>
    <artifactId>capability-platform-base</artifactId>
    <version>0.5</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.17</version>
        <relativePath/>
    </parent>

    <modules>
        <module>base-api</module>
        <module>base-sdk</module>
        <module>base-service</module>
        <module>base-starter</module>
        <module>common-service</module>
        <module>api-gateway</module>
        <module>app-service</module>
        <module>monitor-daemon-service</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <capability-platform-base.version>0.5</capability-platform-base.version>
        <postgresql.driver.version>42.6.0</postgresql.driver.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-boot.version>2.7.17</spring-boot.version>
        <spring.cloud.alibaba.version>2021.1</spring.cloud.alibaba.version>
        <guava.version>32.1.2-jre</guava.version>
        <knife4j.version>2.0.9</knife4j.version>
        <spring.boot.admin.version>2.3.0</spring.boot.admin.version>
        <captcha.version>1.6.2</captcha.version>
        <druid.version>1.2.16</druid.version>
        <mybatisplus.version>3.5.4</mybatisplus.version>
        <mybatisplus.generator.version>3.5.4</mybatisplus.generator.version>
        <velocity.version>2.2</velocity.version>
        <dynamic-datasource-starter>3.4.0</dynamic-datasource-starter>
        <mysql.version>8.0.21</mysql.version>
        <sqlserver.version>4.0</sqlserver.version>
        <oracle.version>11.2.0.3</oracle.version>
        <hibernate.validator.version>6.0.19.Final</hibernate.validator.version>
        <commons.lang.version>3.7</commons.lang.version>
        <commons.fileupload.version>1.3.3</commons.fileupload.version>
        <commons.io.version>2.11.0</commons.io.version>
        <hutool.all.version>5.8.24</hutool.all.version>
        <hutool.http.version>5.8.24</hutool.http.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <joda.time.version>2.9.9</joda.time.version>
        <fastjson.version>2.0.39</fastjson.version>
        <transmittable-thread-local.version>2.2.0</transmittable-thread-local.version>
        <gson.version>2.8.6</gson.version>
        <jsoup.version>1.11.3</jsoup.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <quartz.version>2.3.0</quartz.version>
        <aliyun.core.version>4.5.16</aliyun.core.version>
        <aliyun.dysmsapi.version>1.1.0</aliyun.dysmsapi.version>
        <aliyun-dysmsapiv2.version>3.1.1</aliyun-dysmsapiv2.version>
        <qcloud.qcloudsms.version>1.0.5</qcloud.qcloudsms.version>
        <qiniu.version>7.2.27</qiniu.version>
        <aliyun.oss.version>3.12.0</aliyun.oss.version>
        <qcloud.cos.version>5.4.4</qcloud.cos.version>
        <fastdfs.version>1.26.2</fastdfs.version>
        <minio.version>8.2.2</minio.version>
        <freemarker.version>2.3.29</freemarker.version>
        <mail.version>1.6.2</mail.version>
        <ureport2.version>2.2.9</ureport2.version>
        <IJPay.version>2.7.1</IJPay.version>
        <redisson.version>3.14.1</redisson.version>
        <jpush.version>3.4.8</jpush.version>
        <jsr310.version>1.0.1</jsr310.version>
        <zxing.version>3.3.3</zxing.version>
        <justauth.version>1.16.1</justauth.version>
        <justauth.starter.version>1.4.0</justauth.starter.version>
        <okhttp.version>4.9.1</okhttp.version>
        <anjiplus.version>1.2.9</anjiplus.version>
        <geodesy.version>1.1.3</geodesy.version>
        <satoken.version>1.37.0</satoken.version>
        <poi-tl.version>1.12.0</poi-tl.version>
        <docker-java.version>3.0.14</docker-java.version>
        <javax.ws.rs-api.version>2.1.1</javax.ws.rs-api.version>
        <jersey-hk2.version>2.26</jersey-hk2.version>
        <jsr311-api.version>1.1.1</jsr311-api.version>
        <ip2region.version>2.6.6</ip2region.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.4.Final</mapstruct.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <swagger-annotations.version>1.6.11</swagger-annotations.version>
        <wx-java.version>4.6.4</wx-java.version>
        <java.jwt.version>3.4.0</java.jwt.version>
        <jsonwebtoken.jjwt.api.version>0.11.5</jsonwebtoken.jjwt.api.version>
        <javax.validation.version>2.0.1.Final</javax.validation.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <easyexcel-core.version>3.1.1</easyexcel-core.version>
        <retrofit.version>2.7.2</retrofit.version>
        <rxjava.version>3.1.8</rxjava.version>
        <influxdb-client-java.version>6.10.0</influxdb-client-java.version>
        <jackson-databind.version>2.13.5</jackson-databind.version>
        <poi.version>4.1.2</poi.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <sms4j-spring-boot-starter.version>3.0.4</sms4j-spring-boot-starter.version>
        <hanlp.version>portable-1.8.4</hanlp.version>
        <commons-imaging.version>1.0-alpha3</commons-imaging.version>
        <logstash-logback-encoder.version>4.11</logstash-logback-encoder.version>
        <easy-es.version>2.0.0</easy-es.version>
        <spring-data-elasticsearch.version>4.3.0</spring-data-elasticsearch.version>
        <weixin-java-miniapp.version>4.6.0</weixin-java-miniapp.version>
        <cas-client-core.version>3.2.1</cas-client-core.version>
        <tencentcloud-sdk-java.version>3.1.1000</tencentcloud-sdk-java.version>
        <lzjtu-ds.version>1.0.0</lzjtu-ds.version>
        <magic-api-spring-boot-starter.version>2.1.1</magic-api-spring-boot-starter.version>
        <jaxws-api.version>2.3.1</jaxws-api.version>
        <jaxws-rt.version>2.3.5</jaxws-rt.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <jaxb-runtime.version>2.3.5</jaxb-runtime.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.lanshan</groupId>
                <artifactId>base-api</artifactId>
                <version>${capability-platform-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lanshan</groupId>
                <artifactId>base-sdk</artifactId>
                <version>${capability-platform-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lanshan</groupId>
                <artifactId>base-starter</artifactId>
                <version>${capability-platform-base.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jpush.api</groupId>
                <artifactId>jpush-client</artifactId>
                <version>${jpush.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${captcha.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatisplus.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-starter}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.all.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.http.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring.boot.admin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun.dysmsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun-dysmsapiv2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.qcloudsms</groupId>
                <artifactId>qcloudsms</artifactId>
                <version>${qcloud.qcloudsms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${qcloud.cos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${fastdfs.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${mail.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bstek.ureport</groupId>
                <artifactId>ureport2-console</artifactId>
                <version>${ureport2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-AliPay</artifactId>
                <version>${IJPay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>${jsr310.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xkcoding.justauth</groupId>
                <artifactId>justauth-spring-boot-starter</artifactId>
                <version>${justauth.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>spring-boot-starter-captcha</artifactId>
                <version>${anjiplus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.gavaghan</groupId>
                <artifactId>geodesy</artifactId>
                <version>${geodesy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.docker-java</groupId>
                <artifactId>docker-java</artifactId>
                <version>${docker-java.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>javax.ws.rs-api</artifactId>
                <version>${javax.ws.rs-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-hk2</artifactId>
                <version>${jersey-hk2.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>jsr311-api</artifactId>
                <version>${jsr311-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${wx-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin-java-miniapp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${weixin-java-miniapp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${weixin-java-miniapp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java.jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jsonwebtoken.jjwt.api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jsonwebtoken.jjwt.api.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/javax.validation/validation-api -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jsonwebtoken.jjwt.api.version}</version>
            </dependency>
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel-core</artifactId>
                <version>${easyexcel-core.version}</version>
            </dependency>
            <!-- pagehelper-spring-boot-starter -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.influxdb/influxdb-client-java -->
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb-client-java.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/io.reactivex.rxjava3/rxjava -->
            <dependency>
                <groupId>io.reactivex.rxjava3</groupId>
                <artifactId>rxjava</artifactId>
                <version>${rxjava.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.squareup.retrofit2/retrofit -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-elasticsearch -->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-elasticsearch</artifactId>
                <version>${spring-data-elasticsearch.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.dromara.sms4j/sms4j-spring-boot-starter -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-Email-core</artifactId>
                <version>${sms4j-spring-boot-starter.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.hankcs/hanlp -->
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>${hanlp.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-imaging -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-imaging</artifactId>
                <version>${commons-imaging.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/net.logstash.logback/logstash-logback-encoder -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.14.0</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>7.14.0</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.14.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>6.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
            <dependency>
                <groupId>org.jasig.cas.client</groupId>
                <artifactId>cas-client-core</artifactId>
                <version>${cas-client-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.synjones</groupId>
                <artifactId>cop-sdk-common</artifactId>
                <version>2.0.7-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.edu.lzjtu</groupId>
                <artifactId>lzjtu-ds</artifactId>
                <version>${lzjtu-ds.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.ssssssss</groupId>
                <artifactId>magic-api-spring-boot-starter</artifactId>
                <version>${magic-api-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ssssssss</groupId>
                <artifactId>magic-api-plugin-swagger</artifactId>
                <version>${magic-api-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ssssssss</groupId>
                <artifactId>magic-api-plugin-redis</artifactId>
                <version>${magic-api-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ssssssss</groupId>
                <artifactId>magic-api-plugin-task</artifactId>
                <version>${magic-api-spring-boot-starter.version}</version>
            </dependency>

            <!-- xml解析 -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.ws</groupId>
                <artifactId>jaxws-api</artifactId>
                <version>${jaxws-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-rt</artifactId>
                <version>${jaxws-rt.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb-runtime.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>tencent</id>
            <name>tencent maven mirror</name>
            <url>https://mirrors.tencent.com/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>lanshan</id>
            <name>lanshan maven mirror</name>
            <url>http://lskj.lskejisoft.com:2344/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>json</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>csv</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xdb</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--deploy工程到私有库-->
    <distributionManagement>
        <repository>
            <id>dev-releases</id>
            <url>http://192.168.3.37:8999/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dev-snapshots</id>
            <url>http://192.168.3.37:8999/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
