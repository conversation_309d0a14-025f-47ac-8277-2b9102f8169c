package com.lanshan.base.starter.imaginary;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description imaginary 配置类
 */
@ConditionalOnProperty(prefix = "imaginary", name = {"url"})
@Configuration
@ConfigurationProperties(prefix = "imaginary")
public class ImaginaryConfig {

    /**
     * imaginary 服务地址
     */
    private String url;

    @Bean
    public ImaginaryClient imaginaryClient() {
        return new ImaginaryClient(url);
    }
}
