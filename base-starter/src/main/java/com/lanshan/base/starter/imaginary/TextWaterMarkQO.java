package com.lanshan.base.starter.imaginary;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 文字水印请求参数
 * @date 2023/12/12 10:12
 */
public class TextWaterMarkQO {

    /**
     * 水印文字
     */
    private String text;

    /**
     * 水印位置
     */
    private int margin;

    /**
     * 水印分辨率
     */
    private int dpi;

    /**
     * 文字宽度
     */
    private int textwidth;

    /**
     * 透明度
     */
    private float opacity;

    /**
     * 不重复水印
     */
    private boolean noreplicatenoreplicate = false;

    /**
     * 字体
     */
    private String font;

    /**
     * 字体颜色
     */
    private String color;

    /**
     * 质量 JPEG 时有用
     */
    private int quality;

    /**
     * 压缩率 png 时有用
     */
    private int compression;

    /**
     * 类型
     */
    private String type;

    /**
     * 文件 需为 GET 方法，且 -mount 标志存在
     */
    private String file;

    /**
     * 图片地址 需为 GET 方法，且 -enable-url-source 标识存在
     */
    private String url;

    /**
     * 嵌入水印
     */
    private boolean embed = false;

    /**
     * 强制覆盖
     */
    private boolean force = false;

    /**
     * 旋转角度
     */
    private int rotate;

    /**
     * 不旋转
     */
    private boolean norotation = false;

    /**
     * 不使用配置文件
     */
    private boolean noprofile  = false;

    /**
     * 去除元数据
     */
    private boolean stripmeta = false;

    /**
     * 翻转
     */
    private boolean flip = false;

    /**
     * 旋转
     */
    private boolean flop = false;

    /**
     * 扩展
     */
    private String extend;

    /**
     * 背景颜色
     */
    private String background;

    /**
     * 颜色空间
     */
    private String colorspace;

    /**
     * 高斯模糊
     */
    private float sigma;

    /**
     * 最小亮度
     */
    private float minampl;

    /**
     * 字段 需为 POST 请求类型为 multipart/form
     */
    private String field;

    /**
     * 隔行扫描
     */
    private boolean interlace = false;

    /**
     * 调色板
     */
    private boolean palette = false;









}
