package com.lanshan.base.starter.wxcpsdk.handler;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.lanshan.base.starter.wxcpsdk.event.SysApprovalChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 企微系统审批变更事件处理器.
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SysApprovalChangeHandler extends AbstractHandler {

    private final ApplicationContext applicationContext;

    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxCpService cpService,
                                    WxSessionManager sessionManager) {
        String content = "收到审批状态变更事件，内容：" + JacksonUtils.toJson(wxMessage);
        this.logger.info(content);

        // 发送通讯录变更回调消息
        applicationContext.publishEvent(new SysApprovalChangeEvent(wxMessage));

        return WxCpXmlOutMessage.TEXT()
                .content(content)
                .fromUser(wxMessage.getToUserName())
                .toUser(wxMessage.getFromUserName())
                .build();
    }
}
