package com.lanshan.base.starter.redis.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 信号量类型
 */
public enum SemaphoreEnum {
    ,
    ;

    /**
     * 特征值
     */
    @JsonValue
    private String value;
    /**
     * 描述
     */
    private String description;

    SemaphoreEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static final Map<String, SemaphoreEnum> VALUES = new HashMap<>();

    static {
        for (final SemaphoreEnum semaphoreEnum : SemaphoreEnum.values()) {
            SemaphoreEnum.VALUES.put(semaphoreEnum.value(), semaphoreEnum);
        }
    }

    @JsonCreator
    public static SemaphoreEnum getByValue(String value) {
        return SemaphoreEnum.VALUES.get(value);
    }

    public String value() {
        return this.value;
    }

    public String description() {
        return this.description;
    }
}
