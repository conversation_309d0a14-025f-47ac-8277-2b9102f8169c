package com.lanshan.base.starter.wxcpsdk.handler;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.lanshan.base.starter.rabbitmq.properties.CallbackMQProperties;
import com.lanshan.base.starter.rabbitmq.service.MqMessageSender;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 通讯录变更事件处理器.
 *
 * <AUTHOR>
 */
@Component
public class ContactChangeHandler extends AbstractHandler {

    @Resource
    private MqMessageSender rabbitmqMessageService;

    @Resource
    private CallbackMQProperties callbackMQProperties;

    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxCpService cpService,
                                    WxSessionManager sessionManager) {
        String content = "收到通讯录变更事件，内容：" + JacksonUtils.toJson(wxMessage);
        this.logger.info(content);

        // 发送通讯录变更回调消息
        rabbitmqMessageService.sendMessage(callbackMQProperties.getExchange()
                , callbackMQProperties.getAddressBook().getRoutingKey(), wxMessage);

        return WxCpXmlOutMessage.TEXT()
                .content(content)
                .fromUser(wxMessage.getToUserName())
                .toUser(wxMessage.getFromUserName())
                .build();
    }
}
