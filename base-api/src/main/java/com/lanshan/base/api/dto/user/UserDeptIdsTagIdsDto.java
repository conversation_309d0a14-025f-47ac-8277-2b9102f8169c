package com.lanshan.base.api.dto.user;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "用户id获取部门ids、标签ids")
public class UserDeptIdsTagIdsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "部门id列表")
    private List<Long> departmentIds;

    @ApiModelProperty(value = "标签id列表")
    private List<Long> tagIds;

}
