package com.lanshan.base.api.feign.agent.fallback;

import com.lanshan.base.api.feign.agent.AgentConfigFeign;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.appconfig.AgentConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Slf4j
@Component
public class AgentConfigFeignFallbackFactory implements FallbackFactory<AgentConfigFeign> {


    @Override
    public AgentConfigFeign create(Throwable cause) {
        log.error("{0}", cause);
        return new AgentConfigFeign() {
            @Override
            public Result<AgentConfigVO> selectOne(Serializable id) {
                return null;
            }

            @Override
            public Result<Long> saveOrUpdate(AgentConfigVO vo) {
                return null;
            }
        };
    }
}
