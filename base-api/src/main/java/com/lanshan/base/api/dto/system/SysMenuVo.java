package com.lanshan.base.api.dto.system;


import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 系统菜单表(SysMenu)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "系统菜单表VO")
@Data
@ToString
public class SysMenuVo implements Serializable {

    private static final long serialVersionUID = 3485796144390831380L;
    @ApiModelProperty(value = "菜单ID")
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    @Size(min = 1, max = 50, message = "名称长度1-50个字符")
    @NotNull(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "父菜单ID")
    private Long parentId;

    @ApiModelProperty(value = "显示顺序")
    private Integer sort;

    @ApiModelProperty(value = "路由地址")
    private String path;

    @ApiModelProperty(value = "组件路径")
    private String component;

    @ApiModelProperty(value = "菜单类型（M目录 C菜单 F按钮）")
    private String menuType;

    @ApiModelProperty(value = "权限标识")
    private String perms;

    @ApiModelProperty(value = "菜单其他属性")
    private JSONObject meta;

    @ApiModelProperty("子节点")
    private List<SysMenuVo> children;
}

