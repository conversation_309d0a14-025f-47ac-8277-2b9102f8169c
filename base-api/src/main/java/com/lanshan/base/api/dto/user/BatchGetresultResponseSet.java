package com.lanshan.base.api.dto.user;



import com.fasterxml.jackson.databind.node.ArrayNode;
import com.lanshan.base.api.dto.ResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "获取异步任务结果 batch/getresult")
public class BatchGetresultResponseSet extends ResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务状态，整型，1表示任务开始，2表示任务进行中，3表示任务已完成")
    private Integer status;
    @ApiModelProperty(value = "操作类型，字节串，目前分别有：1. sync_user(增量更新成员) 2. replace_user(全量覆盖成员)3. replace_party(全量覆盖部门)")
    private String type;
    @ApiModelProperty(value = "任务运行总条数")
    private Integer total;
    @ApiModelProperty(value = "目前运行百分比，当任务完成时为100")
    private Integer percentage;
    @ApiModelProperty(value = "详细的处理结果，具体格式参考下面说明。当任务完成后此字段有效")
    private ArrayNode result;

    /*result结构：type为sync_user、replace_user时：
    "result": [
        {
            "userid":"lisi",//成员UserID。对应管理端的账号
            "errcode":0,//该成员对应操作的结果错误码
            "errmsg":"ok"//错误信息，例如无权限错误，键值冲突，格式错误等
        },
        {
            "userid":"zhangsan",
            "errcode":0,
            "errmsg":"ok"
        }
    ]

    result结构：type为replace_party时：
    "result": [
        {
            "action":1,//操作类型（按位或）：1 新建部门 ，2 更改部门名称， 4 移动部门， 8 修改部门排序
            "partyid":1,//部门ID
            "errcode":0,//该部门对应操作的结果错误码
            "errmsg":"ok"//错误信息，例如无权限错误，键值冲突，格式错误等
        },
        {
            "action":4,
            "partyid":2,
            "errcode":0,
            "errmsg":"ok"
        }
    ]*/
}
