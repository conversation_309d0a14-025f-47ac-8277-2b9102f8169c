package com.lanshan.base.api.qo.schedule;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "日程qo")
public class ScheduleSaveQo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 日程ID。创建日程时返回的ID
     */
    private String scheduleId;
    /**
     * 日程参与者列表。最多支持2000人
     */
    private List<Attendee> attendees;
    /**
     * 日程所属日历ID。该日历必须是access_token所对应应用所创建的日历。 注意，这个日历必须是属于组织者(organizer)的日历；
     * 如果不填，那么插入到组织者的默认日历上。 第三方应用必须指定cal_id 不多于64字节
     */
    private String calId;
    /**
     * 日程描述 不多于512个字符
     */
    private String description;
    /**
     * 日程结束时间，Unix时间戳
     */
    private Long endTime;
    /**
     * 日程地址 不多于128个字符
     */
    private String location;
    /**
     * 组织者 不多于64字节
     */
    private String organizer;
    /**
     * 提醒相关信息
     */
    private Reminders reminders;
    /**
     * 日程开始时间，Unix时间戳
     */
    private Long startTime;
    /**
     * 日程标题。0 ~ 128 字符。不填会默认显示为“新建事件”
     */
    private String summary;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attendee implements Serializable{
        private static final long serialVersionUID = 1L;
        /**
         * 日程参与者ID 不多于64字节
         */
        private String userid;
    }


    /**
     * 提醒相关信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Reminders implements Serializable{
        private static final long serialVersionUID = 1L;
        /**
         * 是否自定义重复。0-否；1-是
         */
        private Integer isCustomRepeat;
        /**
         * 是否需要提醒。0-否；1-是
         */
        private Integer isRemind;
        /**
         * 是否重复日程。0-否；1-是
         */
        private Integer isRepeat;

        /**
         * 日程开始（start_time）前多少秒提醒，当is_remind为1时有效。 例如： 300表示日程开始前5分钟提醒。目前仅支持以下数值： 0 - 事件开始时 300 -
         * 事件开始前5分钟 900 - 事件开始前15分钟 3600 - 事件开始前1小时 86400 - 事件开始前1天
         */
        private List<Integer> remindTimeDiffs;
        /**
         * 每月哪几天重复 仅当指定为自定义重复且重复类型为每月时有效 取值范围：1 ~ 31，分别表示1~31号
         */
        private List<Integer> repeatDayOfMonth;
        /**
         * 每周周几重复 仅当指定为自定义重复且重复类型为每周时有效 取值范围：1 ~ 7，分别表示周一至周日
         */
        private List<Integer> repeatDayOfWeek;
        /**
         * 重复间隔 仅当指定为自定义重复时有效 该字段随repeat_type不同而含义不同 例如：
         * repeat_interval指定为3，repeat_type指定为每周重复，那么每3周重复一次；
         * repeat_interval指定为3，repeat_type指定为每月重复，那么每3个月重复一次
         */
        private Integer repeatInterval;
        /**
         * 重复类型，当is_repeat为1时有效。目前支持如下类型： 0 - 每日 1 - 每周 2 - 每月 5 - 每年 7 - 工作日
         */
        private Integer repeatType;
        /**
         * 重复结束时刻，Unix时间戳。不填或填0表示一直重复
         */
        private Long repeatUntil;
        /**
         * 时区。UTC偏移量表示(即偏离零时区的小时数)，东区为正数，西区为负数。 例如：+8 表示北京时间东八区 默认为北京时间东八区 取值范围：-12 ~ +12
         */
        private Integer timezone;
    }
}
