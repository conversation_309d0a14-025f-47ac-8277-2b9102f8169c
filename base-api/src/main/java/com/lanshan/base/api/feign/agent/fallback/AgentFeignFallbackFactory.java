package com.lanshan.base.api.feign.agent.fallback;

import com.alibaba.fastjson2.JSONObject;
import com.lanshan.base.api.feign.agent.AgentFeign;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 企业微信应用服务异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgentFeignFallbackFactory implements FallbackFactory<AgentFeign> {
    @Override
    public AgentFeign create(Throwable cause) {
        log.error("{0}", cause);
        return new AgentFeign() {
            @Override
            public Result<WxCpAgent> getAppInfo(String corpId, String agentId) {
                return Result.build(new WxCpAgent()).error();
            }

            @Override
            public Result<String> setAgent(String corpId, String agentId, WxCpAgent agent) {
                return new Result<String>().error();
            }

            @Override
            public Result<String> setAgentTemplate(String corpId, String agentId, WxCpAgentWorkBench agentWorkBench) {
                return null;
            }

            @Override
            public Result<JSONObject> getAgentTemplate(String corpId, String agentId) {
                return null;
            }

            @Override
            public Result<String> getAccessToken(String corpId, String agentId) {
                return null;
            }
        };
    }
}
