package com.lanshan.base.api.qo.auth;

import com.lanshan.base.api.qo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 跳转的域名须完全匹配access_token对应应用的可信域名，否则会返回50001错误。
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "获取访问用户敏感信息")
public class AuthGetuserdetailQo extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "成员票据")
    private String user_ticket;
}
