package com.lanshan.base.api.dto.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "标签新增/移除成员结果Dto")
public class TagAddOrRemoveUsersDto implements Serializable {

    private static final long serialVersionUID = -7635323390460574115L;

    @ApiModelProperty(value = "错误码")
    private Integer errCode;

    @ApiModelProperty(value = "错误信息")
    private String errMsg;

    @ApiModelProperty(value = "非法的成员账号列表")
    @JsonProperty("invalidlist")
    private String invalidUsers;

    @ApiModelProperty(value = "非法的部门id列表")
    @JsonProperty("invalidparty")
    private String[] invalidParty;
}
