package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.qo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * 仅自建应用可调用。
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "获取互联企业部门成员")
public class LinkedcorpUserSimplelistQo extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "该字段用的是互联应用可见范围接口返回的department_ids参数，用的是 linkedid + ’/‘ + department_id 拼成的字符串")
    private String department_id;
}
