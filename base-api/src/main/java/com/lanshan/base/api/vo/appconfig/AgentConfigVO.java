package com.lanshan.base.api.vo.appconfig;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.io.Serializable;

/**
 * 应用配置表(AgentConfig)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "应用配置表VO")
@Data
@ToString
@SuperBuilder
public class AgentConfigVO implements Serializable{
    private static final long serialVersionUID = 7781854479858460414L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "企业ID")
    private String corpId;

    @ApiModelProperty(value = "应用ID")
    private Integer agentId;

    @ApiModelProperty(value = "请求密钥")
    private String secret;

    @ApiModelProperty(value = "回调token")
    private String token;

    @ApiModelProperty(value = "回调aeskey")
    private String aeskey;

    @ApiModelProperty(value = "应用类型。self:自建应用；third:第三方；addressbook官方通讯录；详情见枚举类。")
    private String agentType;
}

