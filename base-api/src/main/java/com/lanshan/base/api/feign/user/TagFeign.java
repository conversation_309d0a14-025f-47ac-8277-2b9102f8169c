package com.lanshan.base.api.feign.user;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.dto.common.TagIdDTO;
import com.lanshan.base.api.dto.user.TagDto;
import com.lanshan.base.api.dto.user.TagUserDto;
import com.lanshan.base.api.feign.user.fallback.TagFeignFallbackFactory;
import com.lanshan.base.api.qo.user.TagSaveQo;
import com.lanshan.base.api.qo.user.TagUsersQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 调用企业微信应用服务的feign client
 * @Author: GaoJian
 * @Date: 2023/10/13
 */
@FeignClient(contextId = "TagFeign", name = ServiceConstant.BASE_SERVICE_NAME,
        path = ServiceConstant.BASE_ADDRESS_BOOK,
        fallbackFactory = TagFeignFallbackFactory.class)
public interface TagFeign {

    @ApiOperation("创建标签")
    @PostMapping(value = "tagCreate")
    Result<TagIdDTO> tagCreate(@RequestParam(name = "corpId") String corpId,
                               @RequestParam(name = "agentId", required = false) String agentId,
                               @RequestBody TagSaveQo qo);

    @ApiOperation("更新标签名字")
    @PostMapping(value = "tagUpdate")
    Result<Object> tagUpdate(@RequestParam(name = "corpId") String corpId,
                             @RequestParam(name = "agentId", required = false) String agentId,
                             @RequestBody TagSaveQo qo);

    @ApiOperation("删除标签")
    @GetMapping(value = "tagDelete")
    Result<Object> tagDelete(@RequestParam(name = "corpId") String corpId,
                             @RequestParam(name = "agentId", required = false) String agentId,
                             @RequestParam(name = "tagId") Long tagId);

    @ApiOperation("获取标签成员")
    @GetMapping(value = "tagGet")
    Result<TagUserDto> tagGet(@RequestParam(name = "corpId") String corpId,
                              @RequestParam(name = "agentId", required = false) String agentId,
                              @RequestParam(name = "tagId") Long tagId);

    @ApiOperation("增加标签成员")
    @PostMapping(value = "addTagUsers")
    Result<TagAddOrRemoveUsersDto> addTagUsers(@RequestParam(name = "corpId") String corpId,
                                               @RequestParam(name = "agentId", required = false) String agentId,
                                               @RequestBody TagUsersQo qo);

    @ApiOperation("删除标签成员")
    @PostMapping(value = "delTagUsers")
    Result<TagAddOrRemoveUsersDto> delTagUsers(@RequestParam(name = "corpId") String corpId,
                                               @RequestParam(name = "agentId", required = false) String agentId,
                                               @RequestBody TagUsersQo qo);

    @ApiOperation("获取标签列表")
    @GetMapping(value = "tagList")
    Result<List<TagDto>> tagList(@RequestParam(name = "corpId") String corpId,
                                 @RequestParam(name = "agentId", required = false) String agentId);

}
