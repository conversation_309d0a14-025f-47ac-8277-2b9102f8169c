package com.lanshan.base.api.qo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "批量用户部门关系")
public class BatchUserDeptQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标签ID列表")
    @NotEmpty(message = "标签ID列表不能为空")
    private List<Long> departmentidList;

    @ApiModelProperty(value = "用户ID列表")
    @NotEmpty(message = "用户ID列表不能为空")
    private List<String> useridList;

    @ApiModelProperty(value = "主部门ID")
    private Long mainDepartmentid;
}
