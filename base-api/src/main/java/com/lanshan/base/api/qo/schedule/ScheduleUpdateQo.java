package com.lanshan.base.api.qo.schedule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "日程qo")
public class ScheduleUpdateQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日程信息")
    private ScheduleSaveQo qo;

    @ApiModelProperty(value = "日程扩展信息")
    private ScheduleExtQo extQo;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "日程扩展信息qo")
    public static class ScheduleExtQo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 是否不更新参与人。0-否；1-是。默认为0
         */
        private Integer skipAttendees;

        /**
         * 操作模式。是重复日程时有效。
         * 0-默认全部修改；
         * 1-仅修改此日程；
         * 2-修改将来的所有日程
         */
        private Integer opMode;

        /**
         * 操作起始时间。仅当操作模式是1或2时有效。该时间必须是重复日程的某一次开始时间
         * 例如：假如日程开始时间start_time为1661990950（2022-09-01 08:09:10），且重复类型是每周，
         * 那么op_start_time可以是：1661990950（2022-09-01 08:09:10）、1662595750（2022-09-08 08:09:10）
         * 、1663200550（2022-09-15 08:09:10）.....
         */
        private Long opStartTime;

    }
}
