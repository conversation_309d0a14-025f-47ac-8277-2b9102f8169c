package com.lanshan.base.api.qo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @auther: YYmmjj
 * @updateTime: 2022/11/17/17:53
 * @description:
 */
@Data
@ApiModel(value = "查询企微部门信息 版本号v2 查询对象")
public class QueryDepartmentsV2Qo {
	@ApiModelProperty("关键词 部门名称")
	private String keyword;

	@ApiModelProperty("部门名称")
	private String name;

	@ApiModelProperty("父部门")
	private List<String> pids;
}
