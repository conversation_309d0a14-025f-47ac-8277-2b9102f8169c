package com.lanshan.base.api.dto.message;

import com.lanshan.base.api.enums.MsgChannelEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@ToString
@ApiModel(value = "通用消息发送DTO")
public class ComMessageSendDTO<T extends BaseMsgBody> implements Serializable {

    private static final long serialVersionUID = 5557250116808864778L;
    @ApiModelProperty(value = "消息类型", required = true)
    private MsgTypeEnum msgTypeEnum;

    @ApiModelProperty(value = "发送渠道", required = true)
    private List<MsgChannelEnum> sendChannelList;

    @ApiModelProperty(value = "各类消息MsgBody")
    private T msgBody;

}
