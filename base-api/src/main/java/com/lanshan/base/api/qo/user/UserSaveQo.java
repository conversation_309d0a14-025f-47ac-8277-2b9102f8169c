package com.lanshan.base.api.qo.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 注意，每个部门下的部门、成员总数不能超过3万个。建议保证创建department对应的部门和创建成员是串行化处理。
 *
 * 仅通讯录同步助手或第三方通讯录应用可调用。
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "保存成员qo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserSaveQo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成员UserID。对应管理端的账号，企业内必须唯一。长度为1~64个字节。只能由数字、字母和“_-@.”四种字符组成，且第一个字符必须是数字或字母。系统进行唯一性检查时会忽略大小写。
     */
    @ApiModelProperty(value = "成员UserID")
    private String userid;

    /**
     * 新成员UserID。对应管理端的账号，企业内必须唯一。长度为1~64个字节。只能由数字、字母和“_-@.”四种字符组成，且第一个字符必须是数字或字母。系统进行唯一性检查时会忽略大小写。
     */
    @ApiModelProperty(value = "新成员UserID")
    private String newUserId;
    /**
     * 成员名称。长度为1~64个utf8字符
     */
    @ApiModelProperty(value = "成员名称")
    private String name;
    /**
     * 成员别名。长度1~64个utf8字符
     */
    @ApiModelProperty(value = "成员别名")
    private String alias;
    /**
     * 成员所属部门id列表，不超过100个
     */
    @ApiModelProperty(value = "成员所属部门id列表")
    private List<Long> department;
    /**
     * 部门内的排序值，默认为0，成员次序以创建时间从小到大排列。个数必须和参数department的个数一致，数值越大排序越前面。有效的值范围是[0, 2^32)
     */
    @ApiModelProperty(value = "部门内的排序值")
    private List<Integer> order;
    /**
     * 职务信息。长度为0~128个字符
     */
    @ApiModelProperty(value = "职务信息")
    private String position;
    /**
     * 手机号码。企业内必须唯一，mobile/email二者不能同时为空
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    /**
     * 性别。1表示男性，2表示女性
     */
    @ApiModelProperty(value = "性别")
    private String gender;
    /**
     * 邮箱。长度6~64个字节，且为有效的email格式。企业内必须唯一，mobile/email二者不能同时为空
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 企业邮箱。仅对开通企业邮箱的企业有效。长度6~64个字节，且为有效的企业邮箱格式。企业内必须唯一。未填写则系统会为用户生成默认企业邮箱（由系统生成的邮箱可修改一次，2022年4月25日之后创建的成员需通过企业管理后台-协作-邮件-邮箱管理-成员邮箱修改）
     */
    @ApiModelProperty(value = "企业邮箱")
    @JsonProperty("biz_mail")
    private String bizMail;
    /**
     * 个数必须和参数department的个数一致，表示在所在的部门内是否为部门负责人。1表示为部门负责人，0表示非部门负责人。在审批(自建、第三方)等应用里可以用来标识上级审批人
     */
    @ApiModelProperty(value = "表示在所在的部门内是否为部门负责人")
    @JsonProperty("is_leader_in_dept")
    private List<Integer> isLeaderInDept;
    /**
     * 直属上级UserID，设置范围为企业内成员，可以设置最多5个上级
     */
    @ApiModelProperty(value = "直属上级UserID")
    @JsonProperty("direct_leader")
    private List<String> directLeader;
    /**
     * 启用/禁用成员。1表示启用成员，0表示禁用成员
     */
    @ApiModelProperty(value = "启用/禁用成员")
    private Integer enable;
    /**
     * 成员头像的mediaid，通过素材管理接口上传图片获得的mediaid
     */
    @ApiModelProperty(value = "成员头像的mediaid")
    @JsonProperty("avatar_mediaid")
    private String avatarMediaid;
    /**
     * 座机。32字节以内，由纯数字、“-”、“+”或“,”组成。
     */
    @ApiModelProperty(value = "座机")
    private String telephone;
    /**
     * 地址。长度最大128个字符
     */
    @ApiModelProperty(value = "地址")
    private String address;
    /**
     * 主部门
     */
    @ApiModelProperty(value = "主部门")
    @JsonProperty("main_department")
    private Long mainDepartment;
    /**
     * 自定义字段。自定义字段需要先在WEB管理端添加，见扩展属性添加方法，否则忽略未知属性的赋值。
     */
    @ApiModelProperty(value = "扩展属性")
    private Extattr extattr;
    /**
     * 是否邀请该成员使用企业微信（将通过微信服务通知或短信或邮件下发邀请，每天自动下发一次，最多持续3个工作日），默认值为true（企业微信那边默认值）
     */
    @ApiModelProperty(value = "是否邀请该成员使用企业微信")
    @JsonProperty("to_invite")
    private Boolean toInvite = Boolean.FALSE;
    /**
     * 对外职务，如果设置了该值，则以此作为对外展示的职务，否则以position来展示。长度12个汉字内
     */
    @ApiModelProperty(value = "对外职务")
    @JsonProperty("external_position")
    private String externalPosition;
    /**
     * 成员对外属性，字段详情见对外属性
     */
    @ApiModelProperty(value = "成员对外属性")
    @JsonProperty("external_profile")
    private ExternalProfile externalProfile;

    @Data
    public static class Extattr implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "属性列表")
        private List<ObjectNode> attrs;
        /**[
            {
                "type": 0,//属性类型: 0-文本 1-网页 2-小程序
                "name": "文本名称",//属性名称： 需要先确保在管理端有创建该属性，否则会忽略
                "text": {//文本类型的属性
                    "value": "文本"//文本属性内容，长度限制64个UTF8字符
                }
            },
            {
                "type": 1,
                "name": "网页名称",
                "web": {//网页类型的属性，url和title字段要么同时为空表示清除该属性，要么同时不为空
                    "url": "http://www.test.com",//网页的url,必须包含http或者https头
                    "title": "标题"//网页的展示标题,长度限制12个UTF8字符
                }
            }
		]*/
    }

    @Data
    public static class ExternalProfile implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "企业简称")
        @JsonProperty("external_corp_name")
        private String externalCorpName;
        @ApiModelProperty(value = "视频号信息")
        @JsonProperty("wechat_channels")
        private WechatChannels wechatChannels;
        @ApiModelProperty(value = "属性列表")
        @JsonProperty("external_attr")
        private List<ObjectNode> externalAttr;

        /**[
            {
                "type": 0,
                "name": "文本名称",
                "text": {
                    "value": "文本"
                }
            },
            {
                "type": 1,
                "name": "网页名称",
                "web": {
                    "url": "http://www.test.com",
                    "title": "标题"
                }
            },
            {
                "type": 2,
                "name": "测试app",
                "miniprogram": {
                    "appid": "wx8bd80126147dFAKE",
                    "pagepath": "/index",
                    "title": "my miniprogram"
                }
            }
		]*/

        @Data
        public static class WechatChannels implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 视频号名字（设置后，成员将对外展示该视频号）。须从企业绑定到企业微信的视频号中选择，可在“我的企业”页中查看绑定的视频号
             */
            @ApiModelProperty(value = "视频号名字")
            private String nickname;
        }
    }

    @ApiModelProperty(value = "操作成功", hidden = true)
    @JsonIgnore
    private Integer isOperateSuccess;
}
