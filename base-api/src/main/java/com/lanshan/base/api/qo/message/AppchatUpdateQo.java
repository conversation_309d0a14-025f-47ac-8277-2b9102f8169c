package com.lanshan.base.api.qo.message;

import com.lanshan.base.api.qo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * chatid所代表的群必须是该应用所创建。
 * 群成员人数不可超过2000人。
 * 每企业变更群的次数不可超过1000次/小时。
 */
@Data
@ApiModel(value = "修改群聊会话 appchat/update")
public class AppchatUpdateQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "群聊id")
    private String chatId;
    @ApiModelProperty(value = "新的群聊名。若不需更新，请忽略此参数。最多50个utf8字符，超过将截断")
    private String name;
    @ApiModelProperty(value = "新群主的id。若不需更新，请忽略此参数")
    private String owner;
    @ApiModelProperty(value = "添加成员的id列表")
    private List<String> addUserList;
    @ApiModelProperty(value = "踢出成员的id列表")
    private List<String> delUserList;
}
