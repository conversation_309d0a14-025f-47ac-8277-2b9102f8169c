package com.lanshan.base.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "企业微信接口响应基类")
public class ResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "返回码")
    private Integer errcode;
    @ApiModelProperty(value = "对返回码的文本描述内容")
    private String errmsg;
}