package com.lanshan.base.api.dto.material;

import com.lanshan.base.api.dto.ResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "企业微信上传多媒体文件接口响应  media/upload")
public class MediaResponseSet extends ResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）")
    private String type;
    @ApiModelProperty(value = "媒体文件上传后获取的唯一标识，3天内有效")
    private String media_id;
    @ApiModelProperty(value = "媒体文件上传时间戳")
    private Long created_at;
}
