package com.lanshan.base.api.qo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel("验证签名入参")
public class CheckSignatureQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("企业微信加密签名")
    private String msg_signature;

    @ApiModelProperty("时间戳")
    private String timestamp;

    @ApiModelProperty("随机数")
    private String nonce;

    @ApiModelProperty("加密的字符串")
    private String echostr;

    @ApiModelProperty(value = "企业ID", hidden = true)
    private String corpId;

    @ApiModelProperty(value = "应用ID", hidden = true)
    private String agentId;
}

