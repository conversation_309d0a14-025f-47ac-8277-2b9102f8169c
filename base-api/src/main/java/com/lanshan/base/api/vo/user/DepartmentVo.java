package com.lanshan.base.api.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DepartmentVo implements Serializable {
    private static final long serialVersionUID = 657019821146346730L;

    private Long id;

    @ApiModelProperty("部门名称，代开发自建应用需要管理员授权才返回；第三方不可获取，需要通过通讯录展示组件来展示部门名称")
    private String name;

    @ApiModelProperty("部门英文名称，代开发自建应用需要管理员授权才返回；第三方不可获取，需要通过通讯录展示组件来展示部门名称")
    private String nameEn;

    @ApiModelProperty("父部门id。根部门为1")
    private Long parentid;

    @ApiModelProperty("在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)")
    private Long order;

    @ApiModelProperty("组织id路径")
    private String path;

    private String idPath;

    @ApiModelProperty("部门负责人的UserID，返回在应用可见范围内的部门负责人列表；第三方仅通讯录应用或者授权了“组织架构信息-应用可获取企业的部门组织架构信息-部门负责人”的第三方应用可获取")
    private List<String> departmentLeader;

    @ApiModelProperty("部门下的总人数（包含所有子部门下的人员）")
    private Integer userCount;

    @ApiModelProperty("部门下的激活人数（包含所有子部门下的人员）")
    private Integer userActiveCount;

    @ApiModelProperty("标签列表")
    private List<TagVo> tagVoList;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class TagVo implements Serializable {
        private static final long serialVersionUID = 1;

        @ApiModelProperty(value = "标签id")
        private Long tagid;

        @ApiModelProperty(value = "标签名称")
        private String tagname;
    }
}

