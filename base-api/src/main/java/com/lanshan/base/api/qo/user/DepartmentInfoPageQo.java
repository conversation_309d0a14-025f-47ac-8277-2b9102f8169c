package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "部门信息分页查询条件（根据实际情况选择传参）")
public class DepartmentInfoPageQo extends PageQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标签id",required = false)
    private Long tagid;
    @ApiModelProperty(value = "部门名称",required = false)
    private String name;
}
