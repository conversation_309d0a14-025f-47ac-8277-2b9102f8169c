package com.lanshan.base.api.dto.user;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "邀请成员")
public class BatchInviteDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "非法成员列表")
    @JsonProperty("invaliduser")
    private List<String> invalidUser;

    @ApiModelProperty(value = "非法部门列表")
    @JsonProperty("invalidparty")
    private List<Long> invalidParty;

    @ApiModelProperty(value = "非法标签列表")
    @JsonProperty("invalidtag")
    private List<Long> invalidTag;
}
