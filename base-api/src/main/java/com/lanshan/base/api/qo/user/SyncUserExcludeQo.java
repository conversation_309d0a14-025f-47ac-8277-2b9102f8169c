package com.lanshan.base.api.qo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "同步用户排除Qo")
public class SyncUserExcludeQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学工号")
    private String userid;

    @ApiModelProperty(value = "姓名")
    private String name;
}
