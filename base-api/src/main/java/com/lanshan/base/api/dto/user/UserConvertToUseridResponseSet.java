package com.lanshan.base.api.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@Data
@ApiModel(value = "openid转userid user/convert_to_userid")
public class UserConvertToUseridResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "该openid在企业微信对应的成员userid")
    private String userId;
}
